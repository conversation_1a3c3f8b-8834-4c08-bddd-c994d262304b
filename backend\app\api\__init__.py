"""
海天AI纳斯达克交易系统 - API模块
基于: 项目手册4.1节MVP版本技术栈配置
创建日期: 2025年7月31日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: API路由和依赖注入管理
"""

from .deps import (
    # 认证依赖
    get_current_user_token,
    get_current_user,
    get_current_active_user,
    require_scopes,
    
    # 数据库依赖
    get_database_session,
    get_async_database_session,
    
    # 配置依赖
    get_settings,
    get_database_settings,
    get_security_settings,
    get_ai_model_settings,
    get_trading_settings,
    get_qmt_settings,
    
    # 日志依赖
    get_logger,
    get_api_logger,
    get_trading_logger,
    get_ai_logger,
    
    # 外部服务依赖
    get_cache_client,
    get_qmt_client,
    get_ai_model_client,
    
    # 上下文依赖
    get_request_id,
    get_user_context
)

__all__ = [
    # 认证依赖
    "get_current_user_token",
    "get_current_user",
    "get_current_active_user",
    "require_scopes",
    
    # 数据库依赖
    "get_database_session",
    "get_async_database_session",
    
    # 配置依赖
    "get_settings",
    "get_database_settings",
    "get_security_settings",
    "get_ai_model_settings",
    "get_trading_settings",
    "get_qmt_settings",
    
    # 日志依赖
    "get_logger",
    "get_api_logger",
    "get_trading_logger",
    "get_ai_logger",
    
    # 外部服务依赖
    "get_cache_client",
    "get_qmt_client",
    "get_ai_model_client",
    
    # 上下文依赖
    "get_request_id",
    "get_user_context"
]
