#!/usr/bin/env python3
"""
海天AI纳斯达克交易系统 - 代码质量工具安装脚本
基于Python 3.13.2 + FastAPI 0.116.1 + Vue.js 3.5.x技术栈
创建日期: 2025年7月13日
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, cwd=None, check=True):
    """执行命令并返回结果"""
    try:
        print(f"执行命令: {command}")
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(f"输出: {result.stdout}")
        return result
    except subprocess.CalledProcessError as e:
        print(f"命令执行失败: {e}")
        if e.stderr:
            print(f"错误: {e.stderr}")
        if check:
            raise
        return e

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    if version.major != 3 or version.minor < 13:
        print(f"❌ 需要Python 3.13+，当前版本: {version.major}.{version.minor}")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def check_node_version():
    """检查Node.js版本"""
    print("🌐 检查Node.js版本...")
    try:
        result = run_command("node --version")
        version = result.stdout.strip()
        print(f"✅ Node.js版本: {version}")
        return True
    except:
        print("❌ Node.js未安装或不在PATH中")
        return False

def setup_backend_tools():
    """设置后端代码质量工具"""
    print("\n🔧 设置后端代码质量工具...")
    
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return False
    
    # 检查是否在conda环境中
    conda_env = os.getenv("CONDA_DEFAULT_ENV")
    if conda_env:
        print(f"✅ 检测到conda环境: {conda_env}")
    else:
        print("⚠️ 未检测到conda环境，建议在AI_Nasdaq_trading环境中运行")
    
    # 安装Python依赖
    print("📦 安装Python代码质量工具...")
    try:
        # 使用poetry安装依赖
        run_command("poetry install --with dev", cwd=backend_dir)
        print("✅ Python依赖安装完成")
    except:
        print("❌ Python依赖安装失败")
        return False
    
    return True

def setup_frontend_tools():
    """设置前端代码质量工具"""
    print("\n🌐 设置前端代码质量工具...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ frontend目录不存在")
        return False
    
    # 安装Node.js依赖
    print("📦 安装前端代码质量工具...")
    try:
        run_command("npm install", cwd=frontend_dir)
        print("✅ 前端依赖安装完成")
    except:
        print("❌ 前端依赖安装失败")
        return False
    
    return True

def setup_pre_commit():
    """设置pre-commit钩子"""
    print("\n🪝 设置pre-commit钩子...")
    
    try:
        # 安装pre-commit钩子
        run_command("pre-commit install")
        print("✅ pre-commit钩子安装完成")
        
        # 运行一次pre-commit检查所有文件
        print("🔍 运行初始代码质量检查...")
        run_command("pre-commit run --all-files", check=False)
        print("✅ 初始代码质量检查完成")
        
    except:
        print("❌ pre-commit钩子安装失败")
        return False
    
    return True

def setup_git_hooks():
    """设置Git钩子"""
    print("\n📝 设置Git钩子...")
    
    git_dir = Path(".git")
    if not git_dir.exists():
        print("❌ 不在Git仓库中")
        return False
    
    # 设置Git配置
    try:
        run_command("git config core.autocrlf false")
        run_command("git config core.eol lf")
        print("✅ Git配置完成")
    except:
        print("❌ Git配置失败")
        return False
    
    return True

def verify_tools():
    """验证工具安装"""
    print("\n✅ 验证代码质量工具...")
    
    tools_status = {}
    
    # 验证Python工具
    python_tools = ["black", "isort", "flake8", "mypy", "bandit", "pre-commit"]
    for tool in python_tools:
        try:
            run_command(f"{tool} --version")
            tools_status[tool] = "✅"
        except:
            tools_status[tool] = "❌"
    
    # 验证前端工具
    frontend_dir = Path("frontend")
    if frontend_dir.exists():
        frontend_tools = ["eslint", "prettier", "stylelint"]
        for tool in frontend_tools:
            try:
                run_command(f"npx {tool} --version", cwd=frontend_dir)
                tools_status[tool] = "✅"
            except:
                tools_status[tool] = "❌"
    
    # 输出验证结果
    print("\n📊 工具安装状态:")
    for tool, status in tools_status.items():
        print(f"  {status} {tool}")
    
    # 检查是否所有工具都安装成功
    failed_tools = [tool for tool, status in tools_status.items() if status == "❌"]
    if failed_tools:
        print(f"\n❌ 以下工具安装失败: {', '.join(failed_tools)}")
        return False
    
    print("\n🎉 所有代码质量工具安装成功!")
    return True

def main():
    """主函数"""
    print("🚀 海天AI纳斯达克交易系统 - 代码质量工具安装")
    print("=" * 60)
    
    # 检查环境
    if not check_python_version():
        sys.exit(1)
    
    if not check_node_version():
        sys.exit(1)
    
    # 设置工具
    success = True
    
    if not setup_backend_tools():
        success = False
    
    if not setup_frontend_tools():
        success = False
    
    if not setup_pre_commit():
        success = False
    
    if not setup_git_hooks():
        success = False
    
    # 验证安装
    if success and verify_tools():
        print("\n🎉 代码质量工具安装完成!")
        print("\n📝 使用说明:")
        print("  - 提交代码时会自动运行代码质量检查")
        print("  - 手动运行: pre-commit run --all-files")
        print("  - 后端格式化: cd backend && poetry run black .")
        print("  - 前端格式化: cd frontend && npm run format")
        print("  - 前端检查: cd frontend && npm run lint")
        return 0
    else:
        print("\n❌ 代码质量工具安装失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
