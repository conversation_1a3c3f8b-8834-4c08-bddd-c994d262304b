[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "ai-nasdaq-trading"
version = "1.0.0"
description = "海天AI纳斯达克交易系统 - 基于多AI模型的智能量化交易平台"
authors = ["海天AI量化交易开发团队 <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]

# 技术栈版本说明 (更新于2025-07-30)
# Python 3.13.2 + FastAPI 0.116.1 + PostgreSQL 17.5 + Redis 5.2.x
# 前端: Vue.js 3.5.x + TypeScript 5.7.x + Vite 6.0.x + Vitest 3.2.x

[tool.poetry.dependencies]
python = "^3.13.2"

# Web框架和服务器
fastapi = "0.116.1"
uvicorn = {extras = ["standard"], version = "^0.32.1"}
python-multipart = "^0.0.12"

# 数据库和ORM
sqlalchemy = "^2.0.36"
alembic = "^1.14.0"
asyncpg = "^0.29.0"
psycopg2-binary = "^2.9.10"

# 数据验证和序列化
pydantic = "^2.10.3"
pydantic-settings = "^2.7.0"

# 缓存和消息队列
redis = "^5.2.0"
celery = "^5.4.0"

# AI模型API客户端
openai = "^1.58.1"
anthropic = "^0.40.0"

# 数据处理和分析
pandas = "^2.2.3"
numpy = "^2.2.1"

# 安全和认证
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-decouple = "^3.8"

# HTTP客户端
httpx = "^0.28.1"
aiohttp = "^3.11.10"

# 日志和监控
loguru = "^0.7.3"

# 工具库
python-dateutil = "^2.9.0"
pytz = "^2024.2"

[tool.poetry.group.dev.dependencies]
# 测试框架
pytest = "^8.3.3"
pytest-asyncio = "^0.24.0"
pytest-cov = "^6.0.0"
pytest-mock = "^3.14.0"

# 代码质量
black = "^24.10.0"
isort = "^5.13.2"
flake8 = "^7.1.1"
mypy = "^1.14.0"
bandit = "^1.7.10"

# 开发工具
pre-commit = "^4.0.1"
ipython = "^8.30.0"

[tool.black]
line-length = 88
target-version = ['py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  migrations
  | venv
  | env
  | .venv
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "celery.*",
    "redis.*",
    "asyncpg.*",
    "psycopg2.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "ai: marks tests that require AI model access",
]

[tool.coverage.run]
source = ["app"]
omit = [
    "*/tests/*",
    "*/migrations/*",
    "*/venv/*",
    "*/env/*",
    "*/.venv/*",
]

[tool.bandit]
exclude_dirs = ["tests", "migrations", "alembic"]
skips = ["B101"]  # 允许assert语句在开发环境
severity = "medium"
confidence = "medium"

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
