# 海天AI交易系统容器化部署与使用手册

## 文档信息
- **创建日期**: 2025年7月12日
- **最后更新**: 2025年7月13日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: 海天AI纳斯达克交易系统的容器化部署和运维管理
- **技术基础**: Docker + Docker Compose + Vue.js 3.5.x + FastAPI 0.116.1 + PostgreSQL 17.5
- **部署环境**: 开发环境与生产环境统一配置

*本手册提供海天AI交易系统的完整容器化部署方案，实现开发和生产环境的统一管理。*

## 🚀 当前部署状态

**✅ 已成功部署**: PostgreSQL 17.5 + FastAPI 0.116.1 后端服务
**⏳ 待部署**: Vue.js 3.5.x 前端服务 + Nginx 反向代理
**🔗 可访问**: http://localhost:8001/ (API服务) + http://localhost:5433 (数据库)

---

## 版本更新记录
- v1.0: 基于Docker Compose的统一容器化部署方案（支持开发和生产环境）
- v1.1: 更新实际部署配置，修正端口映射和API端点信息（2025年7月13日）

## 目录

1. [系统要求](#1-系统要求)
2. [环境准备](#2-环境准备)
3. [快速部署](#3-快速部署)
4. [服务管理](#4-服务管理)
5. [配置管理](#5-配置管理)
6. [数据管理](#6-数据管理)
7. [监控运维](#7-监控运维)
8. [故障排查](#8-故障排查)
9. [安全配置](#9-安全配置)
10. [性能优化](#10-性能优化)

---

# 1. 系统要求

## 1.1 硬件要求

### 最低配置
- **CPU**: 4核心 2.0GHz
- **内存**: 8GB RAM
- **存储**: 50GB 可用空间
- **网络**: 稳定的互联网连接（用于AI模型调用）

### 推荐配置
- **CPU**: 8核心 3.0GHz
- **内存**: 16GB RAM
- **存储**: 100GB SSD
- **网络**: 100Mbps 带宽

### 生产环境配置
- **CPU**: 16核心 3.5GHz
- **内存**: 32GB RAM
- **存储**: 500GB NVMe SSD
- **网络**: 1Gbps 带宽

## 1.2 软件要求

### 必需软件
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 20.04+
- **Docker**: 24.0+ 版本
- **Docker Compose**: 2.0+ 版本
- **Git**: 2.30+ 版本

### 可选软件
- **VS Code**: 推荐IDE（Vue官方插件支持）
- **Postman**: API测试工具
- **pgAdmin**: 数据库管理工具

# 2. 环境准备

## 2.1 Docker安装

### Windows安装
```bash
# 下载Docker Desktop for Windows
# 访问: https://www.docker.com/products/docker-desktop/
# 安装后重启系统

# 验证安装
docker --version
docker-compose --version
```

### macOS安装
```bash
# 使用Homebrew安装
brew install --cask docker

# 或下载Docker Desktop for Mac
# 访问: https://www.docker.com/products/docker-desktop/

# 验证安装
docker --version
docker-compose --version
```

### Linux安装
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 添加用户到docker组
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

## 2.2 项目获取

### 克隆项目
```bash
# 克隆项目仓库
git clone https://github.com/your-repo/ai-nasdaq-trading.git
cd ai-nasdaq-trading

# 查看项目结构
ls -la
```

### 项目结构
```
AI_Nasdaq_trading/
├── infrastructure/             # 容器化部署配置
│   ├── docker-compose.yml     # Docker编排配置
│   ├── .env                    # 环境变量配置
│   ├── nginx/                  # Nginx配置文件
│   └── postgres/               # PostgreSQL配置文件
├── frontend/                   # Vue.js 3.5.x前端代码
│   ├── Dockerfile
│   └── package.json
├── backend/                    # FastAPI 0.116.1后端代码
│   ├── Dockerfile
│   ├── requirements.txt
│   ├── app/                    # 应用代码
│   │   ├── main.py            # FastAPI主应用
│   │   ├── api/               # API路由
│   │   ├── core/              # 核心配置
│   │   ├── models/            # 数据模型
│   │   ├── schemas/           # Pydantic模式
│   │   ├── ai-traders/        # AI交易员模块
│   │   ├── trading-engine/    # 交易引擎
│   │   ├── supervisor/        # 监督模块
│   │   └── shared/            # 共享组件
│   ├── tests/                 # 测试代码
│   └── alembic/               # 数据库迁移
├── data/                      # 数据存储目录
│   ├── ai_traders/            # AI交易员档案
│   └── logs/                  # 日志文件
├── uploads/                   # 文件上传目录
└── 项目文档/                   # 项目文档
    └── 标准文档/
```

# 3. 快速部署

## 3.1 环境配置

### 创建环境变量文件
```bash
# 进入infrastructure目录
cd infrastructure

# 环境变量文件已存在，可直接编辑
nano .env
```

### 环境变量配置示例
```bash
# =============================================================================
# 应用基础配置
# =============================================================================
APP_NAME=AI_Nasdaq_Trading
APP_VERSION=1.0.0
ENVIRONMENT=production
DEBUG=true
LOG_LEVEL=INFO

# =============================================================================
# 数据库配置 (PostgreSQL 17.5)
# =============================================================================
POSTGRES_HOST=db
POSTGRES_PORT=5433                        # 避免与本地PostgreSQL冲突
POSTGRES_DB=ai_nasdaq_trading
POSTGRES_USER=postgres
POSTGRES_PASSWORD=ai_trading_123
APP_DB_USER=ai_trading_app
APP_DB_PASSWORD=ai_trading_app_123
DATABASE_URL=postgresql+asyncpg://ai_trading_app:ai_trading_app_123@db:5433/ai_nasdaq_trading

# =============================================================================
# 前端服务配置 (Vue.js 3.5.x)
# =============================================================================
FRONTEND_PORT=3000
VITE_API_BASE_URL=http://localhost:8001   # 前端API基础URL

# =============================================================================
# 后端服务配置 (FastAPI 0.116.1)
# =============================================================================
BACKEND_PORT=8001                         # 后端服务端口，避免与其他服务冲突
BACKEND_START_COMMAND=uvicorn app.main:app --reload --host 0.0.0.0 --port 8001

# =============================================================================
# AI模型API配置
# =============================================================================
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
GOOGLE_API_KEY=your_google_api_key

# =============================================================================
# QMT量化交易接口配置
# =============================================================================
QMT_HOST=localhost
QMT_PORT=58610
QMT_USERNAME=your_qmt_username
QMT_PASSWORD=your_qmt_password

# =============================================================================
# AI交易系统特定配置
# =============================================================================
AI_TRADERS_COUNT=8                        # AI交易员数量
MAX_FUND_PER_TRADER=100000                # 每个AI交易员最大资金额度
GLOBAL_DAILY_TRADE_LIMIT=50               # 全局每日交易次数限制
```

## 3.2 一键部署

### 统一环境部署
```bash
# 进入infrastructure目录
cd infrastructure

# 启动所有服务
docker compose up -d

# 查看服务状态
docker compose ps

# 查看实时日志
docker compose logs -f

# 启动指定服务
docker compose up -d db backend

# 重新构建并启动
docker compose up -d --build
```

### 分步部署（推荐）
```bash
# 1. 首先启动数据库
docker compose up -d db

# 2. 等待数据库健康检查通过
docker compose ps

# 3. 启动后端服务
docker compose up -d backend

# 4. 启动前端服务
docker compose up -d frontend

# 5. 启动Nginx代理（可选）
docker compose up -d nginx
```

## 3.3 服务访问地址

### 当前部署环境
- **根路径**: http://localhost:8001/
- **健康检查**: http://localhost:8001/health
- **API文档**: http://localhost:8001/docs
- **ReDoc文档**: http://localhost:8001/redoc
- **API信息**: http://localhost:8001/api/v1/info
- **数据库**: localhost:5433 (PostgreSQL 17.5)

### 前端服务（待部署）
- **前端界面**: http://localhost:3000
- **前端开发服务器**: http://localhost:3000

### 生产环境（配置示例）
- **前端界面**: http://your-domain.com
- **API文档**: http://your-domain.com/api/docs
- **数据库管理**: http://your-domain.com:5050 (仅内网访问)
- **API接口**: http://your-domain.com/api/v1

## 3.4 初始化验证

### 健康检查
```bash
# 检查所有服务状态
docker compose ps

# 检查后端API根路径
curl http://localhost:8001/

# 检查后端健康状态
curl http://localhost:8001/health

# 检查API信息
curl http://localhost:8001/api/v1/info

# 检查数据库连接
docker compose exec db pg_isready -U postgres

# 检查后端容器状态
docker compose logs backend --tail=20
```

### 初始数据导入
```bash
# 执行数据库初始化（待开发）
docker compose exec backend python scripts/init_database.py

# 创建默认AI交易员（待开发）
docker compose exec backend python scripts/create_default_traders.py

# 验证数据导入
docker compose exec db psql -U postgres -d ai_nasdaq_trading -c "SELECT version();"

# 检查数据库表结构（使用Alembic迁移）
docker compose exec backend alembic current
```

## 3.5 当前部署状态

### 已成功部署的服务

| 服务名称 | 镜像版本 | 状态 | 端口映射 | 健康状态 |
|---------|---------|------|----------|----------|
| **PostgreSQL** | postgres:17.5-alpine | ✅ 运行中 | 5433:5432 | ✅ 健康 |
| **FastAPI后端** | infrastructure-backend | ✅ 运行中 | 8001:8001 | ✅ 健康 |
| **前端服务** | - | ⏳ 待部署 | 3000:3000 | - |
| **Nginx代理** | - | ⏳ 待部署 | 80:80 | - |

### 可访问的API端点详情

#### 1. 根路径 - 系统信息
```bash
curl http://localhost:8001/
```
**响应示例**:
```json
{
  "message": "海天AI纳斯达克交易系统",
  "version": "1.0.0",
  "status": "running",
  "timestamp": "2025-07-13T11:59:08.057576",
  "environment": "production",
  "docs": "/docs",
  "redoc": "/redoc"
}
```

#### 2. 健康检查 - 服务状态
```bash
curl http://localhost:8001/health
```
**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-13T11:59:16.241338",
  "services": {
    "api": "running",
    "database": "checking...",
    "ai_traders": "initializing",
    "trading_engine": "standby"
  },
  "version": "1.0.0"
}
```

#### 3. API文档 - Swagger UI
- **访问地址**: http://localhost:8001/docs
- **功能**: 交互式API文档，支持在线测试

#### 4. ReDoc文档 - 美化文档
- **访问地址**: http://localhost:8001/redoc
- **功能**: 美观的API文档展示

#### 5. API信息 - 功能概览
```bash
curl http://localhost:8001/api/v1/info
```
**响应示例**:
```json
{
  "api_version": "v1",
  "features": [
    "AI交易员管理",
    "实时行情数据",
    "交易执行引擎",
    "风险控制系统",
    "性能监控"
  ],
  "endpoints": {
    "traders": "/api/v1/traders",
    "trading": "/api/v1/trading",
    "market_data": "/api/v1/market",
    "monitoring": "/api/v1/monitoring"
  }
}
```

### 技术栈信息
- **Python**: 3.13.2
- **FastAPI**: 0.116.1
- **PostgreSQL**: 17.5
- **Docker**: 最新版本
- **Docker Compose**: 2.0+

# 4. 服务管理

## 4.1 基本操作

### 启动服务
```bash
# 启动所有服务
docker compose up -d

# 启动指定服务
docker compose up -d frontend backend

# 查看启动日志
docker compose up
```

### 停止服务
```bash
# 停止所有服务
docker compose down

# 停止指定服务
docker compose stop frontend

# 强制停止
docker compose kill
```

### 重启服务
```bash
# 重启所有服务
docker compose restart

# 重启指定服务
docker compose restart backend

# 重建并重启
docker compose up -d --build
```

## 4.2 服务扩缩容

### 扩展服务实例
```bash
# 扩展后端服务到3个实例
docker compose up -d --scale backend=3

# 扩展前端服务到2个实例
docker compose up -d --scale frontend=2

# 查看扩展后的服务
docker compose ps
```

### 资源限制
```yaml
# docker-compose.yml中的资源限制配置
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
```

## 4.3 日志管理

### 查看日志
```bash
# 查看所有服务日志
docker compose logs

# 查看指定服务日志
docker compose logs backend

# 实时跟踪日志
docker compose logs -f

# 查看最近100行日志
docker compose logs --tail=100

# 查看特定时间段日志
docker compose logs --since="2025-07-12T10:00:00"
```

### 日志配置
```yaml
# docker-compose.yml中的日志配置
services:
  backend:
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
        max-file: "5"
```

# 5. 配置管理

## 5.1 环境变量管理

### 开发环境配置
```bash
# .env.dev
DEBUG=true
LOG_LEVEL=DEBUG
POSTGRES_PASSWORD=dev_password
FRONTEND_PORT=3000
BACKEND_PORT=8000
```

### 生产环境配置
```bash
# .env.prod
DEBUG=false
LOG_LEVEL=INFO
POSTGRES_PASSWORD=secure_production_password
FRONTEND_PORT=80
BACKEND_PORT=8000
```

### 配置文件切换
```bash
# 当前使用统一配置（开发和生产环境统一）
# 直接编辑.env文件进行配置调整
nano .env
docker compose up -d

# 如需不同环境配置，可创建多个环境文件
cp .env .env.backup
# 编辑配置后重启服务
docker compose restart
```

## 5.2 AI交易员配置

### 交易员数量配置
```bash
# 在.env文件中配置
AI_TRADERS_COUNT=8
AI_TRADERS_CONFIG_FILE=config/traders.json
```

### 交易员参数配置文件
```json
{
  "traders": [
    {
      "id": "AI-Trader-01",
      "model": "gpt-4",
      "fund_ratio": 0.15,
      "risk_level": "medium",
      "max_position_ratio": 0.08,
      "daily_trade_limit": 10
    },
    {
      "id": "AI-Trader-02",
      "model": "claude-3",
      "fund_ratio": 0.12,
      "risk_level": "conservative",
      "max_position_ratio": 0.06,
      "daily_trade_limit": 8
    }
  ]
}
```

## 5.3 QMT接口配置

### QMT连接配置
```bash
# QMT服务配置
QMT_HOST=*************
QMT_PORT=58610
QMT_USERNAME=your_username
QMT_PASSWORD=your_password
QMT_TIMEOUT=30
QMT_RETRY_COUNT=3
```

### QMT健康检查
```bash
# 检查QMT连接（待开发）
docker compose exec backend python scripts/check_qmt_connection.py

# 测试QMT API（待开发）
docker compose exec backend python scripts/test_qmt_api.py
```

# 6. 数据管理

## 6.1 数据库备份

### 自动备份脚本
```bash
#!/bin/bash
# scripts/backup.sh

BACKUP_DIR="/backup"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="ai_trading_backup_${DATE}.sql"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行数据库备份
docker compose exec -T db pg_dump -U postgres ai_nasdaq_trading > "${BACKUP_DIR}/${BACKUP_FILE}"

# 压缩备份文件
gzip "${BACKUP_DIR}/${BACKUP_FILE}"

# 删除7天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "Backup completed: ${BACKUP_FILE}.gz"
```

### 手动备份
```bash
# 备份数据库
docker compose exec db pg_dump -U postgres ai_nasdaq_trading > backup_$(date +%Y%m%d).sql

# 备份AI档案数据（待开发）
docker compose exec backend python scripts/backup_ai_profiles.py

# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d).tar.gz infrastructure/
```

## 6.2 数据恢复

### 数据库恢复
```bash
# 恢复数据库
docker-compose exec -T postgres psql -U postgres ai_trading < backup_20250712.sql

# 恢复AI档案
docker-compose exec backend python scripts/restore_ai_profiles.py backup_20250712_profiles.json

# 验证恢复
docker-compose exec postgres psql -U postgres -d ai_trading -c "SELECT COUNT(*) FROM ai_traders;"
```

### 灾难恢复流程
```bash
# 1. 停止所有服务
docker-compose down

# 2. 清理数据卷
docker volume rm ai-nasdaq-trading_postgres_data

# 3. 重新启动服务
docker-compose up -d

# 4. 等待数据库初始化完成
sleep 30

# 5. 恢复数据
docker-compose exec -T postgres psql -U postgres ai_trading < latest_backup.sql

# 6. 验证恢复
docker-compose exec backend python scripts/verify_data_integrity.py
```

## 6.3 数据清理

### 定期清理脚本
```bash
#!/bin/bash
# scripts/cleanup.sh

# 清理过期的交易记录（保留30天）
docker-compose exec postgres psql -U postgres -d ai_trading -c "
DELETE FROM trading_records
WHERE created_at < NOW() - INTERVAL '30 days';"

# 清理过期的市场数据（保留7天）
docker-compose exec postgres psql -U postgres -d ai_trading -c "
DELETE FROM market_data
WHERE timestamp < NOW() - INTERVAL '7 days';"

# 清理过期的日志文件
find logs/ -name "*.log" -mtime +7 -delete

echo "Data cleanup completed"
```

# 7. 监控运维

## 7.1 健康检查

### 服务健康检查
```bash
# 检查所有容器状态
docker-compose ps

# 检查容器健康状态
docker-compose exec backend curl http://localhost:8000/health

# 检查数据库连接
docker-compose exec postgres pg_isready -U postgres

# 检查前端服务
curl -I http://localhost:3000
```

### 自动健康检查脚本
```bash
#!/bin/bash
# scripts/health_check.sh

SERVICES=("frontend" "backend" "postgres")
FAILED_SERVICES=()

for service in "${SERVICES[@]}"; do
    if ! docker-compose ps $service | grep -q "Up"; then
        FAILED_SERVICES+=($service)
    fi
done

if [ ${#FAILED_SERVICES[@]} -eq 0 ]; then
    echo "All services are healthy"
    exit 0
else
    echo "Failed services: ${FAILED_SERVICES[*]}"
    exit 1
fi
```

## 7.2 性能监控

### 资源使用监控
```bash
# 查看容器资源使用
docker stats

# 查看特定容器资源使用
docker stats ai-nasdaq-trading_backend_1

# 查看磁盘使用
docker system df

# 查看网络使用
docker network ls
```

### 监控指标收集
```yaml
# docker-compose.monitoring.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

## 7.3 日志分析

### 错误日志分析
```bash
# 查找错误日志
docker-compose logs | grep -i error

# 统计错误频率
docker-compose logs --since="1h" | grep -i error | wc -l

# 分析API响应时间
docker-compose logs backend | grep "response_time" | tail -100
```

### 日志聚合配置
```yaml
# docker-compose.logging.yml
version: '3.8'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
    environment:
      - discovery.type=single-node
    ports:
      - "9200:9200"

  kibana:
    image: docker.elastic.co/kibana/kibana:7.15.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
```

# 8. 故障排查

## 8.1 常见问题

### 容器启动失败
```bash
# 问题：容器无法启动
# 解决方案：
# 1. 检查端口占用
netstat -tulpn | grep :3000

# 2. 检查Docker服务状态
sudo systemctl status docker

# 3. 重新构建镜像
docker-compose build --no-cache

# 4. 清理Docker缓存
docker system prune -a
```

### 数据库连接失败
```bash
# 问题：无法连接数据库
# 解决方案：
# 1. 检查数据库容器状态
docker-compose ps postgres

# 2. 检查数据库日志
docker-compose logs postgres

# 3. 验证连接参数
docker-compose exec postgres psql -U postgres -d ai_trading -c "SELECT 1;"

# 4. 重启数据库服务
docker-compose restart postgres
```

### AI模型调用失败
```bash
# 问题：AI模型API调用失败
# 解决方案：
# 1. 检查API密钥配置
docker-compose exec backend python -c "import os; print(os.getenv('OPENAI_API_KEY'))"

# 2. 测试网络连接
docker-compose exec backend curl https://api.openai.com/v1/models

# 3. 检查API配额
docker-compose exec backend python scripts/check_api_quota.py

# 4. 切换备用模型
# 修改.env文件中的模型配置
```

## 8.2 性能问题

### 内存不足
```bash
# 问题：系统内存不足
# 解决方案：
# 1. 检查内存使用
docker stats --no-stream

# 2. 增加交换空间
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 3. 优化容器内存限制
# 在docker-compose.yml中添加内存限制
```

### 磁盘空间不足
```bash
# 问题：磁盘空间不足
# 解决方案：
# 1. 清理Docker镜像和容器
docker system prune -a

# 2. 清理日志文件
docker-compose exec backend find /app/logs -name "*.log" -mtime +7 -delete

# 3. 清理数据库
docker-compose exec backend python scripts/cleanup_old_data.py
```

## 8.3 调试技巧

### 进入容器调试
```bash
# 进入后端容器
docker-compose exec backend bash

# 进入数据库容器
docker-compose exec postgres psql -U postgres ai_trading

# 查看容器文件系统
docker-compose exec backend ls -la /app

# 检查环境变量
docker-compose exec backend env
```

### 网络调试
```bash
# 检查容器网络
docker network ls

# 检查容器IP地址
docker-compose exec backend hostname -I

# 测试容器间连接
docker-compose exec backend ping postgres

# 检查端口监听
docker-compose exec backend netstat -tulpn
```

# 9. 安全配置

## 9.1 网络安全

### 防火墙配置
```bash
# Ubuntu/Debian防火墙配置
sudo ufw enable
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 3000/tcp  # 前端（仅开发环境）
sudo ufw allow 8000/tcp  # API（仅开发环境）

# 生产环境只开放必要端口
sudo ufw deny 3000/tcp
sudo ufw deny 8000/tcp
sudo ufw deny 5432/tcp  # PostgreSQL
sudo ufw deny 5050/tcp  # pgAdmin
```

### SSL/TLS配置
```yaml
# docker-compose.prod.yml中的SSL配置
services:
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
```

### Nginx SSL配置
```nginx
# nginx/nginx.conf
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    location / {
        proxy_pass http://frontend:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /api/ {
        proxy_pass http://backend:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 9.2 数据安全

### 数据库安全配置
```bash
# 强密码策略
POSTGRES_PASSWORD=$(openssl rand -base64 32)

# 数据库访问限制
# 在docker-compose.yml中配置
services:
  postgres:
    environment:
      - POSTGRES_HOST_AUTH_METHOD=md5
    networks:
      - internal
```

### 敏感数据加密
```bash
# 使用Docker Secrets管理敏感信息
echo "your_secret_password" | docker secret create postgres_password -

# 在docker-compose.yml中使用secrets
services:
  postgres:
    secrets:
      - postgres_password
    environment:
      - POSTGRES_PASSWORD_FILE=/run/secrets/postgres_password
```

### API密钥管理
```bash
# 使用环境变量文件管理API密钥
# .env.secrets（不提交到版本控制）
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
ANTHROPIC_API_KEY=sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
GOOGLE_API_KEY=AIzaxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# 设置文件权限
chmod 600 .env.secrets
```

## 9.3 访问控制

### 用户认证配置
```python
# backend/app/auth.py
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

security = HTTPBearer()

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    if not verify_jwt_token(credentials.credentials):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
    return credentials.credentials
```

### API访问限制
```python
# backend/app/middleware.py
from fastapi import Request
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

@limiter.limit("100/minute")
async def api_endpoint(request: Request):
    return {"message": "API response"}
```

# 10. 性能优化

## 10.1 容器优化

### 镜像优化
```dockerfile
# backend/Dockerfile优化版本
FROM python:3.13.2-slim as builder

# 安装依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt

FROM python:3.13.2-slim

# 复制已安装的包
COPY --from=builder /root/.local /root/.local

# 设置环境变量
ENV PATH=/root/.local/bin:$PATH
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

WORKDIR /app
COPY . .

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 资源限制优化
```yaml
# docker-compose.yml中的资源优化
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

## 10.2 数据库优化

### PostgreSQL性能调优
```sql
-- postgresql.conf优化配置
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
```

### 数据库连接池配置
```python
# backend/app/database.py
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

engine = create_engine(
    DATABASE_URL,
    poolclass=QueuePool,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600
)
```

## 10.3 缓存优化

### Redis缓存配置
```yaml
# docker-compose.yml中添加Redis
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
```

### 应用层缓存
```python
# backend/app/cache.py
import redis
from functools import wraps

redis_client = redis.Redis(host='redis', port=6379, db=0)

def cache_result(expiration=300):
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            cached_result = redis_client.get(cache_key)

            if cached_result:
                return json.loads(cached_result)

            result = await func(*args, **kwargs)
            redis_client.setex(cache_key, expiration, json.dumps(result))
            return result
        return wrapper
    return decorator
```

## 10.4 监控和告警

### Prometheus监控配置
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ai-trading-backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'
    scrape_interval: 5s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
```

### 告警规则配置
```yaml
# monitoring/alert.rules.yml
groups:
  - name: ai-trading-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"

      - alert: DatabaseDown
        expr: up{job="postgres"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database is down"
```

---

## 附录

### A. 常用命令速查表

```bash
# 服务管理
docker compose up -d                    # 启动所有服务
docker compose down                     # 停止所有服务
docker compose restart                  # 重启所有服务
docker compose ps                       # 查看服务状态
docker compose logs -f                  # 查看实时日志

# API测试
curl http://localhost:8001/             # 测试根路径
curl http://localhost:8001/health       # 健康检查
curl http://localhost:8001/docs         # API文档

# 数据管理
docker compose exec db pg_dump -U postgres ai_nasdaq_trading > backup.sql
docker compose exec -T db psql -U postgres ai_nasdaq_trading < backup.sql

# 调试命令
docker compose exec backend bash       # 进入后端容器
docker compose exec db psql -U postgres ai_nasdaq_trading  # 进入数据库
docker stats                           # 查看资源使用
```

### B. 故障排查检查清单

- [ ] 检查Docker服务状态
- [ ] 验证环境变量配置
- [ ] 检查端口占用情况
- [ ] 验证网络连接
- [ ] 检查磁盘空间
- [ ] 查看容器日志
- [ ] 验证数据库连接
- [ ] 检查API密钥配置
- [ ] 验证QMT连接
- [ ] 检查防火墙设置

### C. 联系信息

- **技术支持**: <EMAIL>
- **文档更新**: <EMAIL>
- **紧急联系**: <EMAIL>

---

**许可证**
本文档采用商业闭源许可证。
