# Docker容器配置文档

## 文档信息

| 项目 | 内容 |
|------|------|
| 创建日期 | 2025年7月13日 |
| 维护团队 | 海天AI量化交易开发团队 |
| 文档范围 | 容器化环境搭建 - Docker配置说明 |
| 技术基础 | Docker + Docker Compose + 多阶段构建 |

## 版本更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 1.0.0 | 2025-07-13 | 初始版本，完整容器配置 | 开发团队 |

## 目录

- [1. 容器架构概述](#1-容器架构概述)
- [2. 后端容器配置](#2-后端容器配置)
- [3. 前端容器配置](#3-前端容器配置)
- [4. Nginx容器配置](#4-nginx容器配置)
- [5. 数据库容器配置](#5-数据库容器配置)
- [6. Redis容器配置](#6-redis容器配置)
- [7. 网络和存储配置](#7-网络和存储配置)
- [8. 环境差异配置](#8-环境差异配置)

## 1. 容器架构概述

### 1.1 整体架构

海天AI纳斯达克交易系统采用微服务容器化架构，包含以下核心服务：

| 服务名称 | 容器镜像 | 主要功能 | 端口映射 |
|----------|----------|----------|----------|
| nginx | 自构建 | 反向代理、负载均衡、SSL终端 | 80:80, 443:443 |
| frontend | 自构建 | Vue.js前端应用 | 3000:3000 |
| backend | 自构建 | FastAPI后端服务 | 8000:8000 |
| db | postgres:17.5-alpine | PostgreSQL数据库 | 5432:5432 |

### 1.2 技术栈版本

| 组件 | 版本 | 说明 |
|------|------|------|
| Docker | 24.0+ | 容器运行时 |
| Docker Compose | 2.20+ | 容器编排工具 |
| Python | 3.13.2 | 后端运行时 |
| Node.js | 20.x | 前端构建环境 |
| Nginx | 1.25 | Web服务器 |
| PostgreSQL | 17.5 | 关系型数据库 |

## 2. 后端容器配置

### 2.1 Dockerfile结构

后端采用多阶段构建策略，分为构建阶段和运行阶段：

#### 构建阶段特点
- 基于Python 3.13.2-slim镜像
- 安装编译工具和系统依赖
- 安装Python依赖包
- 优化构建缓存

#### 运行阶段特点
- 最小化运行环境
- 非root用户运行
- 健康检查配置
- 安全权限设置

### 2.2 关键配置参数

| 配置项 | 开发环境 | 生产环境 | 说明 |
|--------|----------|----------|------|
| 内存限制 | 1G | 2G | 容器最大内存 |
| CPU限制 | 1.0 | 2.0 | 容器最大CPU |
| 工作进程 | 1 | 4 | Uvicorn工作进程数 |
| 健康检查间隔 | 30s | 30s | 健康检查频率 |

### 2.3 环境变量配置

```bash
# 应用配置
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO

# 数据库连接
DATABASE_URL=******************************/dbname

# 缓存配置 (MVP版本使用内存缓存)
CACHE_TYPE=memory

# 安全配置
SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-secret
```

## 3. 前端容器配置

### 3.1 多阶段构建策略

前端容器支持开发和生产两种模式：

#### 开发模式
- 基于Node.js 20-alpine
- 支持热重载
- 开发依赖完整安装
- 端口3000暴露

#### 生产模式
- 构建阶段：Node.js编译
- 运行阶段：Nginx静态服务
- 资源压缩和优化
- 缓存策略配置

### 3.2 Nginx静态服务配置

| 配置项 | 值 | 说明 |
|--------|-----|------|
| 根目录 | /usr/share/nginx/html | 静态文件目录 |
| 索引文件 | index.html | 默认首页 |
| Gzip压缩 | 启用 | 减少传输大小 |
| 缓存策略 | 1年 | 静态资源缓存 |
| SPA路由 | 支持 | Vue Router支持 |

## 4. Nginx容器配置

### 4.1 反向代理配置

Nginx作为系统网关，提供以下功能：

#### 核心功能
- 前端静态文件服务
- API请求反向代理
- WebSocket连接代理
- SSL终端和安全头设置
- 请求限流和安全防护

#### 上游服务配置
```nginx
upstream backend_api {
    server backend:8000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream frontend_app {
    server frontend:3000 max_fails=3 fail_timeout=30s;
    keepalive 32;
}
```

### 4.2 性能优化配置

| 配置项 | 值 | 说明 |
|--------|-----|------|
| worker_processes | auto | 自动检测CPU核心数 |
| worker_connections | 1024 | 每进程最大连接数 |
| keepalive_timeout | 65s | 连接保持时间 |
| client_max_body_size | 100M | 最大请求体大小 |
| gzip_comp_level | 6 | Gzip压缩级别 |

## 5. 数据库容器配置

### 5.1 PostgreSQL配置

#### 基础配置
- 镜像：postgres:17.5-alpine
- 字符编码：UTF-8
- 排序规则：C
- 数据持久化：外部卷挂载

#### 性能配置
| 参数 | 开发环境 | 生产环境 | 说明 |
|------|----------|----------|------|
| shared_buffers | 256MB | 1GB | 共享缓冲区 |
| effective_cache_size | 1GB | 4GB | 有效缓存大小 |
| work_mem | 4MB | 16MB | 工作内存 |
| max_connections | 100 | 200 | 最大连接数 |

### 5.2 数据持久化

```yaml
volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_DIR}/postgres
```

## 6. 缓存配置 (MVP版本)

### 6.1 内存缓存策略

根据项目手册要求，MVP版本采用内存缓存而非Redis：

#### 缓存类型
- **Python字典缓存**：热点数据、实时行情、AI状态
- **配置缓存**：系统参数、用户设置
- **会话缓存**：用户登录状态

#### 缓存策略
| 策略 | 说明 | 适用场景 |
|------|------|----------|
| LRU淘汰 | 最近最少使用 | 热点数据管理 |
| TTL过期 | 时间到期自动清理 | 临时数据 |
| 手动刷新 | 关键数据手动更新 | 配置变更 |

### 6.2 升级路径

MVP版本使用内存缓存，企业级版本可升级为Redis集群：
- **当前**：内存缓存
- **升级**：Redis集群（分布式缓存）

## 7. 网络和存储配置

### 7.1 网络架构

#### 开发环境网络
```yaml
networks:
  ai_trading:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

#### 生产环境网络
```yaml
networks:
  ai_trading_prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: ai_trading_prod
```

### 7.2 存储卷配置

| 卷名称 | 用途 | 挂载路径 | 持久化 |
|--------|------|----------|--------|
| postgres_data | 数据库数据 | /var/lib/postgresql/data | 是 |
| backend_data | 后端应用数据 | /app/data | 是 |
| logs_data | 系统日志 | /var/log | 是 |
| uploads_data | 上传文件 | /app/uploads | 是 |

## 8. 统一环境配置

### 8.1 统一环境优势

根据项目手册要求，系统采用统一环境配置：

#### 核心原则
- **环境一致性**：开发即生产，避免环境差异导致的问题
- **部署简化**：一套配置，多环境复用
- **测试可靠**：开发环境测试结果与生产环境一致
- **维护便利**：统一的运维流程和故障排查
- **成本降低**：减少环境维护和配置管理成本

### 8.2 统一配置特点

#### 配置文件
- **单一配置**：仅使用 `docker-compose.yml`
- **环境变量**：通过 `.env` 文件统一管理
- **容器化部署**：Docker支持，便于迁移和扩展

#### 端口映射
- Nginx: 80:80, 443:443
- 前端: 3000:3000
- 后端: 8000:8000
- 数据库: 5432:5432

### 8.3 配置管理

| 配置项 | 统一环境 | 说明 |
|--------|----------|------|
| 配置文件 | docker-compose.yml | 单一配置文件 |
| 重启策略 | unless-stopped | 统一重启策略 |
| 日志级别 | INFO | 统一日志级别 |
| 健康检查 | 标准配置 | 统一健康检查 |
| 资源限制 | 合理配置 | 统一资源管理 |
