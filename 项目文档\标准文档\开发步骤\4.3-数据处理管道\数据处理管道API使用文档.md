# 数据处理管道API使用文档

## 1. API概述

### 1.1 API基本信息
- **基础URL**: `http://localhost:8000/api/v1/data-pipeline`
- **认证方式**: Bearer <PERSON>ken (JWT)
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 API功能
数据处理管道API提供以下核心功能：
- 管道状态监控
- 历史数据查询
- 实时数据推送
- AI交易员数据订阅
- 系统统计信息获取

## 2. 认证说明

### 2.1 获取访问令牌
```bash
# 登录获取令牌
curl -X POST "http://localhost:8000/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "your_username",
    "password": "your_password"
  }'
```

### 2.2 使用访问令牌
```bash
# 在请求头中包含令牌
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  "http://localhost:8000/api/v1/data-pipeline/status"
```

## 3. API端点详细说明

### 3.1 获取管道状态

#### 请求信息
- **方法**: GET
- **路径**: `/status`
- **描述**: 获取数据处理管道的当前运行状态

#### 请求示例
```bash
curl -X GET "http://localhost:8000/api/v1/data-pipeline/status" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### 响应示例
```json
{
  "is_running": true,
  "start_time": "2025-08-01T10:30:00",
  "runtime_seconds": 3600.5,
  "processed_count": 15420,
  "error_count": 3,
  "error_rate": 0.0002,
  "active_tasks": 8
}
```

#### 响应字段说明
| 字段 | 类型 | 描述 |
|------|------|------|
| is_running | boolean | 管道是否正在运行 |
| start_time | datetime | 管道启动时间 |
| runtime_seconds | float | 运行时长（秒） |
| processed_count | integer | 已处理数据量 |
| error_count | integer | 错误数量 |
| error_rate | float | 错误率 |
| active_tasks | integer | 活跃任务数 |

### 3.2 获取历史市场数据

#### 请求信息
- **方法**: GET
- **路径**: `/market-data/{symbol}`
- **描述**: 获取指定标的的历史市场数据

#### 请求参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| symbol | string | 是 | 标的代码（如：159509） |
| start_time | datetime | 否 | 开始时间（ISO格式） |
| end_time | datetime | 否 | 结束时间（ISO格式） |
| limit | integer | 否 | 返回数量限制（默认100） |

#### 请求示例
```bash
curl -X GET "http://localhost:8000/api/v1/data-pipeline/market-data/159509?limit=50" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### 响应示例
```json
[
  {
    "symbol": "159509",
    "current_price": 2.50,
    "open_price": 2.48,
    "high_price": 2.52,
    "low_price": 2.46,
    "volume": 1000000,
    "turnover": 2500000.0,
    "change": 0.02,
    "change_pct": 0.81,
    "bid_price": 2.49,
    "ask_price": 2.51,
    "bid_volume": 50000,
    "ask_volume": 48000,
    "timestamp": "2025-08-01T14:30:00"
  }
]
```

### 3.3 获取技术指标数据

#### 请求信息
- **方法**: GET
- **路径**: `/indicators/{symbol}/{indicator_name}`
- **描述**: 获取指定标的和指标的历史数据

#### 请求参数
| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| symbol | string | 是 | 标的代码 |
| indicator_name | string | 是 | 指标名称（RSI/MACD/MA/BOLL） |
| start_time | datetime | 否 | 开始时间 |
| end_time | datetime | 否 | 结束时间 |
| limit | integer | 否 | 返回数量限制 |

#### 请求示例
```bash
curl -X GET "http://localhost:8000/api/v1/data-pipeline/indicators/159509/RSI?limit=20" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### 响应示例
```json
[
  {
    "symbol": "159509",
    "indicator_name": "RSI",
    "indicator_value": 65.42,
    "period": 14,
    "timestamp": "2025-08-01T14:30:00",
    "additional_data": null
  }
]
```

### 3.4 获取管道统计信息

#### 请求信息
- **方法**: GET
- **路径**: `/statistics`
- **描述**: 获取数据管道的详细统计信息

#### 请求示例
```bash
curl -X GET "http://localhost:8000/api/v1/data-pipeline/statistics" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### 响应示例
```json
{
  "pipeline": {
    "is_running": true,
    "processed_count": 15420,
    "error_count": 3,
    "error_rate": 0.0002
  },
  "collector": {
    "collected_count": 15423,
    "error_count": 0,
    "collection_rate": 1.0
  },
  "processor": {
    "market_data": {
      "processed_count": 15420,
      "validation_errors": 2,
      "cleaning_applied": 45
    },
    "tick_data": {
      "processed_count": 0,
      "validation_errors": 0
    }
  },
  "indicators": {
    "RSI": 15420,
    "MACD": 15420,
    "MA": 15420,
    "BOLL": 15420
  },
  "distributors": {
    "websocket": {
      "active_connections": 3,
      "total_messages_sent": 46260,
      "failed_messages": 1
    },
    "ai_trader": {
      "active_subscribers": 8,
      "total_messages_sent": 123360,
      "failed_messages": 0
    }
  },
  "storage": {
    "database": {
      "stored_count": 15420,
      "batch_count": 155,
      "storage_errors": 0
    },
    "cache": {
      "cached_count": 1000,
      "hit_count": 8520,
      "miss_count": 1200
    }
  }
}
```

### 3.5 获取WebSocket连接信息

#### 请求信息
- **方法**: GET
- **路径**: `/connections`
- **描述**: 获取当前WebSocket连接的详细信息

#### 请求示例
```bash
curl -X GET "http://localhost:8000/api/v1/data-pipeline/connections" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### 响应示例
```json
{
  "active_connections": 3,
  "total_messages_sent": 46260,
  "failed_messages": 1,
  "connections": {
    "client-001": {
      "connected_at": "2025-08-01T10:30:00",
      "messages_sent": 15420,
      "last_activity": "2025-08-01T14:30:00",
      "status": "active"
    },
    "client-002": {
      "connected_at": "2025-08-01T11:15:00",
      "messages_sent": 12340,
      "last_activity": "2025-08-01T14:29:58",
      "status": "active"
    }
  }
}
```

### 3.6 AI交易员订阅管理

#### 3.6.1 订阅数据
- **方法**: POST
- **路径**: `/ai-trader/subscribe/{trader_id}`
- **描述**: AI交易员订阅数据推送

```bash
curl -X POST "http://localhost:8000/api/v1/data-pipeline/ai-trader/subscribe/AI-Trader-01" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### 3.6.2 取消订阅
- **方法**: DELETE
- **路径**: `/ai-trader/unsubscribe/{trader_id}`
- **描述**: AI交易员取消数据订阅

```bash
curl -X DELETE "http://localhost:8000/api/v1/data-pipeline/ai-trader/unsubscribe/AI-Trader-01" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## 4. WebSocket实时数据推送

### 4.1 连接建立
```javascript
// JavaScript WebSocket连接示例
const ws = new WebSocket('ws://localhost:8000/api/v1/data-pipeline/ws/client-001');

ws.onopen = function(event) {
    console.log('WebSocket连接已建立');
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    console.log('收到实时数据:', data);
};

ws.onclose = function(event) {
    console.log('WebSocket连接已关闭');
};

ws.onerror = function(error) {
    console.error('WebSocket错误:', error);
};
```

### 4.2 消息格式
```json
{
  "type": "market_data",
  "data": {
    "symbol": "159509",
    "current_price": 2.50,
    "volume": 1000000,
    "timestamp": "2025-08-01T14:30:00"
  },
  "timestamp": "2025-08-01T14:30:00.123"
}
```

### 4.3 消息类型
| 类型 | 描述 |
|------|------|
| market_data | 市场数据更新 |
| technical_indicator | 技术指标更新 |
| system_status | 系统状态更新 |
| heartbeat | 心跳消息 |

## 5. 错误处理

### 5.1 HTTP状态码
| 状态码 | 描述 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 503 | 服务不可用 |

### 5.2 错误响应格式
```json
{
  "error": {
    "code": "PIPELINE_NOT_RUNNING",
    "message": "数据管道未启动",
    "details": "请检查系统状态或联系管理员",
    "timestamp": "2025-08-01T14:30:00"
  }
}
```

### 5.3 常见错误码
| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| PIPELINE_NOT_RUNNING | 数据管道未启动 | 检查系统状态，重启服务 |
| INVALID_SYMBOL | 无效的标的代码 | 检查标的代码格式 |
| DATA_NOT_FOUND | 数据不存在 | 调整查询时间范围 |
| RATE_LIMIT_EXCEEDED | 请求频率超限 | 降低请求频率 |
| WEBSOCKET_CONNECTION_FAILED | WebSocket连接失败 | 检查网络连接 |

## 6. 使用示例

### 6.1 Python客户端示例
```python
import requests
import websocket
import json

class DataPipelineClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {"Authorization": f"Bearer {token}"}
    
    def get_pipeline_status(self):
        """获取管道状态"""
        response = requests.get(
            f"{self.base_url}/status",
            headers=self.headers
        )
        return response.json()
    
    def get_market_data(self, symbol, limit=100):
        """获取市场数据"""
        response = requests.get(
            f"{self.base_url}/market-data/{symbol}",
            params={"limit": limit},
            headers=self.headers
        )
        return response.json()
    
    def connect_websocket(self, connection_id):
        """连接WebSocket"""
        ws_url = f"ws://localhost:8000/api/v1/data-pipeline/ws/{connection_id}"
        ws = websocket.WebSocket()
        ws.connect(ws_url)
        return ws

# 使用示例
client = DataPipelineClient("http://localhost:8000/api/v1/data-pipeline", "your_token")
status = client.get_pipeline_status()
print(f"管道状态: {status}")
```

### 6.2 JavaScript客户端示例
```javascript
class DataPipelineClient {
    constructor(baseUrl, token) {
        this.baseUrl = baseUrl;
        this.headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
    }
    
    async getPipelineStatus() {
        const response = await fetch(`${this.baseUrl}/status`, {
            headers: this.headers
        });
        return await response.json();
    }
    
    async getMarketData(symbol, limit = 100) {
        const response = await fetch(
            `${this.baseUrl}/market-data/${symbol}?limit=${limit}`,
            { headers: this.headers }
        );
        return await response.json();
    }
    
    connectWebSocket(connectionId) {
        const ws = new WebSocket(
            `ws://localhost:8000/api/v1/data-pipeline/ws/${connectionId}`
        );
        
        ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleRealtimeData(data);
        };
        
        return ws;
    }
    
    handleRealtimeData(data) {
        console.log('实时数据:', data);
        // 处理实时数据的业务逻辑
    }
}

// 使用示例
const client = new DataPipelineClient(
    'http://localhost:8000/api/v1/data-pipeline',
    'your_token'
);

client.getPipelineStatus().then(status => {
    console.log('管道状态:', status);
});
```

## 7. 最佳实践

### 7.1 请求优化
- 使用适当的limit参数控制返回数据量
- 合理设置时间范围避免大量数据查询
- 使用WebSocket获取实时数据而非频繁轮询
- 实现客户端缓存减少重复请求

### 7.2 错误处理
- 实现指数退避重试机制
- 监控API响应时间和错误率
- 记录详细的错误日志便于调试
- 实现优雅的降级处理

### 7.3 安全考虑
- 定期刷新访问令牌
- 使用HTTPS加密传输
- 验证服务器证书
- 不在客户端存储敏感信息

### 7.4 性能优化
- 使用连接池复用HTTP连接
- 实现合理的超时设置
- 监控内存使用避免泄漏
- 使用压缩减少传输数据量
