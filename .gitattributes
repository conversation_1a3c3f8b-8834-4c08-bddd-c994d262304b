# 海天AI纳斯达克交易系统 - Git属性配置
# 基于Python 3.13.2 + FastAPI 0.116.1 + Vue.js 3.5.x + TypeScript技术栈

# ===== 行尾符规范化 =====
# 自动规范化行尾符，提交时转换为LF，检出时根据平台转换
* text=auto

# 强制使用LF行尾符的文件类型
*.py text eol=lf
*.js text eol=lf
*.ts text eol=lf
*.vue text eol=lf
*.json text eol=lf
*.md text eol=lf
*.yml text eol=lf
*.yaml text eol=lf
*.toml text eol=lf
*.cfg text eol=lf
*.ini text eol=lf
*.sh text eol=lf
*.sql text eol=lf

# 配置文件
*.conf text eol=lf
*.config text eol=lf
Dockerfile text eol=lf
.gitignore text eol=lf
.gitattributes text eol=lf

# 文档文件
*.txt text eol=lf
*.rst text eol=lf
LICENSE text eol=lf
README text eol=lf

# ===== 二进制文件 =====
# 明确标记为二进制文件，不进行文本处理
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.svg binary
*.webp binary
*.pdf binary
*.zip binary
*.tar binary
*.gz binary
*.7z binary
*.rar binary

# 字体文件
*.woff binary
*.woff2 binary
*.ttf binary
*.eot binary
*.otf binary

# 音视频文件
*.mp3 binary
*.mp4 binary
*.avi binary
*.mov binary
*.wav binary

# 数据库文件
*.db binary
*.sqlite binary
*.sqlite3 binary

# AI模型文件
*.pkl binary
*.joblib binary
*.h5 binary
*.pb binary
*.onnx binary
*.pt binary
*.pth binary

# 编译文件
*.pyc binary
*.pyo binary
*.so binary
*.dll binary
*.exe binary

# ===== 语言特定配置 =====
# Python文件
*.py text diff=python

# JavaScript/TypeScript文件
*.js text diff=javascript
*.ts text diff=javascript
*.vue text diff=javascript

# CSS文件
*.css text diff=css
*.scss text diff=css
*.sass text diff=css
*.less text diff=css

# HTML文件
*.html text diff=html
*.htm text diff=html

# XML文件
*.xml text
*.xsl text
*.xsd text

# ===== 合并策略 =====
# 包管理文件使用union合并策略
package.json merge=union
package-lock.json merge=union
yarn.lock merge=union
requirements.txt merge=union
pyproject.toml merge=union
Pipfile merge=union

# 配置文件使用union合并策略
*.ini merge=union
*.cfg merge=union
*.conf merge=union

# ===== 差异显示配置 =====
# 图片文件显示exif信息
*.png diff=exif
*.jpg diff=exif
*.jpeg diff=exif

# 文档文件
*.md diff=markdown

# ===== 导出忽略 =====
# 开发工具配置不导出到归档
.vscode/ export-ignore
.idea/ export-ignore
*.sublime-* export-ignore

# 测试文件不导出
tests/ export-ignore
test/ export-ignore
*test*.py export-ignore

# 文档源文件不导出
docs/ export-ignore
*.md export-ignore

# 开发脚本不导出
scripts/dev/ export-ignore
scripts/test/ export-ignore

# ===== 特殊文件处理 =====
# 大文件使用LFS
*.zip filter=lfs diff=lfs merge=lfs -text
*.tar.gz filter=lfs diff=lfs merge=lfs -text
*.7z filter=lfs diff=lfs merge=lfs -text

# AI模型文件使用LFS
*.h5 filter=lfs diff=lfs merge=lfs -text
*.pb filter=lfs diff=lfs merge=lfs -text
*.onnx filter=lfs diff=lfs merge=lfs -text
*.pkl filter=lfs diff=lfs merge=lfs -text

# 数据文件使用LFS（大于100MB）
*.csv filter=lfs diff=lfs merge=lfs -text
*.parquet filter=lfs diff=lfs merge=lfs -text

# ===== 中文文件名支持 =====
# 确保中文文件名正确处理
项目文档/ text
标准文档/ text
*.md text

# ===== 安全敏感文件 =====
# 密钥和配置文件特殊处理
*.key binary
*.pem binary
*.crt binary
secrets.json binary
config.json merge=ours

# 环境配置文件
.env merge=ours
.env.* merge=ours
