# ================================
# AI模型配置文件
# ================================
# 
# 此文件配置AI交易系统中使用的各种大语言模型
# 包括模型参数、API配置、性能优化等

# OpenAI GPT-4配置
openai:
  model: "gpt-4"
  base_url: "https://api.openai.com/v1"
  timeout: 60
  max_tokens: 4000
  temperature: 0.1
  top_p: 0.9
  frequency_penalty: 0.0
  presence_penalty: 0.0
  
  # 重试配置
  retry_times: 3
  retry_delay: 1.0
  
  # 使用场景
  use_cases:
    - "complex_reasoning"
    - "risk_assessment"
    - "decision_logic_analysis"

# Anthropic Claude配置
anthropic:
  model: "claude-3-sonnet-20240229"
  base_url: "https://api.anthropic.com"
  timeout: 60
  max_tokens: 4000
  temperature: 0.1
  top_p: 0.9
  
  # 重试配置
  retry_times: 3
  retry_delay: 1.0
  
  # 使用场景
  use_cases:
    - "data_analysis"
    - "pattern_recognition"
    - "performance_analysis"

# Google Gemini配置
google:
  model: "gemini-pro"
  timeout: 60
  max_tokens: 4000
  temperature: 0.1
  top_p: 0.9
  
  # 重试配置
  retry_times: 3
  retry_delay: 1.0
  
  # 使用场景
  use_cases:
    - "multimodal_analysis"
    - "comprehensive_evaluation"
    - "innovation_identification"

# AI交易员模型分配策略
trader_model_assignment:
  # 默认分配策略
  strategy: "balanced"  # balanced, performance_based, random
  
  # 具体分配（可选，覆盖策略）
  assignments:
    "AI-Trader-01": "gpt-4"
    "AI-Trader-02": "claude-3-sonnet"
    "AI-Trader-03": "gemini-pro"
    "AI-Trader-04": "gpt-4"
    "AI-Trader-05": "claude-3-sonnet"
    "AI-Trader-06": "gemini-pro"
    "AI-Trader-07": "gpt-4"
    "AI-Trader-08": "claude-3-sonnet"

# AI交易总监模型配置
supervisor_models:
  # 三模型并行分析
  primary_models:
    - name: "gpt-4"
      weight: 0.35
      focus: "risk_analysis"
    - name: "claude-3-sonnet"
      weight: 0.35
      focus: "performance_analysis"
    - name: "gemini-pro"
      weight: 0.30
      focus: "comprehensive_analysis"
  
  # 融合分析配置
  consensus_threshold: 0.8  # 共识阈值
  disagreement_threshold: 0.3  # 分歧阈值

# 性能优化配置
performance:
  # 并发控制
  max_concurrent_requests: 10
  request_queue_size: 100
  
  # 缓存配置
  response_cache_enabled: true
  cache_ttl: 300  # 5分钟
  
  # 负载均衡
  load_balancing_enabled: true
  health_check_interval: 60
  
  # 降级策略
  fallback_enabled: true
  fallback_model: "gpt-4"

# 成本控制
cost_management:
  # Token使用监控
  token_tracking_enabled: true
  daily_token_limit: 1000000
  
  # 成本预警
  cost_alert_threshold: 100.0  # 美元
  daily_cost_limit: 500.0  # 美元
  
  # 优化策略
  cost_optimization_enabled: true
  prefer_cheaper_models: false

# 质量控制
quality_control:
  # 响应验证
  response_validation_enabled: true
  min_response_length: 50
  max_response_length: 10000
  
  # 内容过滤
  content_filtering_enabled: true
  forbidden_keywords:
    - "guaranteed profit"
    - "risk-free"
    - "insider information"
  
  # 一致性检查
  consistency_check_enabled: true
  consistency_threshold: 0.7

# 监控和日志
monitoring:
  # 性能监控
  performance_monitoring_enabled: true
  response_time_threshold: 30.0  # 秒
  
  # 错误监控
  error_tracking_enabled: true
  error_rate_threshold: 0.05  # 5%
  
  # 详细日志
  detailed_logging_enabled: true
  log_requests: true
  log_responses: false  # 避免记录敏感信息
