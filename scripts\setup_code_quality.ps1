# 海天AI纳斯达克交易系统 - 代码质量工具安装脚本 (PowerShell)
# 基于Python 3.13.2 + FastAPI 0.116.1 + Vue.js 3.5.x技术栈
# 创建日期: 2025年7月13日

Write-Host "🚀 海天AI纳斯达克交易系统 - 代码质量工具安装" -ForegroundColor Green
Write-Host "=" * 60

# 检查Python版本
Write-Host "🐍 检查Python版本..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Python未安装或不在PATH中" -ForegroundColor Red
    exit 1
}

# 检查Node.js版本
Write-Host "🌐 检查Node.js版本..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version 2>&1
    Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js未安装或不在PATH中" -ForegroundColor Red
    exit 1
}

# 检查conda环境
$condaEnv = $env:CONDA_DEFAULT_ENV
if ($condaEnv) {
    Write-Host "✅ 检测到conda环境: $condaEnv" -ForegroundColor Green
} else {
    Write-Host "⚠️ 未检测到conda环境，建议在AI_Nasdaq_trading环境中运行" -ForegroundColor Yellow
}

# 设置后端代码质量工具
Write-Host "`n🔧 设置后端代码质量工具..." -ForegroundColor Yellow

if (Test-Path "backend") {
    Write-Host "📦 安装Python代码质量工具..." -ForegroundColor Cyan
    Set-Location backend
    
    try {
        # 使用poetry安装依赖
        poetry install --with dev
        Write-Host "✅ Python依赖安装完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ Python依赖安装失败: $_" -ForegroundColor Red
        Set-Location ..
        exit 1
    }
    
    Set-Location ..
} else {
    Write-Host "❌ backend目录不存在" -ForegroundColor Red
    exit 1
}

# 设置前端代码质量工具
Write-Host "`n🌐 设置前端代码质量工具..." -ForegroundColor Yellow

if (Test-Path "frontend") {
    Write-Host "📦 安装前端代码质量工具..." -ForegroundColor Cyan
    Set-Location frontend
    
    try {
        npm install
        Write-Host "✅ 前端依赖安装完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ 前端依赖安装失败: $_" -ForegroundColor Red
        Set-Location ..
        exit 1
    }
    
    Set-Location ..
} else {
    Write-Host "❌ frontend目录不存在" -ForegroundColor Red
    exit 1
}

# 设置pre-commit钩子
Write-Host "`n🪝 设置pre-commit钩子..." -ForegroundColor Yellow

try {
    # 安装pre-commit钩子
    pre-commit install
    Write-Host "✅ pre-commit钩子安装完成" -ForegroundColor Green
    
    # 运行一次pre-commit检查所有文件
    Write-Host "🔍 运行初始代码质量检查..." -ForegroundColor Cyan
    pre-commit run --all-files
    Write-Host "✅ 初始代码质量检查完成" -ForegroundColor Green
    
} catch {
    Write-Host "❌ pre-commit钩子安装失败: $_" -ForegroundColor Red
    exit 1
}

# 设置Git钩子
Write-Host "`n📝 设置Git钩子..." -ForegroundColor Yellow

if (Test-Path ".git") {
    try {
        git config core.autocrlf false
        git config core.eol lf
        Write-Host "✅ Git配置完成" -ForegroundColor Green
    } catch {
        Write-Host "❌ Git配置失败: $_" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "❌ 不在Git仓库中" -ForegroundColor Red
    exit 1
}

# 验证工具安装
Write-Host "`n✅ 验证代码质量工具..." -ForegroundColor Yellow

$toolsStatus = @{}

# 验证Python工具
$pythonTools = @("black", "isort", "flake8", "mypy", "bandit", "pre-commit")
foreach ($tool in $pythonTools) {
    try {
        & $tool --version | Out-Null
        $toolsStatus[$tool] = "✅"
    } catch {
        $toolsStatus[$tool] = "❌"
    }
}

# 验证前端工具
if (Test-Path "frontend") {
    $frontendTools = @("eslint", "prettier", "stylelint")
    Set-Location frontend
    foreach ($tool in $frontendTools) {
        try {
            npx $tool --version | Out-Null
            $toolsStatus[$tool] = "✅"
        } catch {
            $toolsStatus[$tool] = "❌"
        }
    }
    Set-Location ..
}

# 输出验证结果
Write-Host "`n📊 工具安装状态:" -ForegroundColor Yellow
foreach ($tool in $toolsStatus.Keys) {
    $status = $toolsStatus[$tool]
    if ($status -eq "✅") {
        Write-Host "  $status $tool" -ForegroundColor Green
    } else {
        Write-Host "  $status $tool" -ForegroundColor Red
    }
}

# 检查是否所有工具都安装成功
$failedTools = $toolsStatus.Keys | Where-Object { $toolsStatus[$_] -eq "❌" }
if ($failedTools.Count -gt 0) {
    Write-Host "`n❌ 以下工具安装失败: $($failedTools -join ', ')" -ForegroundColor Red
    exit 1
}

Write-Host "`n🎉 所有代码质量工具安装成功!" -ForegroundColor Green
Write-Host "`n📝 使用说明:" -ForegroundColor Cyan
Write-Host "  - 提交代码时会自动运行代码质量检查" -ForegroundColor White
Write-Host "  - 手动运行: pre-commit run --all-files" -ForegroundColor White
Write-Host "  - 后端格式化: cd backend && poetry run black ." -ForegroundColor White
Write-Host "  - 前端格式化: cd frontend && npm run format" -ForegroundColor White
Write-Host "  - 前端检查: cd frontend && npm run lint" -ForegroundColor White

Write-Host "`n✅ 代码质量工具安装完成!" -ForegroundColor Green
