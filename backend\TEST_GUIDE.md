# 海天AI纳斯达克交易系统 - 数据管道测试指南

## 概述

本指南提供了运行数据处理管道测试的详细说明。测试套件包括单元测试、集成测试和API端点测试。

## 测试环境要求

### 1. Python环境
- Python 3.13.2 或更高版本
- conda环境: `AI_Nasdaq_Trading`

### 2. 依赖包
确保安装了以下测试相关的依赖包：
```bash
pip install pytest pytest-asyncio httpx
```

### 3. 数据库配置
- PostgreSQL 17.5 数据库服务器运行中
- 数据库连接配置正确（在.env文件中）

## 测试套件结构

### 单元测试
1. **test_data_collector.py** - 数据采集器测试
   - 模拟数据采集器功能测试
   - QMT数据采集器接口测试
   - 订阅者通知机制测试
   - 错误处理和统计信息测试

2. **test_data_processor.py** - 数据处理器测试
   - 市场数据处理器测试
   - Tick数据处理器测试
   - 数据验证和标准化测试
   - 批量处理和并发处理测试

3. **test_technical_indicators.py** - 技术指标计算器测试
   - RSI计算器测试
   - MACD计算器测试
   - 移动平均线计算器测试
   - 布林带计算器测试
   - 成交量指标计算器测试

4. **test_data_distributor_storage.py** - 分发器和存储器测试
   - WebSocket分发器测试
   - AI交易员分发器测试
   - 缓存存储器测试
   - 数据库存储器测试

### 集成测试
5. **test_data_pipeline_integration.py** - 数据管道集成测试
   - 完整数据管道流程测试
   - 组件间协作测试
   - 性能和稳定性测试

### API测试
6. **test_data_pipeline_api.py** - API端点测试
   - 管道控制API测试
   - 数据查询API测试
   - WebSocket连接测试
   - 错误处理测试

## 运行测试

### 方法1: 使用测试运行脚本（推荐）

```bash
# 切换到backend目录
cd backend

# 激活conda环境
conda activate AI_Nasdaq_Trading

# 运行完整测试套件
python run_tests.py
```

这将：
- 运行所有测试套件
- 生成详细的测试报告（JSON和HTML格式）
- 显示测试摘要和统计信息

### 方法2: 单独运行测试

```bash
# 运行单个测试文件
python -m pytest tests/test_data_collector.py -v

# 运行特定测试类
python -m pytest tests/test_data_collector.py::TestMockDataCollector -v

# 运行特定测试方法
python -m pytest tests/test_data_collector.py::TestMockDataCollector::test_initialization -v

# 运行所有测试并生成覆盖率报告
python -m pytest tests/ --cov=app --cov-report=html
```

### 方法3: 使用pytest配置

```bash
# 使用pytest.ini配置运行
pytest

# 运行并显示详细输出
pytest -v

# 运行并显示失败的详细信息
pytest -v --tb=short
```

## 测试报告

### 报告文件
运行`run_tests.py`后会生成：
- `test_report_YYYYMMDD_HHMMSS.json` - JSON格式详细报告
- `test_report_YYYYMMDD_HHMMSS.html` - HTML格式可视化报告

### 报告内容
- 测试运行信息（时间戳、Python版本等）
- 各测试套件的详细结果
- 成功/失败统计
- 错误输出和调试信息

## 常见问题和解决方案

### 1. 导入错误
**问题**: `ModuleNotFoundError: No module named 'app'`
**解决**: 确保在backend目录下运行测试，并且Python路径正确

### 2. 数据库连接错误
**问题**: 数据库连接失败
**解决**: 
- 检查PostgreSQL服务是否运行
- 验证.env文件中的数据库配置
- 确保数据库用户有适当权限

### 3. 异步测试错误
**问题**: `RuntimeError: There is no current event loop`
**解决**: 确保安装了`pytest-asyncio`并使用`@pytest.mark.asyncio`装饰器

### 4. 模拟数据问题
**问题**: QMT接口不可用导致测试失败
**解决**: 测试使用模拟数据，不需要真实的QMT连接

### 5. 内存或性能问题
**问题**: 测试运行缓慢或内存不足
**解决**: 
- 减少测试数据量
- 使用`--tb=short`减少输出
- 分批运行测试

## 测试最佳实践

### 1. 测试隔离
- 每个测试方法都应该独立运行
- 使用fixtures提供测试数据
- 清理测试产生的副作用

### 2. 异步测试
- 使用`@pytest.mark.asyncio`装饰异步测试
- 正确处理异步上下文管理器
- 避免异步测试中的竞态条件

### 3. 模拟和存根
- 使用`unittest.mock`模拟外部依赖
- 模拟数据库操作以提高测试速度
- 模拟网络请求和WebSocket连接

### 4. 测试数据
- 使用fixtures提供一致的测试数据
- 避免硬编码测试数据
- 使用工厂模式生成测试数据

## 持续集成

### GitHub Actions配置示例
```yaml
name: 数据管道测试

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:17.5
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - uses: actions/checkout@v2
    
    - name: 设置Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.13.2'
    
    - name: 安装依赖
      run: |
        pip install -r requirements.txt
        pip install pytest pytest-asyncio httpx
    
    - name: 运行测试
      run: |
        cd backend
        python run_tests.py
    
    - name: 上传测试报告
      uses: actions/upload-artifact@v2
      with:
        name: test-reports
        path: backend/test_report_*.html
```

## 性能基准

### 预期性能指标
- 单元测试: < 30秒
- 集成测试: < 60秒
- API测试: < 45秒
- 总测试时间: < 3分钟

### 性能优化建议
1. 使用内存数据库进行测试
2. 并行运行独立测试
3. 缓存重复的测试数据
4. 优化测试数据生成

## 联系和支持

如果在运行测试时遇到问题，请：
1. 检查本指南中的常见问题部分
2. 查看测试报告中的详细错误信息
3. 确保环境配置正确
4. 联系开发团队获取支持

---

**注意**: 本测试套件设计为在开发环境中运行，使用模拟数据和模拟服务。在生产环境中运行前，请确保适当配置所有外部依赖。
