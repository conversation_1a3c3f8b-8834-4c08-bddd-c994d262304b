# =============================================================================
# 海天AI纳斯达克交易系统 - 默认站点配置
# 基于: 项目手册4.1节MVP版本技术栈配置
# 创建日期: 2025年7月13日
# 用途: 前端应用和API反向代理配置
# =============================================================================

server {
    listen 80;
    server_name localhost;
    
    # 根目录（如果需要直接提供静态文件）
    root /usr/share/nginx/html;
    index index.html;
    
    # =============================================================================
    # 安全配置
    # =============================================================================
    
    # 隐藏Nginx版本
    server_tokens off;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # =============================================================================
    # API代理配置
    # =============================================================================
    
    # API接口代理
    location /api/ {
        # 限流
        limit_req zone=api burst=20 nodelay;
        limit_conn conn_limit_per_ip 10;
        
        # 代理到后端服务
        proxy_pass http://backend_api/;
        
        # 代理头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # 错误处理
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503;
    }
    
    # 认证接口特殊限流
    location /api/auth/ {
        limit_req zone=login burst=5 nodelay;
        limit_conn conn_limit_per_ip 5;
        
        proxy_pass http://backend_api/auth/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # =============================================================================
    # WebSocket代理配置
    # =============================================================================
    
    # WebSocket连接代理
    location /ws {
        proxy_pass http://backend_api;
        
        # WebSocket升级头
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # 标准代理头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket特殊设置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
        proxy_connect_timeout 30s;
    }
    
    # =============================================================================
    # 前端应用代理配置
    # =============================================================================
    
    # 前端应用代理（开发环境）
    location / {
        # 尝试代理到前端开发服务器
        proxy_pass http://frontend_app;
        
        # 代理头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 如果前端服务不可用，回退到静态文件
        error_page 502 503 504 @fallback;
    }
    
    # 静态文件回退
    location @fallback {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache";
    }
    
    # =============================================================================
    # 静态资源配置
    # =============================================================================
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # =============================================================================
    # 健康检查和监控
    # =============================================================================
    
    # Nginx健康检查
    location /nginx-health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Nginx状态监控
    location /nginx-status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        allow 10.0.0.0/8;
        allow 172.16.0.0/12;
        allow 192.168.0.0/16;
        deny all;
    }
    
    # =============================================================================
    # 错误页面配置
    # =============================================================================
    
    # 自定义错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
