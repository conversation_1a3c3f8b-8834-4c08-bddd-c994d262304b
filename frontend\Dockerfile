# =============================================================================
# 海天AI纳斯达克交易系统 - 前端服务容器配置
# 基于: 项目手册4.1节MVP版本技术栈配置
# 创建日期: 2025年7月13日
# 最后更新: 2025年7月30日 (安全漏洞修复后版本同步)
# 技术栈: Vue.js 3.5.x + TypeScript 5.7.x + Vite 6.0.x + Vitest 3.2.x + Element Plus 2.9.x
# 构建策略: 单阶段构建，简化部署流程
# =============================================================================

FROM nginx:1.25.5-alpine

# 设置环境变量
ARG NODE_ENV=production
ARG VITE_API_BASE_URL=http://localhost:8001
ENV NGINX_ENVSUBST_TEMPLATE_DIR=/etc/nginx/templates
ENV NGINX_ENVSUBST_OUTPUT_DIR=/etc/nginx/conf.d

# 安装系统依赖
RUN apk add --no-cache \
    curl \
    gettext \
    nodejs \
    npm \
    git \
    python3 \
    make \
    g++

# 创建非root用户
RUN addgroup -g 1000 appgroup \
    && adduser -u 1000 -G appgroup -s /bin/sh -D appuser

# 设置工作目录
WORKDIR /app

# 创建基本的静态页面
RUN mkdir -p /usr/share/nginx/html
COPY index.html /usr/share/nginx/html/
RUN echo '<h1>海天AI纳斯达克交易系统</h1><p>系统正在启动中...</p>' > /usr/share/nginx/html/index.html

# 创建基本的Nginx配置文件
RUN echo 'server {' > /etc/nginx/conf.d/default.conf && \
    echo '    listen 80;' >> /etc/nginx/conf.d/default.conf && \
    echo '    server_name localhost;' >> /etc/nginx/conf.d/default.conf && \
    echo '    root /usr/share/nginx/html;' >> /etc/nginx/conf.d/default.conf && \
    echo '    index index.html;' >> /etc/nginx/conf.d/default.conf && \
    echo '    location / {' >> /etc/nginx/conf.d/default.conf && \
    echo '        try_files $uri $uri/ /index.html;' >> /etc/nginx/conf.d/default.conf && \
    echo '    }' >> /etc/nginx/conf.d/default.conf && \
    echo '    location /nginx-health {' >> /etc/nginx/conf.d/default.conf && \
    echo '        return 200 "healthy";' >> /etc/nginx/conf.d/default.conf && \
    echo '    }' >> /etc/nginx/conf.d/default.conf && \
    echo '}' >> /etc/nginx/conf.d/default.conf

# 设置权限
RUN chown -R appuser:appgroup /usr/share/nginx/html \
    && chmod -R 755 /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost/nginx-health || exit 1

# 启动命令
CMD ["nginx", "-g", "daemon off;"]

# =============================================================================
# 镜像元数据
# =============================================================================
LABEL maintainer="海天AI量化交易开发团队"
LABEL version="1.0.0"
LABEL description="海天AI纳斯达克交易系统前端服务"
LABEL tech.stack="Vue.js 3.5.x + TypeScript 5.7.x + Vite 6.0.x + Vitest 3.2.x + Element Plus 2.9.x"
LABEL build.date="2025-07-13"
LABEL last.update="2025-07-30"
