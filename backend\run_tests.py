"""
海天AI纳斯达克交易系统 - 测试运行脚本
基于: 项目手册4.3节数据处理管道设计
创建日期: 2025年8月1日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: 运行所有数据管道相关测试并生成报告
"""

import os
import sys
import subprocess
import json
from datetime import datetime
from pathlib import Path


def run_command(command, description):
    """运行命令并返回结果"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    print(f"命令: {command}")
    print("-" * 60)
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        print(f"返回码: {result.returncode}")
        
        if result.stdout:
            print("标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0, result.stdout, result.stderr
        
    except subprocess.TimeoutExpired:
        print("❌ 命令执行超时")
        return False, "", "命令执行超时"
    except Exception as e:
        print(f"❌ 命令执行失败: {e}")
        return False, "", str(e)


def generate_test_report(test_results):
    """生成测试报告"""
    report = {
        "test_run_info": {
            "timestamp": datetime.now().isoformat(),
            "python_version": sys.version,
            "working_directory": os.getcwd(),
            "total_test_suites": len(test_results)
        },
        "test_results": test_results,
        "summary": {
            "passed_suites": sum(1 for result in test_results if result["success"]),
            "failed_suites": sum(1 for result in test_results if not result["success"]),
            "total_suites": len(test_results)
        }
    }
    
    # 计算成功率
    if report["summary"]["total_suites"] > 0:
        success_rate = report["summary"]["passed_suites"] / report["summary"]["total_suites"]
        report["summary"]["success_rate"] = f"{success_rate:.2%}"
    else:
        report["summary"]["success_rate"] = "0%"
    
    return report


def main():
    """主函数"""
    print("🎯 海天AI纳斯达克交易系统 - 数据管道测试套件")
    print("=" * 80)
    
    # 确保在正确的目录
    if not os.path.exists("app"):
        print("❌ 错误: 请在backend目录下运行此脚本")
        sys.exit(1)
    
    # 测试套件列表
    test_suites = [
        {
            "name": "数据采集器单元测试",
            "command": "python -m pytest tests/test_data_collector.py -v --tb=short --no-cov",
            "file": "tests/test_data_collector.py"
        },
        {
            "name": "数据处理器单元测试", 
            "command": "python -m pytest tests/test_data_processor.py -v --tb=short --no-cov",
            "file": "tests/test_data_processor.py"
        },
        {
            "name": "技术指标计算器单元测试",
            "command": "python -m pytest tests/test_technical_indicators.py -v --tb=short --no-cov",
            "file": "tests/test_technical_indicators.py"
        },
        {
            "name": "数据分发器和存储器单元测试",
            "command": "python -m pytest tests/test_data_distributor_storage.py -v --tb=short --no-cov",
            "file": "tests/test_data_distributor_storage.py"
        },
        {
            "name": "数据管道集成测试",
            "command": "python -m pytest tests/test_data_pipeline_integration.py -v --tb=short --no-cov",
            "file": "tests/test_data_pipeline_integration.py"
        },
        {
            "name": "数据管道API端点测试",
            "command": "python -m pytest tests/test_data_pipeline_api.py -v --tb=short --no-cov",
            "file": "tests/test_data_pipeline_api.py"
        }
    ]
    
    # 运行测试
    test_results = []
    
    for suite in test_suites:
        # 检查测试文件是否存在
        if not os.path.exists(suite["file"]):
            print(f"⚠️  跳过 {suite['name']}: 文件 {suite['file']} 不存在")
            test_results.append({
                "name": suite["name"],
                "success": False,
                "stdout": "",
                "stderr": f"测试文件 {suite['file']} 不存在",
                "skipped": True
            })
            continue
        
        # 运行测试
        success, stdout, stderr = run_command(suite["command"], suite["name"])
        
        test_results.append({
            "name": suite["name"],
            "success": success,
            "stdout": stdout,
            "stderr": stderr,
            "skipped": False
        })
    
    # 生成测试报告
    print(f"\n{'='*80}")
    print("📊 生成测试报告")
    print(f"{'='*80}")
    
    report = generate_test_report(test_results)
    
    # 保存JSON报告
    report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"✅ JSON报告已保存: {report_file}")
    
    # 生成HTML报告
    html_report = generate_html_report(report)
    html_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_report)
    
    print(f"✅ HTML报告已保存: {html_file}")
    
    # 打印摘要
    print(f"\n{'='*80}")
    print("📋 测试摘要")
    print(f"{'='*80}")
    print(f"总测试套件: {report['summary']['total_suites']}")
    print(f"通过套件: {report['summary']['passed_suites']}")
    print(f"失败套件: {report['summary']['failed_suites']}")
    print(f"成功率: {report['summary']['success_rate']}")
    
    # 显示失败的测试
    failed_tests = [result for result in test_results if not result["success"] and not result.get("skipped", False)]
    if failed_tests:
        print(f"\n❌ 失败的测试套件:")
        for test in failed_tests:
            print(f"  - {test['name']}")
    
    # 显示跳过的测试
    skipped_tests = [result for result in test_results if result.get("skipped", False)]
    if skipped_tests:
        print(f"\n⚠️  跳过的测试套件:")
        for test in skipped_tests:
            print(f"  - {test['name']}")
    
    # 返回适当的退出码
    if report['summary']['failed_suites'] > 0:
        print(f"\n❌ 测试完成，但有失败的测试套件")
        sys.exit(1)
    else:
        print(f"\n✅ 所有测试套件通过！")
        sys.exit(0)


def generate_html_report(report):
    """生成HTML测试报告"""
    html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>海天AI纳斯达克交易系统 - 数据管道测试报告</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; text-align: center; border-bottom: 3px solid #3498db; padding-bottom: 10px; }}
        h2 {{ color: #34495e; border-left: 4px solid #3498db; padding-left: 10px; }}
        .summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }}
        .summary-card {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }}
        .summary-card h3 {{ margin: 0 0 10px 0; font-size: 1.2em; }}
        .summary-card .value {{ font-size: 2em; font-weight: bold; }}
        .test-result {{ margin: 20px 0; padding: 15px; border-radius: 8px; border-left: 5px solid #ddd; }}
        .test-success {{ background-color: #d4edda; border-left-color: #28a745; }}
        .test-failure {{ background-color: #f8d7da; border-left-color: #dc3545; }}
        .test-skipped {{ background-color: #fff3cd; border-left-color: #ffc107; }}
        .test-name {{ font-weight: bold; font-size: 1.1em; margin-bottom: 10px; }}
        .test-output {{ background-color: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 0.9em; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }}
        .timestamp {{ color: #6c757d; font-size: 0.9em; text-align: center; margin-top: 20px; }}
        .status-badge {{ display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 0.8em; font-weight: bold; }}
        .badge-success {{ background-color: #28a745; color: white; }}
        .badge-failure {{ background-color: #dc3545; color: white; }}
        .badge-skipped {{ background-color: #ffc107; color: black; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 海天AI纳斯达克交易系统 - 数据管道测试报告</h1>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总测试套件</h3>
                <div class="value">{report['summary']['total_suites']}</div>
            </div>
            <div class="summary-card">
                <h3>通过套件</h3>
                <div class="value">{report['summary']['passed_suites']}</div>
            </div>
            <div class="summary-card">
                <h3>失败套件</h3>
                <div class="value">{report['summary']['failed_suites']}</div>
            </div>
            <div class="summary-card">
                <h3>成功率</h3>
                <div class="value">{report['summary']['success_rate']}</div>
            </div>
        </div>
        
        <h2>📋 测试结果详情</h2>
"""
    
    for result in report['test_results']:
        if result.get('skipped', False):
            status_class = "test-skipped"
            badge_class = "badge-skipped"
            status_text = "跳过"
        elif result['success']:
            status_class = "test-success"
            badge_class = "badge-success"
            status_text = "通过"
        else:
            status_class = "test-failure"
            badge_class = "badge-failure"
            status_text = "失败"
        
        html += f"""
        <div class="test-result {status_class}">
            <div class="test-name">
                {result['name']} 
                <span class="status-badge {badge_class}">{status_text}</span>
            </div>
"""
        
        if result['stdout']:
            html += f"""
            <h4>输出:</h4>
            <div class="test-output">{result['stdout']}</div>
"""
        
        if result['stderr']:
            html += f"""
            <h4>错误:</h4>
            <div class="test-output">{result['stderr']}</div>
"""
        
        html += "</div>"
    
    html += f"""
        <div class="timestamp">
            报告生成时间: {report['test_run_info']['timestamp']}<br>
            Python版本: {report['test_run_info']['python_version']}<br>
            工作目录: {report['test_run_info']['working_directory']}
        </div>
    </div>
</body>
</html>
"""
    
    return html


if __name__ == "__main__":
    main()
