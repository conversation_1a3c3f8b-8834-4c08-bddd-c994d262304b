# 虚拟环境配置指南

## 文档信息
- **创建日期**: 2025年7月13日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: 海天AI纳斯达克交易系统虚拟环境配置和管理
- **技术基础**: Conda + Poetry + nvm + Docker

## 版本更新记录
- v1.0: 初始版本，完成虚拟环境配置指南

## 目录
1. [Python虚拟环境管理](#1-python虚拟环境管理)
2. [Node.js版本管理](#2-nodejs版本管理)
3. [环境隔离最佳实践](#3-环境隔离最佳实践)
4. [容器化环境配置](#4-容器化环境配置)

---

## 1. Python虚拟环境管理

### 1.1 Conda环境管理

**环境创建和配置**:
```bash
# 创建项目专用环境
conda create -n AI_Nasdaq_trading python=3.13.2 -y

# 激活环境
conda activate AI_Nasdaq_trading

# 验证环境
python --version  # 应显示 Python 3.13.2
which python      # 显示环境中的Python路径
```

**环境信息查看**:
```bash
# 查看所有环境
conda env list

# 查看当前环境信息
conda info

# 查看环境中的包
conda list

# 导出环境配置
conda env export > environment.yml
```

**环境管理命令**:
```bash
# 克隆环境
conda create --name AI_Nasdaq_trading_backup --clone AI_Nasdaq_trading

# 删除环境
conda env remove -n AI_Nasdaq_trading

# 从配置文件创建环境
conda env create -f environment.yml

# 更新环境
conda env update -f environment.yml
```

### 1.2 Poetry虚拟环境集成

**Poetry环境配置**:
```bash
# 在项目目录中初始化Poetry
cd backend
poetry init

# 配置虚拟环境在项目内创建
poetry config virtualenvs.in-project true

# 安装依赖并创建虚拟环境
poetry install

# 激活Poetry环境
poetry shell
```

**Poetry环境管理**:
```bash
# 查看虚拟环境信息
poetry env info

# 查看虚拟环境路径
poetry env info --path

# 列出所有Poetry环境
poetry env list

# 删除虚拟环境
poetry env remove python
```

### 1.3 环境变量配置

**项目环境变量设置**:
```bash
# 在.env文件中配置
# backend/.env
PYTHONPATH=/app
DATABASE_URL=postgresql://user:pass@localhost/db
REDIS_URL=redis://localhost:6379
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key
```

**环境变量加载**:
```python
# backend/app/core/config.py
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    database_url: str
    redis_url: str
    openai_api_key: str
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
```

### 1.4 开发工具集成

**VS Code集成配置**:
```json
// .vscode/settings.json
{
  "python.defaultInterpreterPath": "D:\\GHJ\\AI\\conda_envs_pkgs\\envs\\AI_Nasdaq_trading\\python.exe",
  "python.terminal.activateEnvironment": true,
  "python.envFile": "${workspaceFolder}/backend/.env"
}
```

**调试配置**:
```json
// .vscode/launch.json
{
  "configurations": [
    {
      "name": "Python: FastAPI",
      "type": "python",
      "request": "launch",
      "module": "uvicorn",
      "args": ["app.main:app", "--reload"],
      "cwd": "${workspaceFolder}/backend",
      "envFile": "${workspaceFolder}/backend/.env"
    }
  ]
}
```

## 2. Node.js版本管理

### 2.1 Node.js版本控制

**使用nvm管理Node.js版本**:
```bash
# 安装nvm (Windows使用nvm-windows)
# 下载地址: https://github.com/coreybutler/nvm-windows

# 安装最新LTS版本
nvm install lts

# 安装特定版本
nvm install 24.1.0

# 切换版本
nvm use 24.1.0

# 查看已安装版本
nvm list

# 设置默认版本
nvm alias default 24.1.0
```

**项目版本锁定**:
```json
// frontend/.nvmrc
24.1.0
```

```bash
# 使用项目指定版本
cd frontend
nvm use  # 自动读取.nvmrc文件
```

### 2.2 包管理器配置

**npm配置优化**:
```bash
# 设置npm镜像源
npm config set registry https://registry.npmjs.org/

# 配置缓存目录
npm config set cache D:\npm-cache

# 设置全局安装目录
npm config set prefix D:\npm-global

# 查看配置
npm config list
```

**yarn替代方案**:
```bash
# 安装yarn
npm install -g yarn

# 设置yarn镜像源
yarn config set registry https://registry.npmjs.org/

# 项目中使用yarn
cd frontend
yarn install
yarn dev
```

### 2.3 前端环境隔离

**项目依赖隔离**:
```bash
# 使用npm ci确保一致性
npm ci  # 生产环境推荐

# 清理node_modules
rm -rf node_modules package-lock.json
npm install

# 检查依赖树
npm ls
npm ls --depth=0  # 仅显示顶级依赖
```

**环境变量管理**:
```bash
# frontend/.env.development
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_TITLE=海天AI交易系统(开发)
VITE_LOG_LEVEL=debug

# frontend/.env.production
VITE_API_BASE_URL=https://api.haitian-ai.com
VITE_APP_TITLE=海天AI交易系统
VITE_LOG_LEVEL=error
```

### 2.4 构建环境配置

**Vite配置优化**:
```typescript
// frontend/vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  server: {
    port: 3000,
    host: '0.0.0.0',
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus']
        }
      }
    }
  }
})
```

## 3. 环境隔离最佳实践

### 3.1 多环境管理策略

**环境分类**:
- **开发环境** (development): 本地开发调试
- **测试环境** (testing): 自动化测试
- **预生产环境** (staging): 生产前验证
- **生产环境** (production): 正式运行环境

**环境配置文件**:
```bash
# 目录结构
config/
├── development.yml    # 开发环境配置
├── testing.yml       # 测试环境配置
├── staging.yml       # 预生产环境配置
└── production.yml    # 生产环境配置
```

### 3.2 依赖隔离策略

**Python依赖隔离**:
```toml
# pyproject.toml
[tool.poetry.group.dev.dependencies]
pytest = "^8.3.3"
black = "^24.10.0"
mypy = "^1.14.0"

[tool.poetry.group.test.dependencies]
pytest-cov = "^6.0.0"
pytest-mock = "^3.14.0"

[tool.poetry.group.docs.dependencies]
mkdocs = "^1.5.0"
```

**前端依赖隔离**:
```json
{
  "dependencies": {
    "vue": "^3.5.13"
  },
  "devDependencies": {
    "vite": "^6.0.5",
    "typescript": "^5.7.2"
  },
  "peerDependencies": {
    "vue": "^3.5.0"
  }
}
```

### 3.3 配置管理最佳实践

**敏感信息管理**:
```bash
# 使用.env文件管理敏感配置
# .env.example (提交到版本控制)
DATABASE_URL=postgresql://user:password@localhost/dbname
REDIS_URL=redis://localhost:6379
OPENAI_API_KEY=your_api_key_here

# .env (不提交到版本控制)
DATABASE_URL=postgresql://real_user:real_pass@localhost/real_db
REDIS_URL=redis://localhost:6379
OPENAI_API_KEY=sk-real-api-key
```

**配置验证**:
```python
# backend/app/core/config.py
from pydantic import BaseSettings, validator

class Settings(BaseSettings):
    database_url: str
    openai_api_key: str
    
    @validator('openai_api_key')
    def validate_openai_key(cls, v):
        if not v.startswith('sk-'):
            raise ValueError('Invalid OpenAI API key format')
        return v
```

### 3.4 环境切换自动化

**自动化脚本**:
```bash
#!/bin/bash
# scripts/switch-env.sh

ENV=${1:-development}

echo "切换到 $ENV 环境..."

# 激活Python环境
conda activate AI_Nasdaq_trading

# 设置环境变量
export NODE_ENV=$ENV
export PYTHON_ENV=$ENV

# 复制对应的配置文件
cp config/$ENV.yml config/current.yml

# 安装对应环境的依赖
if [ "$ENV" = "production" ]; then
    poetry install --only=main
    npm ci --only=production
else
    poetry install
    npm install
fi

echo "环境切换完成: $ENV"
```

## 4. 容器化环境配置

### 4.1 Docker环境隔离

**Python服务容器化**:
```dockerfile
# backend/Dockerfile
FROM python:3.13.2-slim

WORKDIR /app

# 安装Poetry
RUN pip install poetry

# 复制依赖文件
COPY pyproject.toml poetry.lock ./

# 配置Poetry
RUN poetry config virtualenvs.create false

# 安装依赖
RUN poetry install --only=main

# 复制应用代码
COPY . .

# 设置环境变量
ENV PYTHONPATH=/app

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

**前端服务容器化**:
```dockerfile
# frontend/Dockerfile
FROM node:24.1.0-alpine

WORKDIR /app

# 复制依赖文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 使用nginx提供静态文件服务
FROM nginx:alpine
COPY --from=0 /app/dist /usr/share/nginx/html
```

### 4.2 Docker Compose环境编排

**开发环境编排**:
```yaml
# docker-compose.dev.yml
version: '3.8'
services:
  backend:
    build: ./backend
    volumes:
      - ./backend:/app
    environment:
      - PYTHON_ENV=development
    ports:
      - "8000:8000"
    
  frontend:
    build: ./frontend
    volumes:
      - ./frontend:/app
    environment:
      - NODE_ENV=development
    ports:
      - "3000:3000"
```

### 4.3 环境一致性保证

**版本锁定策略**:
```yaml
# docker-compose.yml
version: '3.8'
services:
  backend:
    image: python:3.13.2-slim
    
  frontend:
    image: node:24.1.0-alpine
    
  database:
    image: postgres:17.5-alpine
    
  redis:
    image: redis:7.4-alpine
```

**健康检查配置**:
```yaml
services:
  backend:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

---

## 总结

本指南提供了完整的虚拟环境配置和管理方案，包括Python和Node.js环境管理、环境隔离最佳实践和容器化配置。遵循这些配置可以确保开发环境的一致性、可重现性和可维护性，为海天AI纳斯达克交易系统提供稳定的开发基础。
