"""
海天AI纳斯达克交易系统 - 数据存储管理模块
基于: 项目手册4.3节数据处理管道设计
创建日期: 2025年8月1日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: 数据持久化存储，包括市场数据和技术指标数据的存储
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, insert, update, delete, and_

from app.core.database import get_async_db
from app.schemas.market import MarketData, TechnicalIndicator
from app.models.market import MarketData as MarketDataModel, TechnicalIndicator as TechnicalIndicatorModel
from app.core.exceptions import DatabaseException

logger = logging.getLogger(__name__)


class DataStorage(ABC):
    """数据存储抽象基类"""
    
    def __init__(self):
        self.stored_count = 0
        self.error_count = 0
    
    @abstractmethod
    async def store_market_data(self, data: MarketData) -> bool:
        """存储市场数据"""
        pass
    
    @abstractmethod
    async def store_technical_indicator(self, indicator: TechnicalIndicator) -> bool:
        """存储技术指标"""
        pass
    
    @abstractmethod
    async def get_market_data(self, symbol: str, start_time: datetime, end_time: datetime) -> List[MarketData]:
        """获取历史市场数据"""
        pass
    
    @abstractmethod
    async def get_technical_indicators(self, symbol: str, indicator_name: str, start_time: datetime, end_time: datetime) -> List[TechnicalIndicator]:
        """获取历史技术指标"""
        pass
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        return {
            "stored_count": self.stored_count,
            "error_count": self.error_count,
            "error_rate": self.error_count / max(self.stored_count, 1)
        }


class DatabaseStorage(DataStorage):
    """数据库存储实现"""
    
    def __init__(self):
        super().__init__()
        self.batch_size = 100
        self.batch_buffer = {
            "market_data": [],
            "technical_indicators": []
        }
        self.last_flush_time = datetime.now()
        self.flush_interval = 30  # 30秒刷新一次
    
    async def store_market_data(self, data: MarketData) -> bool:
        """存储市场数据"""
        try:
            # 添加到批处理缓冲区
            self.batch_buffer["market_data"].append(data)
            
            # 检查是否需要刷新
            if (len(self.batch_buffer["market_data"]) >= self.batch_size or 
                (datetime.now() - self.last_flush_time).seconds >= self.flush_interval):
                await self._flush_market_data()
            
            return True
            
        except Exception as e:
            logger.error(f"存储市场数据失败: {e}")
            self.error_count += 1
            return False
    
    async def store_technical_indicator(self, indicator: TechnicalIndicator) -> bool:
        """存储技术指标"""
        try:
            # 添加到批处理缓冲区
            self.batch_buffer["technical_indicators"].append(indicator)
            
            # 检查是否需要刷新
            if (len(self.batch_buffer["technical_indicators"]) >= self.batch_size or 
                (datetime.now() - self.last_flush_time).seconds >= self.flush_interval):
                await self._flush_technical_indicators()
            
            return True
            
        except Exception as e:
            logger.error(f"存储技术指标失败: {e}")
            self.error_count += 1
            return False
    
    async def _flush_market_data(self):
        """刷新市场数据到数据库"""
        if not self.batch_buffer["market_data"]:
            return
        
        async for session in get_async_db():
            try:
                # 批量插入市场数据
                market_data_records = []
                for data in self.batch_buffer["market_data"]:
                    record = {
                        "symbol": data.symbol,
                        "current_price": float(data.current_price),
                        "open_price": float(data.open_price) if data.open_price else None,
                        "high_price": float(data.high_price) if data.high_price else None,
                        "low_price": float(data.low_price) if data.low_price else None,
                        "volume": data.volume,
                        "turnover": data.turnover,
                        "change": float(data.change) if data.change else None,
                        "change_pct": data.change_pct,
                        "bid_price": float(data.bid_price) if data.bid_price else None,
                        "ask_price": float(data.ask_price) if data.ask_price else None,
                        "bid_volume": data.bid_volume,
                        "ask_volume": data.ask_volume,
                        "timestamp": data.timestamp or datetime.now()
                    }
                    market_data_records.append(record)
                
                # 执行批量插入
                await session.execute(insert(MarketDataModel), market_data_records)
                await session.commit()
                
                self.stored_count += len(self.batch_buffer["market_data"])
                logger.debug(f"批量存储市场数据: {len(self.batch_buffer['market_data'])}条")
                
                # 清空缓冲区
                self.batch_buffer["market_data"].clear()
                self.last_flush_time = datetime.now()
                
            except Exception as e:
                await session.rollback()
                logger.error(f"批量存储市场数据失败: {e}")
                self.error_count += len(self.batch_buffer["market_data"])
                raise DatabaseException(f"市场数据存储失败: {e}")
    
    async def _flush_technical_indicators(self):
        """刷新技术指标到数据库"""
        if not self.batch_buffer["technical_indicators"]:
            return
        
        async for session in get_async_db():
            try:
                # 批量插入技术指标
                indicator_records = []
                for indicator in self.batch_buffer["technical_indicators"]:
                    record = {
                        "symbol": indicator.symbol,
                        "indicator_name": indicator.indicator_name,
                        "indicator_value": float(indicator.indicator_value),
                        "period": indicator.period,
                        "timestamp": indicator.timestamp or datetime.now(),
                        "additional_data": indicator.additional_data
                    }
                    indicator_records.append(record)
                
                # 执行批量插入
                await session.execute(insert(TechnicalIndicatorModel), indicator_records)
                await session.commit()
                
                self.stored_count += len(self.batch_buffer["technical_indicators"])
                logger.debug(f"批量存储技术指标: {len(self.batch_buffer['technical_indicators'])}条")
                
                # 清空缓冲区
                self.batch_buffer["technical_indicators"].clear()
                self.last_flush_time = datetime.now()
                
            except Exception as e:
                await session.rollback()
                logger.error(f"批量存储技术指标失败: {e}")
                self.error_count += len(self.batch_buffer["technical_indicators"])
                raise DatabaseException(f"技术指标存储失败: {e}")
    
    async def get_market_data(self, symbol: str, start_time: datetime, end_time: datetime) -> List[MarketData]:
        """获取历史市场数据"""
        async for session in get_async_db():
            try:
                query = select(MarketDataModel).where(
                    and_(
                        MarketDataModel.symbol == symbol,
                        MarketDataModel.timestamp >= start_time,
                        MarketDataModel.timestamp <= end_time
                    )
                ).order_by(MarketDataModel.timestamp)
                
                result = await session.execute(query)
                records = result.scalars().all()
                
                # 转换为MarketData对象
                market_data_list = []
                for record in records:
                    market_data = MarketData(
                        symbol=record.symbol,
                        current_price=record.current_price,
                        open_price=record.open_price,
                        high_price=record.high_price,
                        low_price=record.low_price,
                        volume=record.volume,
                        turnover=record.turnover,
                        change=record.change,
                        change_pct=record.change_pct,
                        bid_price=record.bid_price,
                        ask_price=record.ask_price,
                        bid_volume=record.bid_volume,
                        ask_volume=record.ask_volume,
                        timestamp=record.timestamp
                    )
                    market_data_list.append(market_data)
                
                return market_data_list
                
            except Exception as e:
                logger.error(f"获取历史市场数据失败: {e}")
                raise DatabaseException(f"获取市场数据失败: {e}")
    
    async def get_technical_indicators(self, symbol: str, indicator_name: str, start_time: datetime, end_time: datetime) -> List[TechnicalIndicator]:
        """获取历史技术指标"""
        async for session in get_async_db():
            try:
                query = select(TechnicalIndicatorModel).where(
                    and_(
                        TechnicalIndicatorModel.symbol == symbol,
                        TechnicalIndicatorModel.indicator_name == indicator_name,
                        TechnicalIndicatorModel.timestamp >= start_time,
                        TechnicalIndicatorModel.timestamp <= end_time
                    )
                ).order_by(TechnicalIndicatorModel.timestamp)
                
                result = await session.execute(query)
                records = result.scalars().all()
                
                # 转换为TechnicalIndicator对象
                indicator_list = []
                for record in records:
                    indicator = TechnicalIndicator(
                        symbol=record.symbol,
                        indicator_name=record.indicator_name,
                        indicator_value=record.indicator_value,
                        period=record.period,
                        timestamp=record.timestamp,
                        additional_data=record.additional_data
                    )
                    indicator_list.append(indicator)
                
                return indicator_list
                
            except Exception as e:
                logger.error(f"获取历史技术指标失败: {e}")
                raise DatabaseException(f"获取技术指标失败: {e}")
    
    async def cleanup_old_data(self, retention_days: int = 30):
        """清理过期数据"""
        cutoff_time = datetime.now() - timedelta(days=retention_days)
        
        async for session in get_async_db():
            try:
                # 清理过期市场数据
                market_data_delete = delete(MarketDataModel).where(
                    MarketDataModel.timestamp < cutoff_time
                )
                market_result = await session.execute(market_data_delete)
                
                # 清理过期技术指标
                indicator_delete = delete(TechnicalIndicatorModel).where(
                    TechnicalIndicatorModel.timestamp < cutoff_time
                )
                indicator_result = await session.execute(indicator_delete)
                
                await session.commit()
                
                logger.info(f"清理过期数据完成: 市场数据{market_result.rowcount}条, 技术指标{indicator_result.rowcount}条")
                
            except Exception as e:
                await session.rollback()
                logger.error(f"清理过期数据失败: {e}")
                raise DatabaseException(f"数据清理失败: {e}")
    
    async def force_flush(self):
        """强制刷新所有缓冲区数据"""
        await self._flush_market_data()
        await self._flush_technical_indicators()


class CacheStorage(DataStorage):
    """缓存存储实现（Redis或内存）"""
    
    def __init__(self, max_size: int = 10000):
        super().__init__()
        self.cache = {}
        self.max_cache_size = max_size
        self.cache_ttl = 3600  # 1小时TTL
    
    async def store_market_data(self, data: MarketData) -> bool:
        """存储市场数据到缓存"""
        try:
            cache_key = f"market_data:{data.symbol}:{data.timestamp.isoformat()}"
            self.cache[cache_key] = {
                "data": data,
                "expire_time": datetime.now() + timedelta(seconds=self.cache_ttl)
            }
            
            # 清理过期缓存
            await self._cleanup_expired_cache()
            
            self.stored_count += 1
            return True
            
        except Exception as e:
            logger.error(f"缓存存储市场数据失败: {e}")
            self.error_count += 1
            return False
    
    async def store_technical_indicator(self, indicator: TechnicalIndicator) -> bool:
        """存储技术指标到缓存"""
        try:
            cache_key = f"indicator:{indicator.symbol}:{indicator.indicator_name}:{indicator.timestamp.isoformat()}"
            self.cache[cache_key] = {
                "data": indicator,
                "expire_time": datetime.now() + timedelta(seconds=self.cache_ttl)
            }
            
            # 清理过期缓存
            await self._cleanup_expired_cache()
            
            self.stored_count += 1
            return True
            
        except Exception as e:
            logger.error(f"缓存存储技术指标失败: {e}")
            self.error_count += 1
            return False
    
    async def get_market_data(self, symbol: str, start_time: datetime, end_time: datetime) -> List[MarketData]:
        """从缓存获取市场数据"""
        result = []
        for key, value in self.cache.items():
            if key.startswith(f"market_data:{symbol}:"):
                data = value["data"]
                if start_time <= data.timestamp <= end_time:
                    result.append(data)
        
        return sorted(result, key=lambda x: x.timestamp)
    
    async def get_technical_indicators(self, symbol: str, indicator_name: str, start_time: datetime, end_time: datetime) -> List[TechnicalIndicator]:
        """从缓存获取技术指标"""
        result = []
        for key, value in self.cache.items():
            if key.startswith(f"indicator:{symbol}:{indicator_name}:"):
                data = value["data"]
                if start_time <= data.timestamp <= end_time:
                    result.append(data)
        
        return sorted(result, key=lambda x: x.timestamp)
    
    async def _cleanup_expired_cache(self):
        """清理过期缓存"""
        current_time = datetime.now()
        expired_keys = []
        
        for key, value in self.cache.items():
            if current_time > value["expire_time"]:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.cache[key]
        
        # 如果缓存过大，清理最旧的数据
        if len(self.cache) > self.max_cache_size:
            sorted_items = sorted(self.cache.items(), key=lambda x: x[1]["expire_time"])
            items_to_remove = len(self.cache) - self.max_cache_size
            for i in range(items_to_remove):
                del self.cache[sorted_items[i][0]]
