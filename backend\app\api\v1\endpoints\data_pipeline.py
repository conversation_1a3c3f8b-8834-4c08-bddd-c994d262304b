"""
海天AI纳斯达克交易系统 - 数据管道API端点
基于: 项目手册4.3节数据处理管道设计
创建日期: 2025年8月1日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: 数据管道管理和监控API
"""

import logging
from datetime import datetime, timedelta
from typing import List, Optional
from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect, Depends, Request
from fastapi.responses import JSONResponse

from app.data_pipeline import get_global_pipeline
from app.schemas.market import (
    MarketData, TechnicalIndicator, DataPipelineStatus,
    MarketDataBatch, TechnicalIndicatorBatch, WebSocketMessage
)
from app.core.exceptions import BusinessLogicException

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/status", response_model=DataPipelineStatus)
async def get_pipeline_status():
    """获取数据管道状态"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")
        
        stats = pipeline.get_statistics()
        return DataPipelineStatus(**stats)
        
    except Exception as e:
        logger.error(f"获取数据管道状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@router.get("/market-data/{symbol}", response_model=List[MarketData])
async def get_market_data_history(
    symbol: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100
):
    """获取历史市场数据"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")
        
        # 设置默认时间范围
        if not end_time:
            end_time = datetime.now()
        if not start_time:
            start_time = end_time - timedelta(hours=1)
        
        # 从数据库存储获取历史数据
        if pipeline.database_storage:
            data_list = await pipeline.database_storage.get_market_data(
                symbol, start_time, end_time
            )
            return data_list[:limit]
        else:
            return []
            
    except Exception as e:
        logger.error(f"获取历史市场数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据失败: {str(e)}")


@router.get("/indicators/{symbol}/{indicator_name}", response_model=List[TechnicalIndicator])
async def get_technical_indicators_history(
    symbol: str,
    indicator_name: str,
    start_time: Optional[datetime] = None,
    end_time: Optional[datetime] = None,
    limit: int = 100
):
    """获取历史技术指标数据"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")
        
        # 设置默认时间范围
        if not end_time:
            end_time = datetime.now()
        if not start_time:
            start_time = end_time - timedelta(hours=1)
        
        # 从数据库存储获取历史数据
        if pipeline.database_storage:
            data_list = await pipeline.database_storage.get_technical_indicators(
                symbol, indicator_name, start_time, end_time
            )
            return data_list[:limit]
        else:
            return []
            
    except Exception as e:
        logger.error(f"获取历史技术指标失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取指标失败: {str(e)}")


@router.get("/statistics")
async def get_pipeline_statistics():
    """获取数据管道详细统计信息"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")
        
        stats = {
            "pipeline": pipeline.get_statistics(),
            "collector": pipeline.data_collector.get_statistics() if pipeline.data_collector else {},
            "processor": {
                "market_data": pipeline.market_data_processor.get_statistics() if pipeline.market_data_processor else {},
                "tick_data": pipeline.tick_data_processor.get_statistics() if pipeline.tick_data_processor else {}
            },
            "indicators": {
                name: calc.calculated_count 
                for name, calc in pipeline.technical_indicators.items()
            },
            "distributors": {
                "websocket": pipeline.websocket_distributor.get_statistics() if pipeline.websocket_distributor else {},
                "ai_trader": pipeline.ai_trader_distributor.get_statistics() if pipeline.ai_trader_distributor else {}
            },
            "storage": {
                "database": pipeline.database_storage.get_statistics() if pipeline.database_storage else {},
                "cache": pipeline.cache_storage.get_statistics() if pipeline.cache_storage else {}
            }
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"获取管道统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")


@router.get("/connections")
async def get_websocket_connections():
    """获取WebSocket连接信息"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline or not pipeline.websocket_distributor:
            raise HTTPException(status_code=503, detail="WebSocket分发器未启动")
        
        return pipeline.websocket_distributor.get_connection_info()
        
    except Exception as e:
        logger.error(f"获取连接信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取连接失败: {str(e)}")


@router.post("/ai-trader/subscribe/{trader_id}")
async def subscribe_ai_trader(trader_id: str, request: Request):
    """AI交易员订阅数据"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")
        
        # 这里需要实现AI交易员的回调函数
        # 暂时使用一个简单的回调函数作为示例
        async def trader_callback(data):
            logger.info(f"AI交易员 {trader_id} 收到数据: {type(data).__name__}")
        
        await pipeline.subscribe_ai_trader(trader_id, trader_callback)
        
        return {"message": f"AI交易员 {trader_id} 订阅成功"}
        
    except Exception as e:
        logger.error(f"AI交易员订阅失败: {e}")
        raise HTTPException(status_code=500, detail=f"订阅失败: {str(e)}")


@router.delete("/ai-trader/unsubscribe/{trader_id}")
async def unsubscribe_ai_trader(trader_id: str):
    """AI交易员取消订阅"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")
        
        await pipeline.unsubscribe_ai_trader(trader_id)
        
        return {"message": f"AI交易员 {trader_id} 取消订阅成功"}
        
    except Exception as e:
        logger.error(f"AI交易员取消订阅失败: {e}")
        raise HTTPException(status_code=500, detail=f"取消订阅失败: {str(e)}")


@router.websocket("/ws/{connection_id}")
async def websocket_endpoint(websocket: WebSocket, connection_id: str):
    """WebSocket实时数据推送端点"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            await websocket.close(code=1011, reason="数据管道未启动")
            return
        
        # 添加WebSocket连接
        await pipeline.add_websocket_connection(
            connection_id, 
            websocket, 
            metadata={
                "connected_at": datetime.now().isoformat(),
                "client_info": websocket.headers.get("user-agent", "unknown")
            }
        )
        
        logger.info(f"WebSocket连接已建立: {connection_id}")
        
        try:
            # 保持连接活跃
            while True:
                # 等待客户端消息（心跳或其他控制消息）
                try:
                    message = await websocket.receive_text()
                    logger.debug(f"收到WebSocket消息: {connection_id} - {message}")
                    
                    # 可以在这里处理客户端发送的控制消息
                    # 比如订阅特定标的、设置过滤条件等
                    
                except Exception as e:
                    logger.debug(f"WebSocket接收消息异常: {e}")
                    break
                    
        except WebSocketDisconnect:
            logger.info(f"WebSocket连接断开: {connection_id}")
        except Exception as e:
            logger.error(f"WebSocket连接异常: {connection_id} - {e}")
        finally:
            # 移除连接
            await pipeline.remove_websocket_connection(connection_id)
            logger.info(f"WebSocket连接已清理: {connection_id}")
            
    except Exception as e:
        logger.error(f"WebSocket端点异常: {e}")
        try:
            await websocket.close(code=1011, reason=f"服务器错误: {str(e)}")
        except:
            pass


@router.post("/test/send-data")
async def send_test_data():
    """发送测试数据（仅用于开发测试）"""
    try:
        pipeline = await get_global_pipeline()
        if not pipeline:
            raise HTTPException(status_code=503, detail="数据管道未启动")
        
        # 创建测试市场数据
        test_data = MarketData(
            symbol="159509",
            current_price=2.5,
            open_price=2.48,
            high_price=2.52,
            low_price=2.46,
            volume=1000000,
            timestamp=datetime.now()
        )
        
        # 手动触发数据处理
        await pipeline._handle_raw_data(test_data)
        
        return {"message": "测试数据发送成功", "data": test_data}
        
    except Exception as e:
        logger.error(f"发送测试数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"发送失败: {str(e)}")


# 导出路由
__all__ = ["router"]
