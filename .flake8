# 海天AI纳斯达克交易系统 - Flake8配置
# 基于Python 3.13.2 + FastAPI 0.116.1技术栈
# 创建日期: 2025年7月13日

[flake8]
# 最大行长度（与black保持一致）
max-line-length = 88

# 目标Python版本
python-version = 3.13

# 检查的文件路径
include = backend/app/**/*.py,backend/tests/**/*.py,scripts/**/*.py

# 排除的文件和目录
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    env,
    .env,
    migrations,
    alembic,
    node_modules,
    dist,
    build,
    .pytest_cache,
    .mypy_cache,
    .coverage,
    htmlcov,
    logs,
    data,
    uploads,
    tmp_*,
    temp_*,
    test_*

# 忽略的错误代码
ignore = 
    # E203: 切片中的空白（与black冲突）
    E203,
    # E501: 行太长（由black处理）
    E501,
    # W503: 二元运算符前的换行（与black冲突）
    W503,
    # E231: 逗号后缺少空白（由black处理）
    E231,
    # F401: 导入但未使用（在__init__.py中常见）
    # F401,
    # E402: 模块级导入不在文件顶部
    # E402

# 每个文件的最大复杂度
max-complexity = 12

# 最大认知复杂度
max-cognitive-complexity = 12

# 启用的插件
select = 
    # pycodestyle错误
    E,
    # pycodestyle警告
    W,
    # pyflakes
    F,
    # mccabe复杂度
    C,
    # flake8-bugbear
    B,
    # flake8-comprehensions
    C4,
    # flake8-docstrings
    D,
    # flake8-import-order
    I,
    # flake8-quotes
    Q

# 文档字符串约定
docstring-convention = google

# 导入顺序检查
import-order-style = google
application-import-names = app

# 引号风格
inline-quotes = double
multiline-quotes = double

# 特定错误的配置
per-file-ignores =
    # 测试文件可以有更长的行和更复杂的结构
    backend/tests/**/*.py:E501,C901,D,B011
    # __init__.py文件可以有未使用的导入
    backend/app/**/__init__.py:F401,D104
    # 配置文件可以有更长的行
    backend/app/core/config.py:E501
    # 主入口文件可以忽略某些检查
    backend/app/main.py:D100
    # 脚本文件可以有更宽松的检查
    scripts/**/*.py:D,E501

# 函数名检查
function-naming-style = snake_case

# 变量名检查
variable-naming-style = snake_case

# 类名检查
class-naming-style = PascalCase

# 常量名检查
constant-naming-style = UPPER_CASE

# 模块名检查
module-naming-style = snake_case

# 最大函数长度
max-function-length = 50

# 最大类长度
max-class-length = 200

# 最大模块长度
max-module-length = 1000

# 启用统计信息
statistics = True

# 显示源代码
show-source = True

# 显示pep8错误代码
show-pep8 = True

# 计数错误和警告
count = True

# 基准测试
benchmark = False

# 详细输出
verbose = 0

# 格式化输出
format = %(path)s:%(row)d:%(col)d: %(code)s %(text)s

# 输出文件（可选）
# output-file = flake8-report.txt

# 启用颜色输出（如果终端支持）
color = auto
