{"semi": false, "singleQuote": true, "quoteProps": "as-needed", "trailingComma": "none", "tabWidth": 2, "useTabs": false, "printWidth": 80, "endOfLine": "lf", "arrowParens": "avoid", "bracketSpacing": true, "bracketSameLine": false, "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "proseWrap": "preserve", "requirePragma": false, "vueIndentScriptAndStyle": false, "singleAttributePerLine": false, "overrides": [{"files": "*.vue", "options": {"parser": "vue"}}, {"files": "*.json", "options": {"parser": "json", "trailingComma": "none"}}, {"files": "*.md", "options": {"parser": "markdown", "proseWrap": "always", "printWidth": 100}}, {"files": "*.yaml", "options": {"parser": "yaml", "tabWidth": 2}}, {"files": "*.yml", "options": {"parser": "yaml", "tabWidth": 2}}]}