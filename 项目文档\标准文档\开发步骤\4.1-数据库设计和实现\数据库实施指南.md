# 数据库实施指南

## 文档信息
- **创建日期**: 2025年7月16日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: 海天AI纳斯达克交易系统数据库实施
- **技术基础**: PostgreSQL 17.5 + SQLAlchemy 2.0 + Alembic
- **实施环境**: 开发环境、测试环境、生产环境

---

## 1. 数据库环境准备

### 1.1 PostgreSQL 17.5 安装配置

#### 1.1.1 Windows环境安装
```powershell
# 下载PostgreSQL 17.5安装包
# 从官网下载：https://www.postgresql.org/download/windows/

# 安装配置参数
Port: 5432
Superuser: postgres
Password: [设置强密码]
Locale: Chinese (Simplified)_China.UTF-8
```

#### 1.1.2 基础配置优化
```sql
-- postgresql.conf 关键配置
shared_buffers = 256MB                    # 共享缓冲区
effective_cache_size = 1GB                # 有效缓存大小
work_mem = 4MB                           # 工作内存
maintenance_work_mem = 64MB              # 维护工作内存
checkpoint_completion_target = 0.9       # 检查点完成目标
wal_buffers = 16MB                       # WAL缓冲区
default_statistics_target = 100          # 统计信息目标

-- pg_hba.conf 访问控制
host    all             all             127.0.0.1/32            md5
host    all             all             ::1/128                 md5
```

### 1.2 数据库和用户创建

#### 1.2.1 创建应用数据库
```sql
-- 连接到PostgreSQL
psql -U postgres -h localhost

-- 创建应用数据库
CREATE DATABASE ai_nasdaq_trading
    WITH
    OWNER = postgres
    ENCODING = 'UTF8'
    LC_COLLATE = 'Chinese (Simplified)_China.UTF-8'
    LC_CTYPE = 'Chinese (Simplified)_China.UTF-8'
    TABLESPACE = pg_default
    CONNECTION LIMIT = -1;

-- 创建应用用户
CREATE USER ai_trading_user WITH
    LOGIN
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    INHERIT
    NOREPLICATION
    CONNECTION LIMIT -1
    PASSWORD 'ai_trading_2025!';

-- 授予数据库权限
GRANT CONNECT ON DATABASE ai_nasdaq_trading TO ai_trading_user;
GRANT USAGE ON SCHEMA public TO ai_trading_user;
GRANT CREATE ON SCHEMA public TO ai_trading_user;
```

#### 1.2.2 创建扩展
```sql
-- 连接到应用数据库
\c ai_nasdaq_trading

-- 创建UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建加密扩展
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 创建时间扩展
CREATE EXTENSION IF NOT EXISTS "btree_gist";

-- 验证扩展安装
\dx
```

## 2. 数据库表结构创建

### 2.1 基础表结构SQL脚本

#### 2.1.1 用户权限模块表
```sql
-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_superuser BOOLEAN DEFAULT FALSE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 角色表
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 权限表
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户角色关联表
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    assigned_by UUID REFERENCES users(id),
    UNIQUE(user_id, role_id)
);

-- 角色权限关联表
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by UUID REFERENCES users(id),
    UNIQUE(role_id, permission_id)
);
```

#### 2.1.2 AI交易员模块表
```sql
-- AI交易员基本信息表
CREATE TABLE ai_traders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    model_type VARCHAR(50) NOT NULL,
    model_version VARCHAR(20) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'CREATED',
    allocated_capital DECIMAL(15,2) NOT NULL,
    risk_level VARCHAR(20) DEFAULT 'MEDIUM',
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_allocated_capital_positive CHECK (allocated_capital > 0),
    CONSTRAINT chk_status_valid CHECK (status IN ('CREATED', 'ACTIVE', 'SUSPENDED', 'RETIRED')),
    CONSTRAINT chk_risk_level_valid CHECK (risk_level IN ('LOW', 'MEDIUM', 'HIGH'))
);

-- AI交易员配置表
CREATE TABLE ai_trader_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trader_id UUID NOT NULL REFERENCES ai_traders(id) ON DELETE CASCADE,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT NOT NULL,
    config_type VARCHAR(20) DEFAULT 'STRING',
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(trader_id, config_key),
    CONSTRAINT chk_config_type_valid CHECK (config_type IN ('STRING', 'INTEGER', 'FLOAT', 'BOOLEAN', 'JSON'))
);

-- AI交易员状态表
CREATE TABLE ai_trader_states (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trader_id UUID NOT NULL REFERENCES ai_traders(id) ON DELETE CASCADE,
    work_status VARCHAR(20) NOT NULL DEFAULT 'IDLE',
    health_status VARCHAR(20) NOT NULL DEFAULT 'HEALTHY',
    current_positions INTEGER DEFAULT 0,
    daily_pnl DECIMAL(15,2) DEFAULT 0,
    total_pnl DECIMAL(15,2) DEFAULT 0,
    last_decision_at TIMESTAMP NULL,
    last_trade_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_work_status_valid CHECK (work_status IN ('IDLE', 'ANALYZING', 'TRADING', 'LEARNING', 'ERROR')),
    CONSTRAINT chk_health_status_valid CHECK (health_status IN ('HEALTHY', 'WARNING', 'ERROR', 'OFFLINE'))
);

-- AI交易员档案表
CREATE TABLE ai_trader_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trader_id UUID NOT NULL REFERENCES ai_traders(id) ON DELETE CASCADE,
    profile_data JSONB NOT NULL,
    version INTEGER NOT NULL DEFAULT 1,
    is_current BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(trader_id, version)
);
```

#### 2.1.3 交易数据模块表
```sql
-- 交易订单表
CREATE TABLE trading_orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trader_id UUID NOT NULL REFERENCES ai_traders(id) ON DELETE CASCADE,
    symbol VARCHAR(20) NOT NULL,
    order_type VARCHAR(20) NOT NULL,
    side VARCHAR(10) NOT NULL,
    quantity INTEGER NOT NULL,
    price DECIMAL(10,4) NULL,
    stop_price DECIMAL(10,4) NULL,
    time_in_force VARCHAR(10) DEFAULT 'DAY',
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    filled_quantity INTEGER DEFAULT 0,
    avg_fill_price DECIMAL(10,4) NULL,
    commission DECIMAL(8,2) DEFAULT 0,
    order_reason TEXT,
    external_order_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_quantity_positive CHECK (quantity > 0),
    CONSTRAINT chk_price_positive CHECK (price IS NULL OR price > 0),
    CONSTRAINT chk_stop_price_positive CHECK (stop_price IS NULL OR stop_price > 0),
    CONSTRAINT chk_filled_quantity_valid CHECK (filled_quantity >= 0 AND filled_quantity <= quantity),
    CONSTRAINT chk_order_type_valid CHECK (order_type IN ('MARKET', 'LIMIT', 'STOP', 'STOP_LIMIT')),
    CONSTRAINT chk_side_valid CHECK (side IN ('BUY', 'SELL')),
    CONSTRAINT chk_time_in_force_valid CHECK (time_in_force IN ('DAY', 'GTC', 'IOC', 'FOK')),
    CONSTRAINT chk_status_valid CHECK (status IN ('PENDING', 'SUBMITTED', 'FILLED', 'PARTIALLY_FILLED', 'CANCELLED', 'REJECTED'))
);

-- 交易执行表
CREATE TABLE trading_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES trading_orders(id) ON DELETE CASCADE,
    execution_id VARCHAR(100) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    side VARCHAR(10) NOT NULL,
    quantity INTEGER NOT NULL,
    price DECIMAL(10,4) NOT NULL,
    commission DECIMAL(8,2) DEFAULT 0,
    execution_time TIMESTAMP NOT NULL,
    liquidity_flag VARCHAR(10),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_exec_quantity_positive CHECK (quantity > 0),
    CONSTRAINT chk_exec_price_positive CHECK (price > 0),
    CONSTRAINT chk_exec_side_valid CHECK (side IN ('BUY', 'SELL')),
    CONSTRAINT chk_liquidity_flag_valid CHECK (liquidity_flag IN ('ADDED', 'REMOVED', 'ROUTED'))
);

-- 持仓表
CREATE TABLE positions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trader_id UUID NOT NULL REFERENCES ai_traders(id) ON DELETE CASCADE,
    symbol VARCHAR(20) NOT NULL,
    quantity INTEGER NOT NULL,
    avg_cost DECIMAL(10,4) NOT NULL,
    market_value DECIMAL(15,2) NOT NULL,
    unrealized_pnl DECIMAL(15,2) DEFAULT 0,
    realized_pnl DECIMAL(15,2) DEFAULT 0,
    last_price DECIMAL(10,4) NOT NULL,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(trader_id, symbol),
    CONSTRAINT chk_avg_cost_positive CHECK (avg_cost > 0),
    CONSTRAINT chk_last_price_positive CHECK (last_price > 0)
);

-- 持仓历史表
CREATE TABLE position_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trader_id UUID NOT NULL REFERENCES ai_traders(id) ON DELETE CASCADE,
    symbol VARCHAR(20) NOT NULL,
    quantity INTEGER NOT NULL,
    avg_cost DECIMAL(10,4) NOT NULL,
    market_value DECIMAL(15,2) NOT NULL,
    unrealized_pnl DECIMAL(15,2) DEFAULT 0,
    realized_pnl DECIMAL(15,2) DEFAULT 0,
    snapshot_time TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2.1.4 市场数据模块表
```sql
-- 市场数据表（分区表）
CREATE TABLE market_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(20) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    open_price DECIMAL(10,4) NOT NULL,
    high_price DECIMAL(10,4) NOT NULL,
    low_price DECIMAL(10,4) NOT NULL,
    close_price DECIMAL(10,4) NOT NULL,
    volume INTEGER NOT NULL,
    vwap DECIMAL(10,4),
    data_source VARCHAR(50) DEFAULT 'ALPACA',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_prices_positive CHECK (
        open_price > 0 AND high_price > 0 AND
        low_price > 0 AND close_price > 0
    ),
    CONSTRAINT chk_volume_positive CHECK (volume >= 0),
    CONSTRAINT chk_high_low_valid CHECK (high_price >= low_price)
) PARTITION BY RANGE (timestamp);

-- 技术指标表
CREATE TABLE technical_indicators (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(20) NOT NULL,
    indicator_name VARCHAR(50) NOT NULL,
    indicator_value DECIMAL(15,6) NOT NULL,
    period INTEGER,
    timestamp TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(symbol, indicator_name, period, timestamp)
);

-- 市场快照表
CREATE TABLE market_snapshots (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    snapshot_time TIMESTAMP NOT NULL,
    market_status VARCHAR(20) NOT NULL,
    total_symbols INTEGER NOT NULL,
    snapshot_data JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_total_symbols_positive CHECK (total_symbols > 0),
    CONSTRAINT chk_market_status_valid CHECK (market_status IN ('OPEN', 'CLOSED', 'PRE_MARKET', 'AFTER_HOURS'))
);
```

#### 2.1.5 AI决策和学习模块表
```sql
-- 决策记录表
CREATE TABLE decision_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trader_id UUID NOT NULL REFERENCES ai_traders(id) ON DELETE CASCADE,
    decision_type VARCHAR(20) NOT NULL,
    symbol VARCHAR(20) NOT NULL,
    decision_data JSONB NOT NULL,
    confidence_score DECIMAL(5,4),
    market_conditions JSONB,
    decision_time TIMESTAMP NOT NULL,
    execution_time TIMESTAMP,
    outcome VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_decision_type_valid CHECK (decision_type IN ('BUY', 'SELL', 'HOLD', 'ANALYZE')),
    CONSTRAINT chk_confidence_score_valid CHECK (confidence_score IS NULL OR (confidence_score >= 0 AND confidence_score <= 1)),
    CONSTRAINT chk_outcome_valid CHECK (outcome IS NULL OR outcome IN ('SUCCESS', 'FAILURE', 'PENDING'))
);

-- 学习记录表
CREATE TABLE learning_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trader_id UUID NOT NULL REFERENCES ai_traders(id) ON DELETE CASCADE,
    learning_type VARCHAR(20) NOT NULL,
    input_data JSONB NOT NULL,
    expected_output JSONB,
    actual_output JSONB,
    learning_score DECIMAL(5,4),
    improvement_metrics JSONB,
    learning_time TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_learning_type_valid CHECK (learning_type IN ('SUPERVISED', 'REINFORCEMENT', 'UNSUPERVISED')),
    CONSTRAINT chk_learning_score_valid CHECK (learning_score IS NULL OR (learning_score >= 0 AND learning_score <= 1))
);

-- 绩效指标表
CREATE TABLE performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trader_id UUID NOT NULL REFERENCES ai_traders(id) ON DELETE CASCADE,
    metric_name VARCHAR(50) NOT NULL,
    metric_value DECIMAL(15,6) NOT NULL,
    metric_period VARCHAR(20) NOT NULL,
    calculation_time TIMESTAMP NOT NULL,
    benchmark_value DECIMAL(15,6),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(trader_id, metric_name, metric_period, calculation_time),
    CONSTRAINT chk_metric_period_valid CHECK (metric_period IN ('DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY'))
);
```

#### 2.1.6 系统日志和审计模块表
```sql
-- 系统日志表
CREATE TABLE system_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    log_level VARCHAR(10) NOT NULL,
    logger_name VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    module VARCHAR(50),
    function_name VARCHAR(100),
    line_number INTEGER,
    user_id UUID REFERENCES users(id),
    session_id VARCHAR(100),
    request_id VARCHAR(100),
    additional_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_log_level_valid CHECK (log_level IN ('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'))
);

-- 审计日志表
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    table_name VARCHAR(100) NOT NULL,
    operation VARCHAR(10) NOT NULL,
    record_id UUID NOT NULL,
    old_values JSONB,
    new_values JSONB,
    changed_fields TEXT[],
    user_id UUID REFERENCES users(id),
    session_id VARCHAR(100),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_operation_valid CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE'))
);

-- 风险事件表
CREATE TABLE risk_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    trader_id UUID REFERENCES ai_traders(id) ON DELETE SET NULL,
    event_type VARCHAR(50) NOT NULL,
    risk_level VARCHAR(10) NOT NULL,
    event_description TEXT NOT NULL,
    event_data JSONB,
    status VARCHAR(20) DEFAULT 'OPEN',
    resolution TEXT,
    detected_at TIMESTAMP NOT NULL,
    resolved_at TIMESTAMP,
    created_by UUID REFERENCES users(id),
    resolved_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_risk_level_valid CHECK (risk_level IN ('LOW', 'MEDIUM', 'HIGH', 'CRITICAL')),
    CONSTRAINT chk_status_valid CHECK (status IN ('OPEN', 'INVESTIGATING', 'RESOLVED', 'CLOSED'))
);
```

## 3. 索引创建策略

### 3.1 主键和唯一索引
```sql
-- 主键索引（自动创建）
-- 所有表的id字段都有主键索引

-- 唯一索引
CREATE UNIQUE INDEX uk_users_username ON users(username);
CREATE UNIQUE INDEX uk_users_email ON users(email);
CREATE UNIQUE INDEX uk_ai_traders_name ON ai_traders(name);
CREATE UNIQUE INDEX uk_user_roles_user_role ON user_roles(user_id, role_id);
CREATE UNIQUE INDEX uk_role_permissions_role_perm ON role_permissions(role_id, permission_id);
CREATE UNIQUE INDEX uk_positions_trader_symbol ON positions(trader_id, symbol);
```

### 3.2 外键索引
```sql
-- 用户权限模块外键索引
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON role_permissions(permission_id);

-- AI交易员模块外键索引
CREATE INDEX idx_ai_traders_created_by ON ai_traders(created_by);
CREATE INDEX idx_ai_trader_configs_trader_id ON ai_trader_configs(trader_id);
CREATE INDEX idx_ai_trader_states_trader_id ON ai_trader_states(trader_id);
CREATE INDEX idx_ai_trader_profiles_trader_id ON ai_trader_profiles(trader_id);

-- 交易数据模块外键索引
CREATE INDEX idx_trading_orders_trader_id ON trading_orders(trader_id);
CREATE INDEX idx_trading_executions_order_id ON trading_executions(order_id);
CREATE INDEX idx_positions_trader_id ON positions(trader_id);
CREATE INDEX idx_position_history_trader_id ON position_history(trader_id);

-- AI决策模块外键索引
CREATE INDEX idx_decision_records_trader_id ON decision_records(trader_id);
CREATE INDEX idx_learning_records_trader_id ON learning_records(trader_id);
CREATE INDEX idx_performance_metrics_trader_id ON performance_metrics(trader_id);
```

### 3.3 业务查询索引
```sql
-- 时间相关索引
CREATE INDEX idx_trading_orders_created_at ON trading_orders(created_at);
CREATE INDEX idx_market_data_timestamp ON market_data(timestamp);
CREATE INDEX idx_decision_records_decision_time ON decision_records(decision_time);
CREATE INDEX idx_system_logs_created_at ON system_logs(created_at);

-- 状态查询索引
CREATE INDEX idx_ai_traders_status ON ai_traders(status);
CREATE INDEX idx_trading_orders_status ON trading_orders(status);
CREATE INDEX idx_ai_trader_states_work_status ON ai_trader_states(work_status);
CREATE INDEX idx_risk_events_status ON risk_events(status);

-- 符号查询索引
CREATE INDEX idx_trading_orders_symbol ON trading_orders(symbol);
CREATE INDEX idx_market_data_symbol ON market_data(symbol);
CREATE INDEX idx_positions_symbol ON positions(symbol);
CREATE INDEX idx_technical_indicators_symbol ON technical_indicators(symbol);

-- 复合索引
CREATE INDEX idx_trading_orders_trader_status ON trading_orders(trader_id, status);
CREATE INDEX idx_trading_orders_symbol_status ON trading_orders(symbol, status);
CREATE INDEX idx_market_data_symbol_timestamp ON market_data(symbol, timestamp);
CREATE INDEX idx_decision_records_trader_time ON decision_records(trader_id, decision_time);
```

### 3.4 JSONB索引
```sql
-- AI档案数据GIN索引
CREATE INDEX idx_ai_trader_profiles_data_gin ON ai_trader_profiles USING GIN(profile_data);

-- 特定JSONB字段索引
CREATE INDEX idx_ai_trader_profiles_risk_preference
ON ai_trader_profiles USING GIN((profile_data->'risk_preference'));

CREATE INDEX idx_ai_trader_profiles_trading_style
ON ai_trader_profiles USING GIN((profile_data->'trading_style'));

-- 决策数据索引
CREATE INDEX idx_decision_records_data_gin ON decision_records USING GIN(decision_data);
CREATE INDEX idx_learning_records_input_gin ON learning_records USING GIN(input_data);

-- 市场快照数据索引
CREATE INDEX idx_market_snapshots_data_gin ON market_snapshots USING GIN(snapshot_data);
```

## 4. 数据分区实施

### 4.1 市场数据时间分区
```sql
-- 创建市场数据月度分区
CREATE TABLE market_data_2025_01 PARTITION OF market_data
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE market_data_2025_02 PARTITION OF market_data
FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');

CREATE TABLE market_data_2025_03 PARTITION OF market_data
FOR VALUES FROM ('2025-03-01') TO ('2025-04-01');

-- 创建分区管理函数
CREATE OR REPLACE FUNCTION create_monthly_partition(table_name TEXT, start_date DATE)
RETURNS VOID AS $$
DECLARE
    partition_name TEXT;
    end_date DATE;
BEGIN
    partition_name := table_name || '_' || to_char(start_date, 'YYYY_MM');
    end_date := start_date + INTERVAL '1 month';

    EXECUTE format('CREATE TABLE %I PARTITION OF %I FOR VALUES FROM (%L) TO (%L)',
                   partition_name, table_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;

-- 自动创建未来分区的定时任务
CREATE OR REPLACE FUNCTION maintain_partitions()
RETURNS VOID AS $$
DECLARE
    next_month DATE;
BEGIN
    next_month := date_trunc('month', CURRENT_DATE + INTERVAL '2 months');
    PERFORM create_monthly_partition('market_data', next_month);
END;
$$ LANGUAGE plpgsql;
```

### 4.2 日志表分区策略
```sql
-- 系统日志按月分区
CREATE TABLE system_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    log_level VARCHAR(10) NOT NULL,
    logger_name VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    -- 其他字段...
) PARTITION BY RANGE (created_at);

-- 创建系统日志分区
CREATE TABLE system_logs_2025_01 PARTITION OF system_logs
FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

CREATE TABLE system_logs_2025_02 PARTITION OF system_logs
FOR VALUES FROM ('2025-02-01') TO ('2025-03-01');
```

## 5. 初始数据插入

### 5.1 基础权限数据
```sql
-- 插入基础角色
INSERT INTO roles (id, name, description) VALUES
(uuid_generate_v4(), 'SUPER_ADMIN', '超级管理员，拥有所有权限'),
(uuid_generate_v4(), 'ADMIN', '系统管理员，拥有管理权限'),
(uuid_generate_v4(), 'TRADER_MANAGER', '交易员管理者，可以管理AI交易员'),
(uuid_generate_v4(), 'ANALYST', '分析师，可以查看数据和报告'),
(uuid_generate_v4(), 'VIEWER', '观察者，只能查看基础信息');

-- 插入基础权限
INSERT INTO permissions (id, name, resource, action, description) VALUES
(uuid_generate_v4(), 'USER_CREATE', 'user', 'create', '创建用户'),
(uuid_generate_v4(), 'USER_READ', 'user', 'read', '查看用户'),
(uuid_generate_v4(), 'USER_UPDATE', 'user', 'update', '更新用户'),
(uuid_generate_v4(), 'USER_DELETE', 'user', 'delete', '删除用户'),
(uuid_generate_v4(), 'TRADER_CREATE', 'ai_trader', 'create', '创建AI交易员'),
(uuid_generate_v4(), 'TRADER_READ', 'ai_trader', 'read', '查看AI交易员'),
(uuid_generate_v4(), 'TRADER_UPDATE', 'ai_trader', 'update', '更新AI交易员'),
(uuid_generate_v4(), 'TRADER_DELETE', 'ai_trader', 'delete', '删除AI交易员'),
(uuid_generate_v4(), 'TRADING_READ', 'trading', 'read', '查看交易数据'),
(uuid_generate_v4(), 'TRADING_EXECUTE', 'trading', 'execute', '执行交易'),
(uuid_generate_v4(), 'MARKET_DATA_READ', 'market_data', 'read', '查看市场数据'),
(uuid_generate_v4(), 'REPORT_READ', 'report', 'read', '查看报告');

-- 创建默认超级管理员用户
INSERT INTO users (id, username, email, password_hash, full_name, is_superuser) VALUES
(uuid_generate_v4(), 'admin', '<EMAIL>',
 crypt('admin123', gen_salt('bf')), '系统管理员', TRUE);
```

### 5.2 AI交易员配置模板
```sql
-- 插入AI交易员配置模板
INSERT INTO ai_trader_configs (trader_id, config_key, config_value, config_type, description)
SELECT
    t.id,
    'max_position_size',
    '1000',
    'INTEGER',
    '最大持仓数量'
FROM ai_traders t;

INSERT INTO ai_trader_configs (trader_id, config_key, config_value, config_type, description)
SELECT
    t.id,
    'risk_tolerance',
    '0.02',
    'FLOAT',
    '风险容忍度（2%）'
FROM ai_traders t;

INSERT INTO ai_trader_configs (trader_id, config_key, config_value, config_type, description)
SELECT
    t.id,
    'trading_hours',
    '{"start": "09:30", "end": "16:00", "timezone": "US/Eastern"}',
    'JSON',
    '交易时间配置'
FROM ai_traders t;
```

### 5.3 技术指标配置
```sql
-- 插入常用技术指标配置
INSERT INTO technical_indicators (symbol, indicator_name, indicator_value, period, timestamp) VALUES
('AAPL', 'SMA', 150.25, 20, CURRENT_TIMESTAMP),
('AAPL', 'EMA', 149.80, 20, CURRENT_TIMESTAMP),
('AAPL', 'RSI', 65.5, 14, CURRENT_TIMESTAMP),
('AAPL', 'MACD', 1.25, 12, CURRENT_TIMESTAMP),
('MSFT', 'SMA', 280.15, 20, CURRENT_TIMESTAMP),
('MSFT', 'EMA', 279.90, 20, CURRENT_TIMESTAMP),
('MSFT', 'RSI', 58.2, 14, CURRENT_TIMESTAMP),
('MSFT', 'MACD', 2.15, 12, CURRENT_TIMESTAMP);
```

## 6. 数据库迁移方案

### 6.1 Alembic配置
```python
# alembic.ini 配置文件
[alembic]
script_location = alembic
prepend_sys_path = .
version_path_separator = os
sqlalchemy.url = postgresql://ai_trading_user:ai_trading_2025!@localhost:5432/ai_nasdaq_trading

[post_write_hooks]
hooks = black
black.type = console_scripts
black.entrypoint = black
black.options = -l 79 REVISION_SCRIPT_FILENAME

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S
```