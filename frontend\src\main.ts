/**
 * 海天AI纳斯达克交易系统 - 前端主入口
 * 基于: 项目手册4.1节MVP版本技术栈配置
 * 创建日期: 2025年7月13日
 * 技术栈: Vue.js 3.5.x + TypeScript + Vite + Element Plus
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import App from './App.vue'
import router from './router'

// 创建Vue应用实例
const app = createApp(App)

// 使用Pinia状态管理
app.use(createPinia())

// 使用Vue Router路由
app.use(router)

// 使用Element Plus UI组件库
app.use(ElementPlus)

// 挂载应用
app.mount('#app')
