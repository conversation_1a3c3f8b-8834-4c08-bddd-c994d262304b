[pytest]
# pytest配置文件
minversion = 6.0
addopts = 
    -ra
    --strict-markers
    --strict-config
    --disable-warnings
    -v
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: 单元测试
    integration: 集成测试
    slow: 慢速测试
    auth: 认证相关测试
    trading: 交易相关测试
    monitoring: 监控相关测试
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
