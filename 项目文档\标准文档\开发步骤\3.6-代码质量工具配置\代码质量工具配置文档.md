# 海天AI纳斯达克交易系统 - 代码质量工具配置文档

## 文档信息

| 项目 | 内容 |
|------|------|
| **创建日期** | 2025年7月13日 |
| **维护团队** | 海天AI开发团队 |
| **文档范围** | 代码质量工具配置与使用指南 |
| **技术基础** | Python 3.13.2 + FastAPI 0.116.1 + Vue.js 3.5.x + TypeScript |

## 版本更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| v1.0 | 2025-07-13 | 初始版本，完整的代码质量工具配置 | 海天AI开发团队 |

## 目录

- [1. 概述](#1-概述)
- [2. 后端Python代码质量工具](#2-后端python代码质量工具)
- [3. 前端代码质量工具](#3-前端代码质量工具)
- [4. 预提交钩子配置](#4-预提交钩子配置)
- [5. 安装和使用指南](#5-安装和使用指南)
- [6. 故障排除](#6-故障排除)

## 1. 概述

本文档详细说明了海天AI纳斯达克交易系统的代码质量工具配置，包括Python后端和Vue.js前端的静态代码分析、格式化、类型检查和安全扫描工具。

### 1.1 工具架构

```
代码质量工具体系
├── 后端Python工具
│   ├── Black (代码格式化)
│   ├── isort (导入排序)
│   ├── Flake8 (代码风格检查)
│   ├── MyPy (类型检查)
│   └── Bandit (安全扫描)
├── 前端工具
│   ├── ESLint (代码检查)
│   ├── Prettier (代码格式化)
│   ├── Stylelint (CSS检查)
│   └── Husky (Git钩子)
└── 统一工具
    └── Pre-commit (预提交钩子)
```

### 1.2 配置文件结构

```
项目根目录/
├── .pre-commit-config.yaml     # 预提交钩子配置
├── .flake8                     # Flake8配置
├── bandit.yaml                 # Bandit安全扫描配置
├── backend/
│   └── pyproject.toml          # Python项目配置
└── frontend/
    ├── .eslintrc.js            # ESLint配置
    ├── .prettierrc             # Prettier配置
    ├── .stylelintrc.js         # Stylelint配置
    └── package.json            # Node.js项目配置
```

## 2. 后端Python代码质量工具

### 2.1 Black - 代码格式化工具

**功能**: 自动格式化Python代码，确保代码风格一致性

**配置位置**: `backend/pyproject.toml`

**主要配置**:
```toml
[tool.black]
line-length = 88
target-version = ['py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  migrations
  | alembic
  | .venv
  | venv
)/
'''
```

**使用方法**:
```bash
# 格式化单个文件
black app/main.py

# 格式化整个项目
black .

# 检查格式但不修改
black --check .

# 显示差异
black --diff .
```

### 2.2 isort - 导入排序工具

**功能**: 自动排序和组织Python导入语句

**配置位置**: `backend/pyproject.toml`

**主要配置**:
```toml
[tool.isort]
profile = "black"
line_length = 88
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
```

**使用方法**:
```bash
# 排序导入
isort app/

# 检查但不修改
isort --check-only app/

# 显示差异
isort --diff app/
```

### 2.3 Flake8 - 代码风格检查

**功能**: 检查代码风格、语法错误和复杂度

**配置位置**: `.flake8`

**主要配置**:
```ini
[flake8]
max-line-length = 88
max-complexity = 12
ignore = E203,E501,W503
exclude = .git,__pycache__,.venv,migrations
```

**使用方法**:
```bash
# 检查代码
flake8 app/

# 检查特定文件
flake8 app/main.py

# 生成报告
flake8 --format=html --htmldir=flake8-report app/
```

### 2.4 MyPy - 类型检查

**功能**: 静态类型检查，提高代码质量和可维护性

**配置位置**: `backend/pyproject.toml`

**主要配置**:
```toml
[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
```

**使用方法**:
```bash
# 类型检查
mypy app/

# 生成报告
mypy --html-report mypy-report app/
```

### 2.5 Bandit - 安全扫描

**功能**: 扫描Python代码中的安全漏洞

**配置位置**: `bandit.yaml`

**使用方法**:
```bash
# 安全扫描
bandit -r app/

# 使用配置文件
bandit -c bandit.yaml -r app/

# 生成JSON报告
bandit -f json -o bandit-report.json -r app/
```

## 3. 前端代码质量工具

### 3.1 ESLint - JavaScript/TypeScript检查

**功能**: 检查JavaScript和TypeScript代码质量和风格

**配置位置**: `frontend/.eslintrc.js`

**使用方法**:
```bash
# 检查代码
npm run lint

# 自动修复
npm run lint -- --fix

# 检查特定文件
npx eslint src/components/
```

### 3.2 Prettier - 代码格式化

**功能**: 自动格式化前端代码

**配置位置**: `frontend/.prettierrc`

**使用方法**:
```bash
# 格式化代码
npm run format

# 检查格式
npx prettier --check src/

# 格式化特定文件
npx prettier --write src/components/
```

### 3.3 Stylelint - CSS样式检查

**功能**: 检查CSS/SCSS代码质量和风格

**配置位置**: `frontend/.stylelintrc.js`

**使用方法**:
```bash
# 检查样式
npm run lint:style

# 自动修复
npm run lint:style -- --fix
```

## 4. 预提交钩子配置

### 4.1 Pre-commit 配置

**配置位置**: `.pre-commit-config.yaml`

**功能**: 在Git提交前自动运行代码质量检查

**安装方法**:
```bash
# 安装pre-commit
pip install pre-commit

# 安装钩子
pre-commit install

# 手动运行所有钩子
pre-commit run --all-files
```

### 4.2 钩子执行流程

1. **Python代码检查**
   - Black格式化
   - isort导入排序
   - Flake8风格检查
   - MyPy类型检查
   - Bandit安全扫描

2. **前端代码检查**
   - ESLint检查
   - Prettier格式化
   - Stylelint样式检查

3. **通用检查**
   - 文件大小检查
   - 合并冲突检查
   - YAML/JSON语法检查
   - 私钥检测

## 5. 安装和使用指南

### 5.1 环境要求

- Python 3.13+
- Node.js 18+
- Git 2.0+

### 5.2 快速安装

**使用Python脚本**:
```bash
python scripts/setup_code_quality.py
```

**使用PowerShell脚本**:
```powershell
.\scripts\setup_code_quality.ps1
```

### 5.3 手动安装

**后端工具安装**:
```bash
cd backend
pip install black isort flake8 mypy bandit pre-commit
```

**前端工具安装**:
```bash
cd frontend
npm install
```

### 5.4 日常使用

**提交代码前**:
```bash
# 自动运行（通过pre-commit钩子）
git commit -m "your message"

# 手动运行检查
pre-commit run --all-files
```

**开发过程中**:
```bash
# 后端代码格式化
cd backend && black . && isort .

# 前端代码格式化
cd frontend && npm run format && npm run lint
```

## 6. 故障排除

### 6.1 常见问题

**问题1**: Pre-commit钩子执行失败
```bash
# 解决方案：重新安装钩子
pre-commit clean
pre-commit install
```

**问题2**: Python工具版本冲突
```bash
# 解决方案：使用虚拟环境
conda activate AI_Nasdaq_trading
pip install --upgrade black isort flake8 mypy bandit
```

**问题3**: 前端工具安装失败
```bash
# 解决方案：清理缓存重新安装
cd frontend
rm -rf node_modules package-lock.json
npm install
```

### 6.2 配置验证

**验证工具安装**:
```bash
# 检查Python工具
black --version
isort --version
flake8 --version
mypy --version
bandit --version

# 检查前端工具
cd frontend
npx eslint --version
npx prettier --version
npx stylelint --version
```

**验证配置文件**:
```bash
# 检查配置语法
pre-commit validate-config
```

### 6.3 性能优化

**加速检查**:
```bash
# 只检查修改的文件
pre-commit run --files $(git diff --cached --name-only)

# 并行执行
pre-commit run --all-files --parallel
```

---

**文档维护**: 本文档应随着工具版本更新和配置变更及时更新。如有问题请联系海天AI开发团队。
