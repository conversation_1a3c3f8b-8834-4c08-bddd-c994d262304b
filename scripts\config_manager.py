#!/usr/bin/env python3
"""
配置管理命令行工具

用于验证、管理和维护AI交易系统的配置
"""

import os
import sys
import asyncio
import argparse
import json
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.app.core.config import config_manager, validate_environment
from backend.app.core.config_validator import validate_configuration, generate_config_report


def setup_argparse() -> argparse.ArgumentParser:
    """设置命令行参数解析"""
    parser = argparse.ArgumentParser(
        description="AI交易系统配置管理工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python scripts/config_manager.py validate              # 验证所有配置
  python scripts/config_manager.py check-env             # 检查环境变量
  python scripts/config_manager.py report                # 生成配置报告
  python scripts/config_manager.py create-env            # 创建.env文件
  python scripts/config_manager.py test-connections      # 测试外部连接
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 验证配置命令
    validate_parser = subparsers.add_parser('validate', help='验证所有配置')
    validate_parser.add_argument('--json', action='store_true', help='以JSON格式输出结果')
    validate_parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    # 检查环境变量命令
    env_parser = subparsers.add_parser('check-env', help='检查环境变量')
    env_parser.add_argument('--list-missing', action='store_true', help='列出缺失的环境变量')
    
    # 生成报告命令
    report_parser = subparsers.add_parser('report', help='生成配置报告')
    report_parser.add_argument('--output', '-o', help='输出文件路径')
    
    # 创建.env文件命令
    create_env_parser = subparsers.add_parser('create-env', help='从模板创建.env文件')
    create_env_parser.add_argument('--force', action='store_true', help='强制覆盖现有文件')
    
    # 测试连接命令
    test_parser = subparsers.add_parser('test-connections', help='测试外部服务连接')
    test_parser.add_argument('--service', choices=['database', 'redis', 'all'], 
                           default='all', help='指定要测试的服务')
    
    return parser


async def cmd_validate(args) -> int:
    """验证配置命令"""
    print("🔍 开始验证配置...")
    
    try:
        results = await validate_configuration()
        
        if args.json:
            print(json.dumps(results, indent=2, ensure_ascii=False))
            return 0 if results['overall_status'] == 'success' else 1
        
        # 格式化输出
        status_emoji = {
            'success': '✅',
            'warning': '⚠️',
            'failed': '❌',
            'error': '❌'
        }
        
        overall_status = results.get('overall_status', 'unknown')
        print(f"\n{status_emoji.get(overall_status, '❓')} 总体状态: {overall_status.upper()}")
        
        # 显示摘要
        summary = results.get('summary', {})
        print(f"📊 验证摘要:")
        print(f"   - 总验证项: {summary.get('total_validations', 0)}")
        print(f"   - 成功: {summary.get('successes', 0)}")
        print(f"   - 警告: {summary.get('warnings', 0)}")
        print(f"   - 错误: {summary.get('errors', 0)}")
        
        # 显示详细结果
        if args.verbose:
            print(f"\n📋 详细结果:")
            for name, result in results.get('validations', {}).items():
                status = result.get('status', 'unknown')
                emoji = status_emoji.get(status, '❓')
                print(f"   {emoji} {name}: {result.get('message', 'No message')}")
        
        # 显示错误和警告
        errors = results.get('errors', [])
        if errors:
            print(f"\n❌ 错误:")
            for error in errors:
                print(f"   - {error}")
        
        warnings = results.get('warnings', [])
        if warnings:
            print(f"\n⚠️ 警告:")
            for warning in warnings:
                print(f"   - {warning}")
        
        return 0 if overall_status == 'success' else 1
        
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        return 1


def cmd_check_env(args) -> int:
    """检查环境变量命令"""
    print("🔍 检查环境变量...")
    
    required_vars = [
        "APP_DB_PASSWORD",
        "SECRET_KEY"
    ]

    optional_vars = [
        "AI_MODEL_API_KEY",
        "ANTHROPIC_API_KEY",
        "GOOGLE_API_KEY",
        "REDIS_PASSWORD",
        "ENCRYPTION_KEY",
        "QMT_USERNAME",
        "QMT_PASSWORD",
        "QMT_ACCOUNT_ID"
    ]
    
    missing_required = []
    missing_optional = []
    present_vars = []
    
    # 检查必需变量
    for var in required_vars:
        if os.getenv(var):
            present_vars.append(var)
        else:
            missing_required.append(var)
    
    # 检查可选变量
    for var in optional_vars:
        if os.getenv(var):
            present_vars.append(var)
        else:
            missing_optional.append(var)
    
    # 输出结果
    print(f"✅ 已配置变量 ({len(present_vars)}):")
    for var in present_vars:
        value = os.getenv(var, "")
        masked_value = "*" * min(len(value), 8) if value else ""
        print(f"   - {var}: {masked_value}")
    
    if missing_required:
        print(f"\n❌ 缺失必需变量 ({len(missing_required)}):")
        for var in missing_required:
            print(f"   - {var}")
    
    if missing_optional:
        print(f"\n⚠️ 缺失可选变量 ({len(missing_optional)}):")
        for var in missing_optional:
            print(f"   - {var}")
    
    if args.list_missing and (missing_required or missing_optional):
        print(f"\n📝 需要配置的变量:")
        for var in missing_required + missing_optional:
            print(f"export {var}=your_value_here")
    
    return 0 if not missing_required else 1


def cmd_report(args) -> int:
    """生成配置报告命令"""
    print("📊 生成配置报告...")
    
    try:
        report = generate_config_report()
        
        if args.output:
            output_path = Path(args.output)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"✅ 报告已保存到: {output_path}")
        else:
            print(report)
        
        return 0
        
    except Exception as e:
        print(f"❌ 生成报告失败: {e}")
        return 1


def cmd_create_env(args) -> int:
    """创建.env文件命令"""
    print("📝 创建.env文件...")
    
    env_path = Path(".env")
    template_path = Path(".env.template")
    
    # 检查模板文件
    if not template_path.exists():
        print(f"❌ 模板文件不存在: {template_path}")
        return 1
    
    # 检查现有文件
    if env_path.exists() and not args.force:
        print(f"⚠️ .env文件已存在，使用 --force 强制覆盖")
        return 1
    
    try:
        # 复制模板文件
        with open(template_path, 'r', encoding='utf-8') as template_file:
            content = template_file.read()
        
        with open(env_path, 'w', encoding='utf-8') as env_file:
            env_file.write(content)
        
        print(f"✅ .env文件已创建")
        print(f"📝 请编辑 {env_path} 并填写实际配置值")
        print(f"⚠️ 请确保 .env 文件不被提交到版本控制系统")
        
        return 0
        
    except Exception as e:
        print(f"❌ 创建.env文件失败: {e}")
        return 1


async def cmd_test_connections(args) -> int:
    """测试连接命令"""
    print("🔗 测试外部服务连接...")
    
    try:
        # 导入验证器
        from backend.app.core.config_validator import ConfigValidator
        
        settings = config_manager.settings
        validator = ConfigValidator(settings)
        
        tests = []
        if args.service in ['database', 'all']:
            tests.append(('数据库', validator._validate_database_connection))
        if args.service in ['redis', 'all']:
            tests.append(('Redis', validator._validate_redis_connection))
        
        all_passed = True
        
        for name, test_func in tests:
            print(f"\n🔍 测试 {name} 连接...")
            try:
                result = await test_func()
                if result['status'] == 'success':
                    print(f"✅ {name} 连接成功")
                    if 'connection_details' in result:
                        details = result['connection_details']
                        for key, value in details.items():
                            print(f"   - {key}: {value}")
                else:
                    print(f"❌ {name} 连接失败: {result.get('message', 'Unknown error')}")
                    all_passed = False
            except Exception as e:
                print(f"❌ {name} 连接测试异常: {e}")
                all_passed = False
        
        return 0 if all_passed else 1
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return 1


async def main():
    """主函数"""
    parser = setup_argparse()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    # 设置工作目录为项目根目录
    os.chdir(project_root)
    
    try:
        if args.command == 'validate':
            return await cmd_validate(args)
        elif args.command == 'check-env':
            return cmd_check_env(args)
        elif args.command == 'report':
            return cmd_report(args)
        elif args.command == 'create-env':
            return cmd_create_env(args)
        elif args.command == 'test-connections':
            return await cmd_test_connections(args)
        else:
            print(f"❌ 未知命令: {args.command}")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 操作被用户中断")
        return 1
    except Exception as e:
        print(f"❌ 执行命令时发生错误: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
