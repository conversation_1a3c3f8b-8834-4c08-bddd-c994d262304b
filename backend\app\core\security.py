"""
海天AI纳斯达克交易系统 - 安全模块
基于: 项目手册4.1节MVP版本技术栈配置
创建日期: 2025年7月31日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: JWT认证、密码哈希、安全工具函数
"""

from datetime import datetime, timedelta, timezone
from typing import Any, Union, Optional
import jwt
from passlib.context import CryptContext
from passlib.hash import bcrypt
from fastapi import HTTPException, status
from fastapi.security import HTTP<PERSON>earer, HTTPAuthorizationCredentials
from pydantic import BaseModel

from app.core.settings import settings

# =============================================================================
# 密码哈希配置
# =============================================================================

# 密码上下文配置，使用bcrypt算法
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证密码
    
    Args:
        plain_password: 明文密码
        hashed_password: 哈希密码
        
    Returns:
        bool: 密码是否匹配
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    生成密码哈希
    
    Args:
        password: 明文密码
        
    Returns:
        str: 哈希后的密码
    """
    return pwd_context.hash(password)


# =============================================================================
# JWT Token配置
# =============================================================================

class TokenData(BaseModel):
    """Token数据模型"""
    username: Optional[str] = None
    user_id: Optional[int] = None
    scopes: list[str] = []


class Token(BaseModel):
    """Token响应模型"""
    access_token: str
    token_type: str
    expires_in: int


def create_access_token(
    data: dict, 
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    创建访问令牌
    
    Args:
        data: 要编码的数据
        expires_delta: 过期时间增量
        
    Returns:
        str: JWT访问令牌
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=settings.security.access_token_expire_minutes
        )
    
    to_encode.update({"exp": expire})
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.security.secret_key, 
        algorithm=settings.security.algorithm
    )
    
    return encoded_jwt


def create_refresh_token(
    data: dict, 
    expires_delta: Optional[timedelta] = None
) -> str:
    """
    创建刷新令牌
    
    Args:
        data: 要编码的数据
        expires_delta: 过期时间增量
        
    Returns:
        str: JWT刷新令牌
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            days=settings.security.refresh_token_expire_days
        )
    
    to_encode.update({"exp": expire, "type": "refresh"})
    
    encoded_jwt = jwt.encode(
        to_encode, 
        settings.security.secret_key, 
        algorithm=settings.security.algorithm
    )
    
    return encoded_jwt


def verify_token(token: str) -> dict:
    """
    验证JWT令牌
    
    Args:
        token: JWT令牌
        
    Returns:
        dict: 解码后的数据
        
    Raises:
        HTTPException: 令牌无效或过期
    """
    try:
        payload = jwt.decode(
            token, 
            settings.security.secret_key, 
            algorithms=[settings.security.algorithm]
        )
        return payload
    except jwt.ExpiredSignatureError:
        from .exceptions import AuthenticationException
        raise AuthenticationException("Token已过期")
    except jwt.InvalidTokenError:
        from .exceptions import AuthenticationException
        raise AuthenticationException("无效的Token")


def decode_token(token: str) -> TokenData:
    """
    解码JWT令牌并返回TokenData
    
    Args:
        token: JWT令牌
        
    Returns:
        TokenData: 令牌数据
        
    Raises:
        HTTPException: 令牌无效
    """
    payload = verify_token(token)
    
    username: str = payload.get("sub")
    user_id: int = payload.get("user_id")
    scopes: list[str] = payload.get("scopes", [])
    
    if username is None:
        from .exceptions import AuthenticationException
        raise AuthenticationException("无效的Token数据")
    
    return TokenData(username=username, user_id=user_id, scopes=scopes)


# =============================================================================
# 安全工具函数
# =============================================================================

def generate_api_key() -> str:
    """
    生成API密钥
    
    Returns:
        str: 32位随机API密钥
    """
    import secrets
    return secrets.token_urlsafe(32)


def validate_api_key(api_key: str) -> bool:
    """
    验证API密钥格式
    
    Args:
        api_key: API密钥
        
    Returns:
        bool: 密钥格式是否有效
    """
    if not api_key or len(api_key) < 32:
        return False
    
    # 检查是否包含有效字符
    import string
    valid_chars = string.ascii_letters + string.digits + '-_'
    return all(c in valid_chars for c in api_key)


def check_password_strength(password: str) -> dict:
    """
    检查密码强度
    
    Args:
        password: 密码
        
    Returns:
        dict: 密码强度检查结果
    """
    result = {
        "is_valid": True,
        "score": 0,
        "messages": []
    }
    
    if len(password) < 8:
        result["is_valid"] = False
        result["messages"].append("密码长度至少8位")
    else:
        result["score"] += 1
    
    if not any(c.isupper() for c in password):
        result["messages"].append("建议包含大写字母")
    else:
        result["score"] += 1
    
    if not any(c.islower() for c in password):
        result["messages"].append("建议包含小写字母")
    else:
        result["score"] += 1
    
    if not any(c.isdigit() for c in password):
        result["messages"].append("建议包含数字")
    else:
        result["score"] += 1
    
    if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
        result["messages"].append("建议包含特殊字符")
    else:
        result["score"] += 1

    # 判断密码强度
    result["is_strong"] = result["score"] >= 4 and result["is_valid"]

    return result


# =============================================================================
# HTTP Bearer认证
# =============================================================================

security = HTTPBearer()

def get_current_user_token(
    credentials: HTTPAuthorizationCredentials = security
) -> str:
    """
    获取当前用户的JWT令牌
    
    Args:
        credentials: HTTP认证凭据
        
    Returns:
        str: JWT令牌
        
    Raises:
        HTTPException: 认证失败
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="缺少认证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return credentials.credentials
