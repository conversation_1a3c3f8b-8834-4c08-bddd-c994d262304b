"""
海天AI纳斯达克交易系统 - API v1 路由汇总
基于: 项目手册4.1节MVP版本技术栈配置
创建日期: 2025年7月31日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: API v1版本路由汇总和管理
"""

from fastapi import APIRouter

from app.api.v1.endpoints import auth, traders, trading, monitoring, data_pipeline

# 创建API v1路由器
api_router = APIRouter()

# =============================================================================
# 路由注册
# =============================================================================

# 认证相关路由
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["认证管理"]
)

# AI交易员管理路由
api_router.include_router(
    traders.router,
    prefix="/traders",
    tags=["AI交易员管理"]
)

# 交易执行路由
api_router.include_router(
    trading.router,
    prefix="/trading",
    tags=["交易执行"]
)

# 监控和统计路由
api_router.include_router(
    monitoring.router,
    prefix="/monitoring",
    tags=["监控统计"]
)

# 数据管道路由
api_router.include_router(
    data_pipeline.router,
    prefix="/data-pipeline",
    tags=["数据管道"]
)

# =============================================================================
# API信息端点
# =============================================================================

@api_router.get("/info", summary="获取API信息")
async def get_api_info():
    """
    获取API v1版本信息和功能列表
    
    Returns:
        dict: API信息
    """
    return {
        "api_version": "v1",
        "title": "海天AI纳斯达克交易系统API",
        "description": "AI驱动的纳斯达克实时量化交易系统API",
        "features": [
            "用户认证和授权管理",
            "AI交易员创建和管理",
            "实时交易执行和监控",
            "交易数据分析和统计",
            "风险控制和预警系统",
            "系统性能监控",
            "实时数据处理管道",
            "技术指标计算和分发"
        ],
        "endpoints": {
            "auth": {
                "description": "用户认证和授权",
                "prefix": "/api/v1/auth",
                "endpoints": [
                    "POST /login - 用户登录",
                    "POST /logout - 用户登出",
                    "POST /refresh - 刷新令牌",
                    "GET /me - 获取当前用户信息"
                ]
            },
            "traders": {
                "description": "AI交易员管理",
                "prefix": "/api/v1/traders",
                "endpoints": [
                    "GET / - 获取交易员列表",
                    "POST / - 创建新交易员",
                    "GET /{trader_id} - 获取交易员详情",
                    "PUT /{trader_id} - 更新交易员配置",
                    "DELETE /{trader_id} - 删除交易员",
                    "POST /{trader_id}/start - 启动交易员",
                    "POST /{trader_id}/stop - 停止交易员"
                ]
            },
            "trading": {
                "description": "交易执行和管理",
                "prefix": "/api/v1/trading",
                "endpoints": [
                    "GET /positions - 获取持仓信息",
                    "GET /orders - 获取订单历史",
                    "POST /orders - 手动下单",
                    "DELETE /orders/{order_id} - 撤销订单",
                    "GET /market-data - 获取市场数据",
                    "GET /performance - 获取交易绩效"
                ]
            },
            "monitoring": {
                "description": "系统监控和统计",
                "prefix": "/api/v1/monitoring",
                "endpoints": [
                    "GET /system-status - 获取系统状态",
                    "GET /performance-metrics - 获取性能指标",
                    "GET /risk-metrics - 获取风险指标",
                    "GET /trading-statistics - 获取交易统计",
                    "GET /logs - 获取系统日志"
                ]
            },
            "data_pipeline": {
                "description": "数据处理管道",
                "prefix": "/api/v1/data-pipeline",
                "endpoints": [
                    "GET /status - 获取管道状态",
                    "GET /market-data/{symbol} - 获取历史市场数据",
                    "GET /indicators/{symbol}/{indicator_name} - 获取技术指标",
                    "GET /statistics - 获取管道统计信息",
                    "GET /connections - 获取WebSocket连接信息",
                    "POST /ai-trader/subscribe/{trader_id} - AI交易员订阅",
                    "DELETE /ai-trader/unsubscribe/{trader_id} - 取消订阅",
                    "WebSocket /ws/{connection_id} - 实时数据推送"
                ]
            }
        },
        "authentication": {
            "type": "Bearer Token (JWT)",
            "description": "使用JWT令牌进行身份验证",
            "header": "Authorization: Bearer <token>"
        },
        "rate_limiting": {
            "enabled": True,
            "default_limit": "100 requests per minute",
            "description": "API请求频率限制"
        }
    }


@api_router.get("/health", summary="API健康检查")
async def api_health_check():
    """
    API v1健康检查端点
    
    Returns:
        dict: 健康状态信息
    """
    return {
        "status": "healthy",
        "version": "v1",
        "services": {
            "auth": "operational",
            "traders": "operational",
            "trading": "operational",
            "monitoring": "operational",
            "data_pipeline": "operational"
        },
        "message": "API v1 is running normally"
    }
