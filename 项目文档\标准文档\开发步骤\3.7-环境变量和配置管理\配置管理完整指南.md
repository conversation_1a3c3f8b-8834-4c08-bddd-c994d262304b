# 配置管理完整指南

## 文档信息
- **创建日期**: 2025年7月13日
- **最后更新**: 2025年7月13日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: AI交易系统配置管理和环境变量管理
- **技术基础**: FastAPI + Pydantic + YAML + 环境变量

*本指南提供AI交易系统配置管理的完整说明，包括环境变量、配置文件、安全管理等各个方面。*

---

## 目录

1. [配置管理概述](#1-配置管理概述)
2. [环境变量模板说明](#2-环境变量模板说明)
3. [配置文件说明](#3-配置文件说明)
4. [密钥配置和安全要求](#4-密钥配置和安全要求)
5. [配置分层策略](#5-配置分层策略)
6. [配置验证和测试](#6-配置验证和测试)
7. [配置部署和更新流程](#7-配置部署和更新流程)
8. [故障排除和恢复](#8-故障排除和恢复)

---

# 1. 配置管理概述

## 1.1 设计理念

AI交易系统采用分层配置管理策略，确保：
- **安全性**: 敏感信息通过环境变量管理，不存储在代码中
- **灵活性**: 支持多种配置源，环境变量优先级最高
- **可维护性**: 配置结构清晰，易于理解和修改
- **可验证性**: 完整的配置验证机制，确保系统启动前配置正确

## 1.2 配置架构

```
配置管理架构
├── 环境变量 (.env)           # 最高优先级，敏感信息
├── YAML配置文件 (config/)    # 结构化配置，非敏感信息
├── 默认配置 (代码中)         # 兜底配置，确保系统可启动
└── 运行时配置               # 动态配置，系统运行时生成
```

## 1.3 配置组件

| 组件 | 文件 | 用途 | 优先级 |
|------|------|------|--------|
| 环境变量 | `.env` | 敏感信息、部署特定配置 | 最高 |
| AI模型配置 | `config/ai_models.yaml` | AI模型参数和策略 | 高 |
| 交易配置 | `config/trading.yaml` | 交易参数和规则 | 高 |
| 安全配置 | `config/security.yaml` | 安全策略和规则 | 高 |
| 默认配置 | `config/default.yaml` | 基础默认值 | 中 |
| 代码配置 | `backend/app/core/settings.py` | 硬编码默认值 | 最低 |

---

# 2. 环境变量模板说明

## 2.1 环境变量文件结构

### .env.template 文件说明
```bash
# 这是环境变量模板文件，包含所有必需和可选的配置项
# 使用方法：
# 1. 复制 .env.template 为 .env
# 2. 填写实际配置值
# 3. 确保 .env 文件不被提交到版本控制
```

### 必需环境变量

| 变量名 | 说明 | 示例值 | 安全级别 |
|--------|------|--------|----------|
| `DB_PASSWORD` | 数据库密码 | `your_secure_password` | 高 |
| `QMT_USERNAME` | QMT用户名 | `your_qmt_username` | 高 |
| `QMT_PASSWORD` | QMT密码 | `your_qmt_password` | 高 |
| `QMT_ACCOUNT_ID` | QMT账户ID | `your_account_id` | 高 |
| `SECRET_KEY` | JWT密钥 | `32字符以上随机字符串` | 最高 |

### 可选环境变量

| 变量名 | 说明 | 默认值 | 用途 |
|--------|------|--------|------|
| `OPENAI_API_KEY` | OpenAI API密钥 | 无 | GPT-4模型访问 |
| `ANTHROPIC_API_KEY` | Anthropic API密钥 | 无 | Claude模型访问 |
| `GOOGLE_API_KEY` | Google API密钥 | 无 | Gemini模型访问 |
| `REDIS_PASSWORD` | Redis密码 | 无 | Redis认证 |
| `ENCRYPTION_KEY` | 数据加密密钥 | 无 | 敏感数据加密 |

## 2.2 环境变量配置步骤

### 步骤1：创建.env文件
```bash
# 使用配置管理工具创建
python scripts/config_manager.py create-env

# 或手动复制
cp .env.template .env
```

### 步骤2：填写配置值
```bash
# 编辑.env文件
nano .env

# 或使用其他编辑器
code .env
```

### 步骤3：验证配置
```bash
# 验证环境变量
python scripts/config_manager.py check-env

# 完整配置验证
python scripts/config_manager.py validate
```

## 2.3 环境变量安全要求

### 密码强度要求
- **最小长度**: 16字符
- **复杂性**: 包含大小写字母、数字、特殊字符
- **唯一性**: 每个环境使用不同密码
- **定期更换**: 建议每90天更换一次

### 密钥生成建议
```bash
# 生成强密码
openssl rand -base64 32

# 生成JWT密钥
python -c "import secrets; print(secrets.token_urlsafe(32))"

# 生成加密密钥
python -c "import os; print(os.urandom(32).hex())"
```

---

# 3. 配置文件说明

## 3.1 默认配置 (config/default.yaml)

包含系统的基础默认配置，环境变量会覆盖这些值。

### 主要配置项
```yaml
app:
  name: "AI Trading System"
  version: "1.0.0"

server:
  host: "0.0.0.0"
  port: 8000

database:
  pool_size: 20
  max_overflow: 30
```

## 3.2 AI模型配置 (config/ai_models.yaml)

### 模型配置结构
```yaml
# 各AI模型的详细配置
openai:
  model: "gpt-4"
  timeout: 60
  max_tokens: 4000

# AI交易员模型分配
trader_model_assignment:
  "AI-Trader-01": "gpt-4"
  "AI-Trader-02": "claude-3-sonnet"
```

### 配置说明
- **模型参数**: 控制AI模型的行为和性能
- **分配策略**: 决定哪个AI交易员使用哪个模型
- **性能优化**: 并发控制、缓存、负载均衡
- **成本控制**: Token使用监控、成本预警

## 3.3 交易配置 (config/trading.yaml)

### 全局交易配置
```yaml
global_trading:
  total_capital: 10000000.0  # 1000万元
  max_usable_capital_ratio: 0.8
  daily_loss_limit_ratio: 0.03
```

### AI交易员个性化配置
```yaml
ai_traders:
  capital_allocation:
    "AI-Trader-01": 150  # 150万元
    "AI-Trader-02": 120  # 120万元
  
  individual_params:
    "AI-Trader-01":
      profit_threshold: 0.08
      loss_threshold: -0.03
      risk_level: "moderate"
```

## 3.4 安全配置 (config/security.yaml)

### 认证和授权
```yaml
jwt:
  algorithm: "HS256"
  access_token_expire_minutes: 30

access_control:
  roles:
    admin:
      permissions:
        - "system.manage"
        - "trading.manage"
```

### 数据保护
```yaml
encryption:
  sensitive_data_encryption:
    enabled: true
    algorithm: "AES-256-GCM"
```

---

# 4. 密钥配置和安全要求

## 4.1 密钥类型和用途

### 系统密钥
| 密钥类型 | 环境变量 | 用途 | 轮换周期 |
|----------|----------|------|----------|
| JWT密钥 | `SECRET_KEY` | 用户认证令牌签名 | 30天 |
| 数据库密码 | `DB_PASSWORD` | 数据库连接认证 | 90天 |
| 加密密钥 | `ENCRYPTION_KEY` | 敏感数据加密 | 90天 |

### 外部服务密钥
| 服务 | 环境变量 | 用途 | 管理方式 |
|------|----------|------|----------|
| OpenAI | `OPENAI_API_KEY` | GPT-4 API访问 | 按需轮换 |
| Anthropic | `ANTHROPIC_API_KEY` | Claude API访问 | 按需轮换 |
| Google | `GOOGLE_API_KEY` | Gemini API访问 | 按需轮换 |
| QMT | `QMT_PASSWORD` | 交易接口认证 | 定期轮换 |

## 4.2 密钥安全要求

### 存储安全
- **环境变量**: 敏感密钥只通过环境变量传递
- **文件权限**: .env文件权限设置为600 (仅所有者可读写)
- **版本控制**: .env文件必须在.gitignore中排除
- **备份加密**: 密钥备份必须加密存储

### 传输安全
- **HTTPS**: 所有API通信使用HTTPS
- **TLS 1.3**: 使用最新的TLS协议
- **证书验证**: 严格验证SSL证书

### 访问控制
- **最小权限**: 只给必要的组件访问相应密钥
- **审计日志**: 记录所有密钥访问和使用
- **定期审查**: 定期审查密钥使用情况

## 4.3 密钥轮换流程

### 自动轮换
```yaml
# 在security.yaml中配置
key_management:
  rotation:
    enabled: true
    jwt_key_rotation_days: 30
    encryption_key_rotation_days: 90
```

### 手动轮换步骤
1. **生成新密钥**: 使用安全的随机数生成器
2. **更新配置**: 在.env文件中更新密钥
3. **重启服务**: 重启应用以加载新密钥
4. **验证功能**: 确保所有功能正常工作
5. **销毁旧密钥**: 安全删除旧密钥

---

# 5. 配置分层策略

## 5.1 配置优先级

配置加载按以下优先级顺序：

1. **环境变量** (最高优先级)
   - 运行时设置的环境变量
   - .env文件中的变量

2. **YAML配置文件** (高优先级)
   - config/ai_models.yaml
   - config/trading.yaml
   - config/security.yaml
   - config/default.yaml

3. **代码默认值** (最低优先级)
   - settings.py中的默认值

## 5.2 配置命名规范

### 环境变量命名
- **格式**: `{组件}_{配置项}`
- **大写**: 全部使用大写字母
- **分隔符**: 使用下划线分隔
- **示例**: `DB_PASSWORD`, `AI_MAX_CONCURRENT`

### YAML配置命名
- **格式**: 小写字母，下划线分隔
- **层级**: 使用嵌套结构组织
- **示例**: `ai_models.openai.timeout`

### 配置分组
```
数据库配置: DB_*
AI模型配置: AI_*, OPENAI_*, ANTHROPIC_*, GOOGLE_*
QMT配置: QMT_*
交易配置: TRADING_*
安全配置: SECRET_*, JWT_*, SECURITY_*
Redis配置: REDIS_*
日志配置: LOG_*
```

## 5.3 配置继承和覆盖

### 继承规则
- 子配置继承父配置的默认值
- 环境变量覆盖YAML配置
- 特定配置覆盖通用配置

### 覆盖示例
```yaml
# default.yaml
database:
  pool_size: 20
  timeout: 30

# 环境变量覆盖
DB_POOL_SIZE=50  # 覆盖pool_size
DB_TIMEOUT=60    # 覆盖timeout
```

---

# 6. 配置验证和测试

## 6.1 配置验证机制

### 启动时验证
系统启动时自动执行以下验证：
- 必需环境变量检查
- 配置格式验证
- 外部服务连接测试
- 权限和安全检查

### 验证工具使用
```bash
# 完整配置验证
python scripts/config_manager.py validate

# 检查环境变量
python scripts/config_manager.py check-env

# 测试外部连接
python scripts/config_manager.py test-connections
```

## 6.2 验证项目详情

### 环境变量验证
- **存在性检查**: 验证必需变量是否存在
- **格式验证**: 检查变量值格式是否正确
- **强度验证**: 验证密码和密钥强度

### 配置文件验证
- **语法检查**: YAML文件语法正确性
- **结构验证**: 配置结构完整性
- **值范围检查**: 数值在合理范围内

### 外部服务验证
- **数据库连接**: PostgreSQL连接和权限
- **Redis连接**: Redis服务可用性
- **AI模型API**: API密钥有效性（可选）

## 6.3 验证结果处理

### 验证状态
- **SUCCESS**: 所有验证通过
- **WARNING**: 有警告但不影响运行
- **ERROR**: 有错误，系统无法启动

### 错误处理
```python
# 验证失败时的处理
if validation_result['overall_status'] == 'failed':
    logger.error("Configuration validation failed")
    sys.exit(1)
```

---

# 7. 配置部署和更新流程

## 7.1 初始部署流程

### 步骤1：准备配置文件
```bash
# 1. 创建.env文件
python scripts/config_manager.py create-env

# 2. 编辑配置
nano .env

# 3. 验证配置
python scripts/config_manager.py validate
```

### 步骤2：部署验证
```bash
# 1. 测试数据库连接
python scripts/config_manager.py test-connections --service database

# 2. 测试Redis连接
python scripts/config_manager.py test-connections --service redis

# 3. 生成配置报告
python scripts/config_manager.py report --output deployment_report.md
```

### 步骤3：启动系统
```bash
# 1. 启动后端服务
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000

# 2. 验证系统状态
curl http://localhost:8000/health
```

## 7.2 配置更新流程

### 热更新（无需重启）
- 某些配置支持热更新
- 通过API接口动态更新
- 自动验证新配置

### 冷更新（需要重启）
1. **备份当前配置**
2. **更新配置文件**
3. **验证新配置**
4. **重启服务**
5. **验证系统功能**

### 回滚流程
```bash
# 1. 停止服务
docker-compose down

# 2. 恢复配置
cp .env.backup .env

# 3. 重启服务
docker-compose up -d

# 4. 验证功能
python scripts/config_manager.py validate
```

## 7.3 配置变更管理

### 变更记录
- 所有配置变更必须记录
- 包含变更原因、时间、操作人
- 保留变更前后的配置快照

### 审批流程
- 重要配置变更需要审批
- 安全相关配置需要安全团队审批
- 生产环境变更需要运维团队确认

### 测试要求
- 配置变更前必须在测试环境验证
- 关键配置变更需要回归测试
- 性能相关配置需要性能测试

---

# 8. 故障排除和恢复

## 8.1 常见配置问题

### 环境变量问题
| 问题 | 症状 | 解决方案 |
|------|------|----------|
| 缺失必需变量 | 启动失败 | 检查.env文件，补充缺失变量 |
| 密钥格式错误 | 认证失败 | 重新生成正确格式的密钥 |
| 权限不足 | 无法读取配置 | 检查文件权限，设置正确权限 |

### 数据库连接问题
| 问题 | 症状 | 解决方案 |
|------|------|----------|
| 连接超时 | 数据库操作失败 | 检查网络连接和防火墙设置 |
| 认证失败 | 登录被拒绝 | 验证用户名密码，检查权限 |
| 连接池耗尽 | 性能下降 | 调整连接池配置参数 |

### AI模型API问题
| 问题 | 症状 | 解决方案 |
|------|------|----------|
| API密钥无效 | 401错误 | 检查密钥有效性，重新获取 |
| 配额超限 | 429错误 | 检查使用量，升级配额 |
| 网络超时 | 请求超时 | 调整超时设置，检查网络 |

## 8.2 诊断工具

### 配置诊断
```bash
# 完整诊断
python scripts/config_manager.py validate --verbose

# 生成诊断报告
python scripts/config_manager.py report --output diagnosis.md

# 检查特定服务
python scripts/config_manager.py test-connections --service database
```

### 日志分析
```bash
# 查看配置相关日志
grep "config" logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log | grep -i config

# 实时监控日志
tail -f logs/app.log | grep -i config
```

## 8.3 恢复流程

### 配置恢复
1. **识别问题**: 通过日志和诊断工具确定问题
2. **备份当前状态**: 保存当前配置以便分析
3. **恢复配置**: 从备份恢复正确配置
4. **验证修复**: 确认问题已解决
5. **监控系统**: 持续监控确保稳定

### 紧急恢复
```bash
# 1. 快速恢复到最后已知良好配置
cp config_backup/.env .env
cp -r config_backup/config/* config/

# 2. 重启服务
docker-compose restart

# 3. 验证恢复
python scripts/config_manager.py validate
```

### 数据恢复
- 配置错误导致的数据问题
- 从数据库备份恢复
- 验证数据完整性

## 8.4 预防措施

### 配置备份
- 定期自动备份配置文件
- 保留多个版本的配置备份
- 测试备份恢复流程

### 监控告警
- 配置文件变更监控
- 配置验证失败告警
- 外部服务连接异常告警

### 文档维护
- 保持配置文档更新
- 记录配置变更历史
- 维护故障排除知识库

---

## 总结

本指南提供了AI交易系统配置管理的完整说明，涵盖了从环境变量设置到故障恢复的各个方面。正确的配置管理是系统稳定运行的基础，请严格按照本指南执行配置管理操作。

### 关键要点
1. **安全第一**: 敏感信息必须通过环境变量管理
2. **分层管理**: 采用分层配置策略，确保灵活性
3. **验证机制**: 完整的配置验证确保系统可靠性
4. **变更管理**: 规范的变更流程确保配置安全
5. **故障恢复**: 完善的备份和恢复机制确保业务连续性

### 最佳实践
- 定期更换密钥和密码
- 保持配置文档更新
- 定期验证配置有效性
- 建立完善的监控告警
- 定期演练故障恢复流程
