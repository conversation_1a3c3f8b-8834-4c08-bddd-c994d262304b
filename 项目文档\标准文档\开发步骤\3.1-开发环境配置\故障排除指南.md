# 故障排除指南

## 文档信息
- **创建日期**: 2025年7月13日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: 开发环境配置常见问题解决方案
- **技术基础**: Vue.js 3.5.x + FastAPI 0.116.1 + Python 3.13.2 + PostgreSQL 17.5

## 版本更新记录
- v1.0: 初始版本，完成常见问题和解决方案

## 目录
1. [Python环境问题](#1-python环境问题)
2. [Node.js环境问题](#2-nodejs环境问题)
3. [Docker环境问题](#3-docker环境问题)
4. [VS Code配置问题](#4-vs-code配置问题)
5. [网络和权限问题](#5-网络和权限问题)

---

## 1. Python环境问题

### 1.1 Conda环境问题

**问题**: conda命令未找到
```
'conda' is not recognized as an internal or external command
```

**解决方案**:
```bash
# 1. 检查Anaconda/Miniconda是否已安装
where conda

# 2. 如果未安装，下载并安装Miniconda
# 下载地址: https://docs.conda.io/en/latest/miniconda.html

# 3. 添加conda到PATH环境变量
# Windows: 添加以下路径到PATH
# C:\Users\<USER>\miniconda3\Scripts
# C:\Users\<USER>\miniconda3

# 4. 重启命令行工具
```

**问题**: Python 3.13.2版本不可用
```
PackagesNotFoundError: The following packages are not available from current channels: python=3.13.2
```

**解决方案**:
```bash
# 1. 更新conda到最新版本
conda update conda

# 2. 添加conda-forge频道
conda config --add channels conda-forge

# 3. 使用可用的最新版本
conda search python=3.13*
conda create -n AI_Nasdaq_trading python=3.13.1 -y

# 4. 如果仍不可用，使用Python 3.12
conda create -n AI_Nasdaq_trading python=3.12 -y
```

### 1.2 包安装问题

**问题**: pip安装包失败
```
ERROR: Could not install packages due to an EnvironmentError
```

**解决方案**:
```bash
# 1. 确保环境已激活
conda activate AI_Nasdaq_trading

# 2. 升级pip
python -m pip install --upgrade pip

# 3. 使用国内镜像源
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ package_name

# 4. 如果权限问题，使用用户安装
pip install --user package_name

# 5. 清理pip缓存
pip cache purge
```

**问题**: 包版本冲突
```
ERROR: pip's dependency resolver does not currently take into account all the packages that are installed
```

**解决方案**:
```bash
# 1. 创建新的干净环境
conda create -n AI_Nasdaq_trading_new python=3.13.2 -y
conda activate AI_Nasdaq_trading_new

# 2. 使用requirements.txt逐步安装
pip install -r requirements.txt

# 3. 或者使用conda安装主要包
conda install fastapi uvicorn sqlalchemy -c conda-forge
```

### 1.3 VS Code Python解释器问题

**问题**: VS Code找不到Python解释器

**解决方案**:
```bash
# 1. 查找conda环境路径
conda info --envs

# 2. 在VS Code中手动设置解释器路径
# Ctrl+Shift+P -> Python: Select Interpreter
# 选择: D:\GHJ\AI\conda_envs_pkgs\envs\AI_Nasdaq_trading\python.exe

# 3. 验证设置
# 在VS Code终端中运行
python --version
```

## 2. Node.js环境问题

### 2.1 Node.js安装问题

**问题**: Node.js版本过旧
```
Node.js version 16.x.x is not supported
```

**解决方案**:
```bash
# 1. 卸载旧版本Node.js
# Windows: 控制面板 -> 程序和功能 -> 卸载Node.js

# 2. 下载最新LTS版本
# 访问: https://nodejs.org/

# 3. 安装Node.js 24.1.0+

# 4. 验证安装
node --version
npm --version
```

### 2.2 npm包安装问题

**问题**: npm安装全局包失败
```
EACCES: permission denied
```

**解决方案**:
```bash
# Windows解决方案:
# 1. 以管理员身份运行命令行

# 2. 或者配置npm全局目录
npm config set prefix "C:\Users\<USER>\AppData\Roaming\npm"

# 3. 添加到PATH环境变量
# C:\Users\<USER>\AppData\Roaming\npm

# 4. 重新安装全局包
npm install -g @vue/cli vite eslint prettier
```

**问题**: npm网络连接问题
```
npm ERR! network timeout
```

**解决方案**:
```bash
# 1. 使用国内镜像源
npm config set registry https://registry.npmmirror.com/

# 2. 增加超时时间
npm config set timeout 60000

# 3. 使用yarn替代
npm install -g yarn
yarn global add @vue/cli vite eslint prettier

# 4. 恢复默认源
npm config set registry https://registry.npmjs.org/
```

### 2.3 前端工具问题

**问题**: Vue CLI创建项目失败

**解决方案**:
```bash
# 1. 检查Vue CLI版本
vue --version

# 2. 更新Vue CLI
npm uninstall -g @vue/cli
npm install -g @vue/cli@latest

# 3. 清理npm缓存
npm cache clean --force

# 4. 使用Vite替代
npm create vue@latest my-project
```

## 3. Docker环境问题

### 3.1 Docker安装问题

**问题**: Docker Desktop启动失败
```
Docker Desktop failed to start
```

**解决方案**:
```bash
# 1. 检查系统要求
# - Windows 10/11 Pro, Enterprise, Education
# - 启用Hyper-V和容器功能

# 2. 启用Windows功能
# 控制面板 -> 程序和功能 -> 启用或关闭Windows功能
# 勾选: Hyper-V, 容器, 适用于Linux的Windows子系统

# 3. 重启计算机

# 4. 重新安装Docker Desktop
# 下载地址: https://www.docker.com/products/docker-desktop
```

### 3.2 Docker权限问题

**问题**: Docker命令权限不足
```
permission denied while trying to connect to the Docker daemon socket
```

**解决方案**:
```bash
# Windows解决方案:
# 1. 确保Docker Desktop正在运行

# 2. 将用户添加到docker-users组
# 计算机管理 -> 本地用户和组 -> 组 -> docker-users
# 添加当前用户

# 3. 重新登录Windows

# 4. 验证权限
docker run --rm hello-world
```

### 3.3 容器网络问题

**问题**: 容器无法访问网络
```
Could not resolve host
```

**解决方案**:
```bash
# 1. 检查Docker网络
docker network ls

# 2. 重启Docker Desktop

# 3. 重置Docker网络
docker system prune -a
docker network prune

# 4. 检查防火墙设置
# Windows防火墙 -> 允许应用通过防火墙
# 确保Docker Desktop已允许
```

### 3.4 端口占用问题

**问题**: 端口已被占用
```
Bind for 0.0.0.0:5432 failed: port is already allocated
```

**解决方案**:
```bash
# 1. 查找占用端口的进程
netstat -ano | findstr :5432

# 2. 终止占用进程
taskkill /PID [进程ID] /F

# 3. 或者修改docker-compose.yml中的端口映射
ports:
  - "5433:5432"  # 使用不同的主机端口

# 4. 重新启动容器
docker-compose up -d
```

## 4. VS Code配置问题

### 4.1 插件安装问题

**问题**: VS Code插件安装失败
```
Extension installation failed
```

**解决方案**:
```bash
# 1. 检查网络连接

# 2. 清理VS Code缓存
# 关闭VS Code
# 删除: %APPDATA%\Code\User\workspaceStorage
# 删除: %APPDATA%\Code\logs

# 3. 手动安装插件
# 下载.vsix文件
# VS Code -> Extensions -> ... -> Install from VSIX

# 4. 使用命令行安装
code --install-extension ms-python.python
```

### 4.2 工作区配置问题

**问题**: VS Code设置不生效

**解决方案**:
```json
// 1. 检查settings.json语法
// 使用JSON验证器检查语法错误

// 2. 重置工作区设置
// 删除 .vscode/settings.json
// 重新创建配置

// 3. 检查设置优先级
// 用户设置 < 工作区设置 < 文件夹设置

// 4. 重启VS Code
```

### 4.3 调试配置问题

**问题**: Python调试无法启动

**解决方案**:
```json
// 1. 检查launch.json配置
{
  "name": "Python: FastAPI Backend",
  "type": "python",
  "request": "launch",
  "program": "${workspaceFolder}/backend/app/main.py",
  "console": "integratedTerminal",
  "cwd": "${workspaceFolder}/backend",
  "env": {
    "PYTHONPATH": "${workspaceFolder}/backend"
  }
}

// 2. 确保Python解释器正确
// 3. 检查工作目录路径
// 4. 验证环境变量
```

## 5. 网络和权限问题

### 5.1 网络连接问题

**问题**: 无法下载包或镜像

**解决方案**:
```bash
# 1. 检查网络连接
ping google.com

# 2. 配置代理（如果需要）
# npm代理
npm config set proxy http://proxy.company.com:8080
npm config set https-proxy http://proxy.company.com:8080

# pip代理
pip install --proxy http://proxy.company.com:8080 package_name

# Docker代理
# Docker Desktop -> Settings -> Resources -> Proxies

# 3. 使用国内镜像源
# npm: https://registry.npmmirror.com/
# pip: https://pypi.tuna.tsinghua.edu.cn/simple/
# Docker: https://docker.mirrors.ustc.edu.cn/
```

### 5.2 防火墙问题

**问题**: 防火墙阻止连接

**解决方案**:
```bash
# 1. 检查Windows防火墙设置
# 控制面板 -> 系统和安全 -> Windows Defender防火墙

# 2. 添加应用例外
# 允许应用通过防火墙 -> 更改设置
# 添加: Docker Desktop, Node.js, Python

# 3. 添加端口例外
# 高级设置 -> 入站规则 -> 新建规则
# 端口: 3000, 8000, 5432

# 4. 临时禁用防火墙测试
# 注意：仅用于测试，测试后重新启用
```

### 5.3 权限问题

**问题**: 文件或目录权限不足

**解决方案**:
```bash
# Windows解决方案:
# 1. 以管理员身份运行命令行

# 2. 修改文件夹权限
# 右键文件夹 -> 属性 -> 安全 -> 编辑
# 添加完全控制权限

# 3. 检查用户组
# 确保用户在Administrators或docker-users组中

# 4. 使用用户目录
# 避免在系统目录（如C:\Program Files）中操作
```

---

## 快速诊断清单

### 环境检查清单

- [ ] Python 3.13.2 已安装并可访问
- [ ] conda环境 AI_Nasdaq_trading 已创建并激活
- [ ] Node.js 24.1.0+ 已安装
- [ ] npm全局工具已安装（vue, vite, eslint, prettier）
- [ ] Docker Desktop 正在运行
- [ ] VS Code 已安装并配置
- [ ] 网络连接正常
- [ ] 防火墙允许相关应用
- [ ] 用户权限充足

### 常用诊断命令

```bash
# 系统信息
systeminfo
echo %PATH%

# Python环境
conda info --envs
conda activate AI_Nasdaq_trading
python --version
pip --version

# Node.js环境
node --version
npm --version
npm list -g --depth=0

# Docker环境
docker --version
docker info
docker ps

# 网络测试
ping google.com
nslookup registry.npmjs.org
```

### 紧急联系方式

- **技术负责人**: [联系方式]
- **系统管理员**: [联系方式]
- **紧急支持**: [联系方式]

---

## 附录

### A. 环境重置脚本

```bash
#!/bin/bash
# 环境重置脚本 - 谨慎使用

echo "警告：此脚本将重置开发环境"
read -p "确认继续？(y/N): " confirm

if [[ $confirm == [yY] ]]; then
    # 删除conda环境
    conda env remove -n AI_Nasdaq_trading
    
    # 清理npm缓存
    npm cache clean --force
    
    # 清理Docker
    docker system prune -a
    
    echo "环境重置完成，请重新配置"
fi
```

### B. 日志收集脚本

```bash
#!/bin/bash
# 收集诊断日志

mkdir -p logs
echo "收集系统诊断信息..."

# 系统信息
systeminfo > logs/system_info.txt
echo %PATH% > logs/path_info.txt

# Python信息
conda info --envs > logs/conda_envs.txt
conda list > logs/conda_packages.txt

# Node.js信息
npm list -g --depth=0 > logs/npm_global.txt

# Docker信息
docker info > logs/docker_info.txt
docker ps -a > logs/docker_containers.txt

echo "诊断信息已保存到 logs/ 目录"
```
