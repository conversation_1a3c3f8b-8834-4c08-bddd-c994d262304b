# 海天AI纳指科技ETF(159509)专业交易系统数据库设计文档

## 文档信息
- **创建日期**: 2025年7月16日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: 海天AI纳指科技ETF(159509)专业交易系统数据库设计
- **交易标的**: 纳指科技ETF (代码: 159509) - 唯一交易标的
- **技术基础**: PostgreSQL 17.5 + SQLAlchemy 2.0 + Alembic
- **设计原则**: 领域驱动设计（DDD）+ 高并发优化 + 单一标的专业化

---

## 1. 数据库架构设计说明

### 1.1 整体架构设计

海天AI纳指科技ETF(159509)专业交易系统采用PostgreSQL 17.5作为主数据库，专门支持159509的高并发择时交易和AI决策的数据存储需求。

**架构特点**：
- **企业级可靠性**：ACID事务保证、WAL日志、PITR恢复
- **高性能**：支持大数据量和复杂查询优化
- **JSON支持**：原生JSONB类型，完美适配AI档案存储
- **扩展性强**：丰富的插件生态，支持时序数据、全文搜索
- **标准兼容**：完整的SQL标准支持

### 1.2 数据库分层设计

```mermaid
graph TB
    subgraph "应用层"
        A[FastAPI 应用服务]
        A1[REST API 接口]
        A2[WebSocket 实时通信]
        A3[认证授权中间件]
    end

    subgraph "ORM层"
        B[SQLAlchemy 2.0]
        B1[模型定义]
        B2[会话管理]
        B3[查询构建器]
    end

    subgraph "数据访问层"
        C[CRUD Operations]
        C1[Repository 模式]
        C2[事务管理]
        C3[缓存策略]
    end

    subgraph "数据库层 (PostgreSQL)"
        D1[基础用户模块]
        D2[AI交易员模块]
        D3[交易数据模块]
        D4[市场数据模块]
    end

    %% 连接关系
    A --> B
    B --> C
    C --> D1
    C --> D2
    C --> D3
    C --> D4

    %% 样式定义
    classDef appLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px,color:#000
    classDef ormLayer fill:#f3e5f5,stroke:#4a148c,stroke-width:2px,color:#000
    classDef dataLayer fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px,color:#000
    classDef dbLayer fill:#fff3e0,stroke:#e65100,stroke-width:2px,color:#000
    classDef moduleBox fill:#f5f5f5,stroke:#757575,stroke-width:1px,color:#000

    class A,A1,A2,A3 appLayer
    class B,B1,B2,B3 ormLayer
    class C,C1,C2,C3 dataLayer
    class D1,D2,D3,D4 dbLayer
    class D1A,D1B,D1C,D1D,D2A,D2B,D2C,D2D,D3A,D3B,D3C,D3D,D4A,D4B,D4C,D4D moduleBox
```

### 1.3 核心数据表设计

#### 1.3.1 基础用户表

**users (用户表)**
- 存储系统用户基本信息
- 简单的用户认证
- 基本的用户状态管理

#### 1.3.2 AI交易员配置和状态表

**ai_traders (AI交易员信息和配置表)**
- AI交易员的核心信息和配置参数
- 模型类型和版本管理
- 交易配置参数（止盈止损、交易限制等）
- 创建时间和状态跟踪

**ai_trader_states (AI交易员状态表)**
- 实时状态信息
- 工作状态、健康状态
- 性能指标和监控数据

**ai_trader_profiles (AI交易员档案表)**
- 使用JSONB存储个人档案
- 支持复杂的档案数据结构
- 高效的JSON查询和索引

#### 1.3.3 交易记录和订单表

**trading_orders (交易订单表)**
- 完整的订单生命周期记录
- 订单状态跟踪
- 风险控制信息

**trading_executions (交易执行表)**
- 订单执行详细记录
- 执行价格和数量
- 滑点和费用信息

**positions (持仓表)**
- 当前持仓信息
- 实时盈亏计算
- 风险敞口管理

**position_history (持仓历史表)**
- 持仓变化历史
- 支持历史查询和分析
- 绩效计算基础数据

#### 1.3.4 市场数据和技术指标表

**market_data (市场数据表)**
- 实时行情数据存储
- 支持多种数据频率
- 高效的时序数据管理

**technical_indicators (技术指标表)**
- 计算后的技术指标
- 支持自定义指标
- 实时更新机制


#### 1.3.5 绩效指标表

**performance_metrics (绩效指标表)**
- 详细的绩效数据
- 多维度指标计算
- 历史趋势分析

#### 1.3.6 系统日志和审计表

**system_logs (系统日志表)**
- 系统运行日志
- 错误和异常记录
- 性能监控数据

**audit_logs (审计日志表)**
- 关键操作审计
- 数据变更跟踪
- 合规性记录

**risk_events (风险事件表)**
- 风险事件记录
- 风险级别和处理状态
- 风险分析和报告

## 2. 实体关系图（ERD）和数据字典

### 2.1 核心实体关系图

```mermaid
erDiagram
    %% 核心用户和AI交易员模块
    users用户表 ||--o{ ai_traders交易员表 : "创建 creates"
    ai_traders交易员表 ||--|| ai_trader_states状态表 : "状态 has_state"
    ai_traders交易员表 ||--o{ ai_trader_profiles档案表 : "档案 has_profile"

    %% 交易执行模块
    ai_traders交易员表 ||--o{ trading_orders订单表 : "下单 places"
    trading_orders订单表 ||--o{ trading_executions执行表 : "执行 executed_as"

    %% 持仓管理
    ai_traders交易员表 ||--o{ positions持仓表 : "持有 holds"
    ai_traders交易员表 ||--o{ position_history持仓历史表 : "历史 tracked_in"

    %% 市场数据和技术指标
    market_data市场数据表 ||--o{ technical_indicators技术指标表 : "生成 generates"

    %% 绩效和风险管理
    ai_traders交易员表 ||--o{ performance_metrics绩效指标表 : "绩效 measured_by"
    ai_traders交易员表 ||--o{ risk_events风险事件表 : "触发 triggers"

    %% 审计和日志
    users用户表 ||--o{ system_logs系统日志表 : "生成 generates"
    users用户表 ||--o{ audit_logs审计日志表 : "审计 audited_in"
```

### 2.2 数据字典

#### 2.2.1 基础用户模块

**users 表**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 用户唯一标识 |
| username | VARCHAR(50) | UNIQUE, NOT NULL | 用户名 |
| email | VARCHAR(100) | UNIQUE, NOT NULL | 邮箱地址 |
| password_hash | VARCHAR(255) | NOT NULL | 密码哈希 |
| full_name | VARCHAR(100) | NOT NULL | 用户全名 |
| role | VARCHAR(20) | NOT NULL, DEFAULT 'USER' | 用户角色(ADMIN/USER/VIEWER) |
| is_active | BOOLEAN | DEFAULT TRUE | 是否激活 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT NOW() | 更新时间 |

#### 2.2.2 AI交易员模块

**ai_traders 表**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 交易员唯一标识 |
| name | VARCHAR(100) | UNIQUE, NOT NULL | 交易员名称 |
| display_name | VARCHAR(100) | NOT NULL | 显示名称 |
| model_type | VARCHAR(50) | NOT NULL | AI模型类型(GPT-4/Claude-3/Gemini等) |
| model_version | VARCHAR(20) | NOT NULL | 模型版本 |
| status | VARCHAR(20) | NOT NULL | 当前状态(CREATED/ACTIVE/SUSPENDED/RETIRED) |
| max_position_quantity | INTEGER | NOT NULL | 最大持仓数量配额 |
| max_single_trade_quantity | INTEGER | NOT NULL | 单笔最大交易数量 |
| daily_trade_limit | INTEGER | NOT NULL | 日交易次数限制 |
| profit_threshold_pct | DECIMAL(5,2) | NOT NULL | 止盈阈值百分比 |
| loss_threshold_pct | DECIMAL(5,2) | NOT NULL | 止损阈值百分比 |
| model_temperature | DECIMAL(3,2) | DEFAULT 0.7 | AI模型温度参数 |
| risk_preference | DECIMAL(3,2) | NOT NULL | 风险偏好系数(0-1) |
| trading_style | VARCHAR(50) | NULL | 交易风格描述 |
| is_config_active | BOOLEAN | DEFAULT TRUE | 配置是否激活 |
| created_by | UUID | FK(users.id) | 创建者 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT NOW() | 更新时间 |

**ai_trader_states 表**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 状态唯一标识 |
| trader_id | UUID | FK(ai_traders.id), UNIQUE | 交易员ID |
| work_status | VARCHAR(20) | NOT NULL | 工作状态(IDLE/ANALYZING/TRADING/LEARNING/ERROR) |
| health_status | VARCHAR(20) | NOT NULL | 健康状态(HEALTHY/WARNING/ERROR/OFFLINE) |
| current_positions | INTEGER | DEFAULT 0 | 当前持仓数量 |
| daily_trades_count | INTEGER | DEFAULT 0 | 当日交易次数 |
| daily_pnl | DECIMAL(12,4) | DEFAULT 0 | 当日盈亏 |
| performance_level | VARCHAR(10) | DEFAULT 'C' | 绩效等级(S/A/B/C/D) |
| total_trades | INTEGER | DEFAULT 0 | 累计交易次数 |
| win_rate | DECIMAL(5,2) | DEFAULT 0 | 胜率百分比 |
| total_return | DECIMAL(10,4) | DEFAULT 0 | 累计收益率 |
| last_decision_at | TIMESTAMP | NULL | 最后决策时间 |
| error_count | INTEGER | DEFAULT 0 | 错误计数 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT NOW() | 更新时间 |

**ai_trader_profiles 表**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 档案唯一标识 |
| trader_id | UUID | FK(ai_traders.id) | 交易员ID |
| profile_type | VARCHAR(50) | NOT NULL | 档案类型(TRADING_RULES/LEARNING_NOTES) |
| profile_data | JSONB | NOT NULL | 档案数据 |
| version | INTEGER | NOT NULL | 档案版本 |
| is_current | BOOLEAN | DEFAULT TRUE | 是否为当前版本 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT NOW() | 更新时间 |

#### 2.2.3 交易数据模块

**trading_orders 表**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 订单唯一标识 |
| trader_id | UUID | FK(ai_traders.id) | 交易员ID |
| symbol | VARCHAR(20) | NOT NULL, DEFAULT '159509', CHECK (symbol = '159509') | 159509代码（系统强制限制） |
| order_type | VARCHAR(20) | NOT NULL | 订单类型(MARKET/LIMIT) |
| side | VARCHAR(10) | NOT NULL | 买卖方向(BUY/SELL) |
| quantity | INTEGER | NOT NULL | 159509委托数量 |
| price | DECIMAL(10,4) | NULL | 159509委托价格 |
| filled_quantity | INTEGER | DEFAULT 0 | 159509成交数量 |
| avg_fill_price | DECIMAL(10,4) | NULL | 平均成交价 |
| status | VARCHAR(20) | NOT NULL | 订单状态(PENDING/FILLED/CANCELLED/REJECTED) |
| decision_confidence | DECIMAL(3,2) | NULL | 决策置信度 |
| decision_reason | TEXT | NULL | 决策依据 |
| qmt_order_id | VARCHAR(50) | NULL | QMT订单ID |
| submit_time | TIMESTAMP | NULL | 提交时间 |
| fill_time | TIMESTAMP | NULL | 成交时间 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT NOW() | 更新时间 |

**trading_executions 表**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 执行记录唯一标识 |
| order_id | UUID | FK(trading_orders.id) | 订单ID |
| trader_id | UUID | FK(ai_traders.id) | 交易员ID |
| symbol | VARCHAR(20) | NOT NULL, DEFAULT '159509', CHECK (symbol = '159509') | 159509代码（系统强制限制） |
| side | VARCHAR(10) | NOT NULL | 买卖方向 |
| quantity | INTEGER | NOT NULL | 159509成交数量 |
| price | DECIMAL(10,4) | NOT NULL | 159509成交价格 |
| amount | DECIMAL(15,2) | NOT NULL | 159509成交金额 |
| commission | DECIMAL(8,4) | DEFAULT 0 | 佣金费用 |
| net_amount | DECIMAL(15,2) | NOT NULL | 净成交金额 |
| execution_time | TIMESTAMP | NOT NULL | 执行时间 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |

**positions 表**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 持仓唯一标识 |
| trader_id | UUID | FK(ai_traders.id) | 交易员ID |
| symbol | VARCHAR(20) | NOT NULL, DEFAULT '159509', CHECK (symbol = '159509') | 159509代码（系统强制限制） |
| quantity | INTEGER | NOT NULL | 159509持仓数量 |
| available_quantity | INTEGER | NOT NULL | 159509可卖数量 |
| avg_cost | DECIMAL(10,4) | NOT NULL | 159509平均成本价 |
| market_value | DECIMAL(15,2) | NOT NULL | 159509市值 |
| unrealized_pnl | DECIMAL(12,4) | NOT NULL | 159509浮动盈亏 |
| first_buy_at | TIMESTAMP | NOT NULL | 首次买入时间 |
| last_trade_at | TIMESTAMP | NOT NULL | 最后交易时间 |
| is_active | BOOLEAN | DEFAULT TRUE | 是否活跃持仓 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT NOW() | 更新时间 |

**position_history 表**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 历史记录唯一标识 |
| trader_id | UUID | FK(ai_traders.id) | 交易员ID |
| symbol | VARCHAR(20) | NOT NULL, DEFAULT '159509', CHECK (symbol = '159509') | 159509代码（系统强制限制） |
| action_type | VARCHAR(20) | NOT NULL | 操作类型(BUY/SELL) |
| quantity | INTEGER | NOT NULL | 159509数量变化 |
| price | DECIMAL(10,4) | NOT NULL | 159509成交价格 |
| amount | DECIMAL(15,2) | NOT NULL | 159509成交金额 |
| position_before | INTEGER | NOT NULL | 159509操作前持仓 |
| position_after | INTEGER | NOT NULL | 操作后持仓 |
| realized_pnl | DECIMAL(12,4) | DEFAULT 0 | 本次实现盈亏 |
| trade_date | DATE | NOT NULL | 交易日期 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |

#### 2.2.4 交易数据模块

**trading_orders 表**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 订单唯一标识 |
| trader_id | UUID | FK(ai_traders.id) | 交易员ID |
| symbol | VARCHAR(20) | NOT NULL, DEFAULT '159509', CHECK (symbol = '159509') | 159509代码（系统强制限制） |
| order_type | VARCHAR(20) | NOT NULL | 订单类型(MARKET/LIMIT/STOP) |
| side | VARCHAR(10) | NOT NULL | 买卖方向(BUY/SELL) |
| quantity | INTEGER | NOT NULL | 159509委托数量 |
| price | DECIMAL(10,4) | NULL | 159509委托价格 |
| filled_quantity | INTEGER | DEFAULT 0 | 159509成交数量 |
| avg_fill_price | DECIMAL(10,4) | NULL | 平均成交价 |
| status | VARCHAR(20) | NOT NULL | 订单状态(PENDING/FILLED/CANCELLED/REJECTED) |
| decision_confidence | DECIMAL(3,2) | NULL | 决策置信度 |
| decision_reason | TEXT | NULL | 决策依据 |
| ai_analysis_data | JSONB | NULL | AI分析数据 |
| position_check_passed | BOOLEAN | DEFAULT FALSE | 仓位验证是否通过 |
| risk_check_passed | BOOLEAN | DEFAULT FALSE | 风险检查是否通过 |
| qmt_order_id | VARCHAR(50) | NULL | QMT订单ID |
| submit_time | TIMESTAMP | NULL | 提交时间 |
| fill_time | TIMESTAMP | NULL | 成交时间 |
| cancel_time | TIMESTAMP | NULL | 撤销时间 |
| error_message | TEXT | NULL | 错误信息 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT NOW() | 更新时间 |

**trading_executions 表**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 执行记录唯一标识 |
| order_id | UUID | FK(trading_orders.id) | 订单ID |
| trader_id | UUID | FK(ai_traders.id) | 交易员ID |
| symbol | VARCHAR(20) | NOT NULL, DEFAULT '159509', CHECK (symbol = '159509') | 159509代码（系统强制限制） |
| side | VARCHAR(10) | NOT NULL | 买卖方向 |
| quantity | INTEGER | NOT NULL | 159509成交数量 |
| price | DECIMAL(10,4) | NOT NULL | 159509成交价格 |
| amount | DECIMAL(15,2) | NOT NULL | 159509成交金额 |
| commission | DECIMAL(8,4) | DEFAULT 0 | 佣金费用 |
| net_amount | DECIMAL(15,2) | NOT NULL | 净成交金额 |
| execution_time | TIMESTAMP | NOT NULL | 执行时间 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |

#### 2.2.4 市场数据和技术指标模块

**market_data 表（159509专用）**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 数据唯一标识 |
| symbol | VARCHAR(20) | NOT NULL, DEFAULT '159509', CHECK (symbol = '159509') | 159509代码（系统强制限制） |
| data_type | VARCHAR(20) | NOT NULL | 数据类型(TICK/1MIN/5MIN/DAILY) |
| timestamp | TIMESTAMP | NOT NULL | 时间戳 |
| open_price | DECIMAL(10,4) | NULL | 159509开盘价 |
| high_price | DECIMAL(10,4) | NULL | 159509最高价 |
| low_price | DECIMAL(10,4) | NULL | 159509最低价 |
| close_price | DECIMAL(10,4) | NOT NULL | 159509收盘价 |
| volume | BIGINT | NOT NULL | 成交量 |
| amount | DECIMAL(18,2) | NULL | 成交额 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |

**technical_indicators 表（159509专用ETF指标）**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 指标唯一标识 |
| symbol | VARCHAR(20) | NOT NULL, DEFAULT '159509', CHECK (symbol = '159509') | 159509代码（系统强制限制） |
| indicator_type | VARCHAR(50) | NOT NULL | ETF适用指标类型(RSI/MACD/MA/BOLL等) |
| timeframe | VARCHAR(20) | NOT NULL | 时间框架(1MIN/5MIN/15MIN/DAILY) |
| timestamp | TIMESTAMP | NOT NULL | 时间戳 |
| indicator_values | JSONB | NOT NULL | 159509指标值 |
| signal_strength | DECIMAL(3,2) | NULL | 159509择时信号强度 |
| signal_direction | VARCHAR(10) | NULL | 信号方向(BUY/SELL/HOLD) |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |

#### 2.2.5 绩效指标模块

**performance_metrics 表**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 绩效指标唯一标识 |
| trader_id | UUID | FK(ai_traders.id) | 交易员ID |
| metric_date | DATE | NOT NULL | 指标日期 |
| total_return | DECIMAL(10,4) | DEFAULT 0 | 总收益率 |
| win_rate | DECIMAL(5,2) | DEFAULT 0 | 胜率 |
| max_drawdown | DECIMAL(10,4) | DEFAULT 0 | 最大回撤 |
| sharpe_ratio | DECIMAL(6,3) | DEFAULT 0 | 夏普比率 |
| total_trades | INTEGER | DEFAULT 0 | 总交易次数 |
| profit_trades | INTEGER | DEFAULT 0 | 盈利交易次数 |
| loss_trades | INTEGER | DEFAULT 0 | 亏损交易次数 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |

#### 2.2.6 系统日志和审计模块

**system_logs 表**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 日志唯一标识 |
| log_level | VARCHAR(20) | NOT NULL | 日志级别(DEBUG/INFO/WARNING/ERROR/CRITICAL) |
| module | VARCHAR(50) | NOT NULL | 模块名称 |
| message | TEXT | NOT NULL | 日志消息 |
| details | JSONB | NULL | 详细信息 |
| user_id | UUID | FK(users.id) | 相关用户ID |
| trader_id | UUID | FK(ai_traders.id) | 相关交易员ID |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |

**audit_logs 表**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 审计日志唯一标识 |
| action_type | VARCHAR(50) | NOT NULL | 操作类型 |
| table_name | VARCHAR(50) | NOT NULL | 表名 |
| record_id | UUID | NOT NULL | 记录ID |
| old_values | JSONB | NULL | 旧值 |
| new_values | JSONB | NULL | 新值 |
| user_id | UUID | FK(users.id) | 操作用户ID |
| ip_address | VARCHAR(45) | NULL | IP地址 |
| user_agent | TEXT | NULL | 用户代理 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |

**risk_events 表（159509专用风险控制）**
| 字段名 | 数据类型 | 约束 | 说明 |
|--------|----------|------|------|
| id | UUID | PK | 风险事件唯一标识 |
| event_type | VARCHAR(50) | NOT NULL | 事件类型(POSITION_LIMIT/LOSS_LIMIT/FREQUENCY_LIMIT) |
| risk_level | VARCHAR(20) | NOT NULL | 风险级别(LOW/MEDIUM/HIGH/CRITICAL) |
| trader_id | UUID | FK(ai_traders.id) | 相关交易员ID |
| symbol | VARCHAR(20) | DEFAULT '159509', CHECK (symbol = '159509' OR symbol IS NULL) | 159509代码（系统强制限制） |
| event_description | TEXT | NOT NULL | 159509风险事件描述 |
| trigger_conditions | JSONB | NOT NULL | 159509风险触发条件 |
| current_values | JSONB | NOT NULL | 159509当前值 |
| threshold_values | JSONB | NOT NULL | 159509风险阈值 |
| resolution_status | VARCHAR(20) | DEFAULT 'OPEN' | 解决状态 |
| resolution_time | TIMESTAMP | NULL | 解决时间 |
| created_at | TIMESTAMP | DEFAULT NOW() | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT NOW() | 更新时间 |

## 3. 数据库性能优化策略

### 3.1 索引策略

#### 3.1.1 主键索引
- 所有表使用UUID作为主键
- 自动创建B-tree索引
- 保证唯一性和快速查找

#### 3.1.2 外键索引
```sql
-- AI交易员相关索引
CREATE INDEX idx_ai_traders_created_by ON ai_traders(created_by);
CREATE INDEX idx_ai_trader_configs_trader_id ON ai_trader_configs(trader_id);
CREATE INDEX idx_ai_trader_states_trader_id ON ai_trader_states(trader_id);
CREATE INDEX idx_ai_trader_profiles_trader_id ON ai_trader_profiles(trader_id);

-- 交易相关索引
CREATE INDEX idx_trading_orders_trader_id ON trading_orders(trader_id);
CREATE INDEX idx_trading_executions_trader_id ON trading_executions(trader_id);
CREATE INDEX idx_trading_executions_order_id ON trading_executions(order_id);

-- 持仓管理索引
CREATE INDEX idx_positions_trader_id ON positions(trader_id);
CREATE INDEX idx_position_history_trader_id ON position_history(trader_id);

-- 绩效指标索引
CREATE INDEX idx_performance_metrics_trader_id ON performance_metrics(trader_id);

-- 时间相关索引
CREATE INDEX idx_trading_orders_created_at ON trading_orders(created_at);
CREATE INDEX idx_market_data_timestamp ON market_data(timestamp);
CREATE INDEX idx_technical_indicators_timestamp ON technical_indicators(timestamp);
```

#### 3.1.3 业务查询索引
```sql
-- 订单状态查询索引
CREATE INDEX idx_trading_orders_status ON trading_orders(status);
CREATE INDEX idx_trading_orders_symbol_status ON trading_orders(symbol, status);
CREATE INDEX idx_trading_orders_symbol_side ON trading_orders(symbol, side);

-- AI交易员状态索引
CREATE INDEX idx_ai_traders_status ON ai_traders(status);
CREATE INDEX idx_ai_traders_performance_level ON ai_traders(performance_level);
CREATE INDEX idx_ai_trader_states_work_status ON ai_trader_states(work_status);
CREATE INDEX idx_ai_trader_states_health_status ON ai_trader_states(health_status);

-- 持仓查询索引
CREATE INDEX idx_positions_symbol ON positions(symbol);
CREATE INDEX idx_positions_trader_symbol ON positions(trader_id, symbol);
CREATE INDEX idx_positions_active ON positions(is_active);

-- 159509市场数据查询索引（专门优化）
CREATE INDEX idx_market_data_159509_timestamp ON market_data(timestamp) WHERE symbol = '159509';
CREATE INDEX idx_market_data_159509_type ON market_data(data_type) WHERE symbol = '159509';
CREATE INDEX idx_technical_indicators_159509_type ON technical_indicators(indicator_type) WHERE symbol = '159509';

-- 风险事件查询索引
CREATE INDEX idx_risk_events_level_status ON risk_events(risk_level, resolution_status);
CREATE INDEX idx_risk_events_trader_type ON risk_events(trader_id, event_type);
```

#### 3.1.4 JSONB索引
```sql
-- AI档案数据索引
CREATE INDEX idx_ai_trader_profiles_data_gin ON ai_trader_profiles USING GIN(profile_data);

-- 市场数据JSONB索引
CREATE INDEX idx_technical_indicators_values_gin ON technical_indicators USING GIN(indicator_values);
```

## 4. 数据验证规则和约束条件

### 4.1 数据完整性约束

#### 4.1.1 159509专用CHECK约束
```sql
-- 159509专用约束
ALTER TABLE positions ADD CONSTRAINT chk_symbol_159509_only
CHECK (symbol = '159509');

ALTER TABLE trading_orders ADD CONSTRAINT chk_symbol_159509_only
CHECK (symbol = '159509');

ALTER TABLE trading_executions ADD CONSTRAINT chk_symbol_159509_only
CHECK (symbol = '159509');

ALTER TABLE market_data ADD CONSTRAINT chk_symbol_159509_only
CHECK (symbol = '159509');

ALTER TABLE technical_indicators ADD CONSTRAINT chk_symbol_159509_only
CHECK (symbol = '159509');

-- 数值范围约束
ALTER TABLE ai_traders ADD CONSTRAINT chk_max_position_quantity_positive
CHECK (max_position_quantity > 0);

ALTER TABLE trading_orders ADD CONSTRAINT chk_quantity_positive
CHECK (quantity > 0);

ALTER TABLE trading_orders ADD CONSTRAINT chk_price_positive
CHECK (price IS NULL OR price > 0);

-- 枚举值约束
ALTER TABLE ai_traders ADD CONSTRAINT chk_status_valid
CHECK (status IN ('CREATED', 'ACTIVE', 'SUSPENDED', 'RETIRED'));

ALTER TABLE trading_orders ADD CONSTRAINT chk_side_valid
CHECK (side IN ('BUY', 'SELL'));
```

### 4.2 外键约束

#### 4.2.1 引用完整性
```sql
-- 外键约束定义
ALTER TABLE ai_traders ADD CONSTRAINT fk_ai_traders_created_by
FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL;

ALTER TABLE trading_orders ADD CONSTRAINT fk_trading_orders_trader_id
FOREIGN KEY (trader_id) REFERENCES ai_traders(id) ON DELETE CASCADE;

ALTER TABLE positions ADD CONSTRAINT fk_positions_trader_id
FOREIGN KEY (trader_id) REFERENCES ai_traders(id) ON DELETE CASCADE;
```

---

## 总结

本数据库设计文档详细描述了海天AI纳斯达克交易系统的核心数据库架构设计，包括：

### 1. **核心数据表（14张）**
- **基础用户模块**：users
- **AI交易员模块**：ai_traders（合并了配置信息）、ai_trader_states、ai_trader_profiles
- **交易数据模块**：trading_orders、trading_executions、positions、position_history
- **市场数据模块**：market_data、technical_indicators
- **绩效指标模块**：performance_metrics
- **系统日志模块**：system_logs、audit_logs、risk_events

### 2. **核心特性**
- **159509专业化**：所有交易相关表都强制限制为159509
- **AI交易员管理**：完整的AI交易员生命周期管理
- **交易执行跟踪**：从订单到执行的完整记录
- **持仓管理**：精确的持仓跟踪和历史记录
- **绩效评估**：多维度的绩效指标计算
- **风险控制**：专门的风险事件监控

### 3. **性能优化**
- **索引策略**：针对业务查询模式的专门索引优化
- **JSONB支持**：灵活的AI档案和配置数据存储
- **约束验证**：完整的数据完整性保障

该设计遵循MVP原则，专注于核心功能，为系统的稳定运行和业务扩展提供了坚实的数据基础。
