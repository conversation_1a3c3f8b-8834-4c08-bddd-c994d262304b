# Git配置和规范文档

## 文档信息

- **创建日期**: 2025-01-13
- **维护团队**: 海天AI纳斯达克交易系统开发团队
- **适用范围**: 整个项目开发生命周期
- **技术基础**: Git 2.40+, Python 3.13.2, FastAPI 0.116.1, Vue.js 3.5.x

## 版本更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 1.0.0 | 2025-01-13 | 初始版本，建立Git配置和规范体系 | 系统架构师 |

## 目录

- [1. Git Flow分支模型](#1-git-flow分支模型)
- [2. 分支命名规范](#2-分支命名规范)
- [3. 提交消息规范](#3-提交消息规范)
- [4. Git配置文件说明](#4-git配置文件说明)
- [5. Git钩子配置](#5-git钩子配置)
- [6. 合并策略](#6-合并策略)
- [7. 冲突解决流程](#7-冲突解决流程)

## 1. Git Flow分支模型

### 1.1 分支架构

```
main (生产环境)
├── dev (开发集成)
│   ├── feature/ai-trader-core
│   ├── feature/trading-engine
│   ├── feature/frontend-dashboard
│   └── feature/risk-management
├── hotfix/critical-bug-fix
└── release/v1.0.0
```

### 1.2 分支职责

| 分支类型 | 用途 | 生命周期 | 合并目标 |
|----------|------|----------|----------|
| **main** | 生产环境代码，随时可部署 | 永久 | - |
| **dev** | 开发环境集成，功能测试 | 永久 | main |
| **feature/** | 功能开发分支 | 临时 | dev |
| **hotfix/** | 紧急修复分支 | 临时 | main + dev |
| **release/** | 发布准备分支 | 临时 | main + dev |

### 1.3 分支保护规则

- **main分支**: 受保护，禁止直接推送，必须通过PR
- **dev分支**: 受保护，禁止直接推送，必须通过PR
- **feature分支**: 开放推送，但合并需要代码审查

## 2. 分支命名规范

### 2.1 功能分支 (feature/)

```bash
feature/模块名-功能描述
```

**示例**:
- `feature/ai-trader-decision-engine`
- `feature/trading-order-execution`
- `feature/frontend-dashboard-ui`
- `feature/risk-management-system`

### 2.2 修复分支 (hotfix/)

```bash
hotfix/问题描述-YYYYMMDD
```

**示例**:
- `hotfix/trading-timeout-20250113`
- `hotfix/memory-leak-20250113`

### 2.3 发布分支 (release/)

```bash
release/v主版本.次版本.修订版本
```

**示例**:
- `release/v1.0.0`
- `release/v1.1.0`
- `release/v2.0.0`

## 3. 提交消息规范

### 3.1 Conventional Commits格式

```
<type>(<scope>): <description>

[optional body]

[optional footer(s)]
```

### 3.2 提交类型 (type)

| 类型 | 说明 | 示例 |
|------|------|------|
| **feat** | 新功能 | `feat(ai-trader): 添加决策引擎` |
| **fix** | 修复bug | `fix(trading): 修复订单超时问题` |
| **docs** | 文档更新 | `docs(api): 更新API文档` |
| **style** | 代码格式调整 | `style: 修复代码缩进` |
| **refactor** | 代码重构 | `refactor(engine): 优化交易引擎结构` |
| **perf** | 性能优化 | `perf(ai): 优化AI推理速度` |
| **test** | 测试相关 | `test(trader): 添加单元测试` |
| **chore** | 构建/工具变动 | `chore: 更新依赖包版本` |
| **ci** | CI配置变动 | `ci: 添加自动化测试` |
| **build** | 构建系统变动 | `build: 更新Docker配置` |

### 3.3 作用域 (scope)

| 作用域 | 说明 |
|--------|------|
| **ai-trader** | AI交易员模块 |
| **trading** | 交易引擎模块 |
| **frontend** | 前端界面模块 |
| **api** | API接口模块 |
| **config** | 配置相关 |
| **docs** | 文档相关 |

### 3.4 提交消息示例

```bash
# 功能开发
feat(ai-trader): 实现多AI交易员并行决策机制

添加了6个独立AI交易员的并行决策功能：
- 每个AI交易员独立分析市场数据
- 实现决策冲突检测和处理机制
- 支持个性化参数配置

Closes #123

# 问题修复
fix(trading): 修复QMT接口连接超时问题

- 增加连接重试机制
- 优化超时参数配置
- 添加连接状态监控

# 文档更新
docs(system): 更新系统架构文档

更新了AI交易员架构设计说明
```

## 4. Git配置文件说明

### 4.1 .gitignore文件

**主要忽略内容**:
- Python缓存文件 (`__pycache__/`, `*.pyc`)
- Node.js依赖 (`node_modules/`)
- 环境变量文件 (`.env*`)
- AI模型文件 (`*.pkl`, `*.h5`)
- 日志文件 (`logs/`, `*.log`)
- 敏感配置 (`*secrets*`, `*config.json`)

### 4.2 .gitattributes文件

**主要配置**:
- 行尾符规范化 (`* text=auto`)
- 二进制文件标记 (`*.pkl binary`)
- 合并策略 (`package.json merge=union`)
- LFS大文件管理 (`*.h5 filter=lfs`)

## 5. Git钩子配置

### 5.1 commit-msg钩子

**功能**:
- 验证Conventional Commits格式
- 检查提交消息长度
- 支持中文提交消息
- 跳过合并和回滚提交检查

**配置位置**: `.git/hooks/commit-msg`

### 5.2 pre-commit钩子

**检查项目**:
- Python语法和代码风格
- JavaScript/TypeScript ESLint
- 文件大小限制
- 敏感信息检测
- JSON格式验证
- TODO/FIXME标记提醒

**配置位置**: `.git/hooks/pre-commit`

## 6. 合并策略

### 6.1 Fast-Forward合并

**适用场景**: feature分支合并到dev分支
```bash
git checkout dev
git merge --ff-only feature/branch-name
```

### 6.2 No-Fast-Forward合并

**适用场景**: dev分支合并到main分支
```bash
git checkout main
git merge --no-ff dev
```

### 6.3 Squash合并

**适用场景**: 清理提交历史
```bash
git merge --squash feature/branch-name
git commit -m "feat: 合并功能分支"
```

## 7. 冲突解决流程

### 7.1 冲突检测

```bash
# 检查冲突状态
git status

# 查看冲突文件
git diff --name-only --diff-filter=U
```

### 7.2 冲突解决

1. **手动解决冲突**
   ```bash
   # 编辑冲突文件，解决冲突标记
   # <<<<<<< HEAD
   # 当前分支内容
   # =======
   # 合并分支内容
   # >>>>>>> branch-name
   ```

2. **标记解决完成**
   ```bash
   git add 冲突文件名
   ```

3. **完成合并**
   ```bash
   git commit -m "resolve: 解决合并冲突"
   ```

### 7.3 冲突预防

- 定期同步主分支更新
- 保持功能分支生命周期短
- 避免同时修改相同文件
- 使用代码审查发现潜在冲突

## 8. 最佳实践

### 8.1 提交频率

- 功能完整时提交，避免半成品提交
- 每次提交包含单一逻辑变更
- 提交前运行测试确保代码质量

### 8.2 分支管理

- 及时删除已合并的功能分支
- 定期清理本地和远程分支
- 保持分支命名的一致性

### 8.3 代码审查

- 所有代码变更必须通过Pull Request
- 审查者需要检查代码质量和业务逻辑
- 关键功能需要架构师参与审查
