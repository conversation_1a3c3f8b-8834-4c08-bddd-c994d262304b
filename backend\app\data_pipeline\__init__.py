"""
海天AI纳斯达克交易系统 - 数据处理管道模块
基于: 项目手册4.3节数据处理管道设计
创建日期: 2025年8月1日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: 实时数据处理管道，支持毫秒级数据处理和分发
"""

from .collector import (
    DataCollector,
    QMTDataCollector,
    MockDataCollector
)
from .processor import (
    DataProcessor,
    MarketDataProcessor,
    TickDataProcessor
)
from .indicators import (
    TechnicalIndicatorCalculator,
    RSICalculator,
    MACDCalculator,
    MACalculator,
    BollingerBandsCalculator,
    VolumeIndicatorCalculator
)
from .distributor import (
    DataDistributor,
    WebSocketDistributor,
    AITraderDistributor
)
from .storage import (
    DataStorage,
    DatabaseStorage,
    CacheStorage
)
from .pipeline import (
    DataPipeline,
    RealTimeDataPipeline,
    get_data_pipeline,
    get_global_pipeline,
    shutdown_global_pipeline
)

__all__ = [
    # 数据采集
    "DataCollector",
    "QMTDataCollector", 
    "MockDataCollector",
    
    # 数据处理
    "DataProcessor",
    "MarketDataProcessor",
    "TickDataProcessor",
    
    # 技术指标计算
    "TechnicalIndicatorCalculator",
    "RSICalculator",
    "MACDCalculator",
    "MACalculator",
    "BollingerBandsCalculator",
    "VolumeIndicatorCalculator",
    
    # 数据分发
    "DataDistributor",
    "WebSocketDistributor",
    "AITraderDistributor",
    
    # 数据存储
    "DataStorage",
    "DatabaseStorage",
    "CacheStorage",
    
    # 数据管道
    "DataPipeline",
    "RealTimeDataPipeline",
    "get_data_pipeline",
    "get_global_pipeline",
    "shutdown_global_pipeline"
]
