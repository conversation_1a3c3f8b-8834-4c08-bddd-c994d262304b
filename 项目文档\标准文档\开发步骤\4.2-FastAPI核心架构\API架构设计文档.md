# 海天AI纳斯达克交易系统 - API架构设计文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025年7月31日
- **技术栈**: FastAPI 0.116.1 + Python 3.13.2
- **开发步骤**: 4.2 FastAPI核心架构

## 1. 架构概述

### 1.1 设计原则
- **分层架构**: API层、业务逻辑层、数据访问层、基础设施层
- **依赖注入**: 使用FastAPI内置DI系统实现松耦合
- **异步优先**: 全面采用async/await模式提升性能
- **模块化设计**: 按功能域划分模块，便于维护和扩展
- **安全优先**: JWT认证、权限控制、数据验证

### 1.2 核心组件
```
backend/app/
├── main.py                 # 应用入口和配置
├── core/                   # 核心配置模块
│   ├── settings.py         # 配置管理
│   ├── database.py         # 数据库连接
│   ├── security.py         # 安全认证
│   └── exceptions.py       # 异常处理
├── api/                    # API接口层
│   ├── deps.py            # 依赖注入
│   └── v1/                # API版本1
│       ├── api.py         # 路由汇总
│       └── endpoints/     # 具体端点
└── middleware/            # 中间件（集成在main.py）
```

## 2. 中间件架构

### 2.1 中间件栈（按执行顺序）
1. **SecurityHeadersMiddleware**: 安全头设置
2. **PerformanceMonitoringMiddleware**: 性能监控
3. **RequestLoggingMiddleware**: 请求日志
4. **CORSMiddleware**: 跨域处理

### 2.2 中间件功能

#### 2.2.1 请求日志中间件
- 生成唯一请求ID
- 记录请求开始和完成时间
- 监控处理时间
- 添加响应头：X-Request-ID, X-Process-Time

#### 2.2.2 性能监控中间件
- 监控请求处理时间
- 慢请求告警（>5秒）
- 性能指标收集

#### 2.2.3 安全头中间件
- X-Content-Type-Options: nosniff
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- Referrer-Policy: strict-origin-when-cross-origin

## 3. 依赖注入系统

### 3.1 认证依赖
```python
# 认证链
get_current_user_token() -> get_current_user() -> get_current_active_user()

# 权限检查
require_scopes(["read", "write", "admin"])
```

### 3.2 数据库依赖
```python
# 同步数据库会话
get_database_session() -> Session

# 异步数据库会话  
get_async_database_session() -> AsyncSession
```

### 3.3 配置依赖
```python
get_settings()          # 全局配置
get_security_settings() # 安全配置
get_trading_settings()  # 交易配置
get_ai_model_settings() # AI模型配置
```

### 3.4 日志依赖
```python
get_logger(name)        # 通用日志器
get_api_logger()        # API专用日志器
get_trading_logger()    # 交易专用日志器
get_ai_logger()         # AI专用日志器
```

## 4. API路由架构

### 4.1 版本控制
- 基础路径: `/api/v1`
- 版本隔离: 每个版本独立的路由模块
- 向后兼容: 保持旧版本API可用

### 4.2 路由分组

#### 4.2.1 认证管理 (`/api/v1/auth`)
- `POST /login` - 用户登录
- `POST /logout` - 用户登出
- `POST /refresh` - 刷新令牌
- `GET /me` - 获取当前用户信息
- `POST /register` - 用户注册

#### 4.2.2 AI交易员管理 (`/api/v1/traders`)
- `GET /` - 获取交易员列表
- `POST /` - 创建新交易员
- `GET /{trader_id}` - 获取交易员详情
- `PUT /{trader_id}` - 更新交易员配置
- `DELETE /{trader_id}` - 删除交易员
- `POST /{trader_id}/start` - 启动交易员
- `POST /{trader_id}/stop` - 停止交易员

#### 4.2.3 交易执行 (`/api/v1/trading`)
- `GET /positions` - 获取持仓信息
- `GET /orders` - 获取订单历史
- `POST /orders` - 手动下单
- `DELETE /orders/{order_id}` - 撤销订单
- `GET /market-data` - 获取市场数据
- `GET /performance` - 获取交易绩效

#### 4.2.4 监控统计 (`/api/v1/monitoring`)
- `GET /system-status` - 获取系统状态
- `GET /performance-metrics` - 获取性能指标
- `GET /risk-metrics` - 获取风险指标
- `GET /trading-statistics` - 获取交易统计
- `GET /logs` - 获取系统日志

## 5. 异常处理架构

### 5.1 异常层次结构
```
Exception
└── AITradingException (基础异常)
    ├── DatabaseException (数据库异常)
    ├── AuthenticationException (认证异常)
    ├── AuthorizationException (授权异常)
    ├── ValidationException (验证异常)
    ├── BusinessLogicException (业务逻辑异常)
    ├── ExternalServiceException (外部服务异常)
    │   ├── AIModelException (AI模型异常)
    │   └── QMTException (QMT接口异常)
    └── TradingException (交易异常)
        └── RiskControlException (风险控制异常)
```

### 5.2 异常处理器
- `ai_trading_exception_handler`: 自定义异常处理
- `http_exception_handler`: HTTP异常处理
- `validation_exception_handler`: 验证异常处理
- `general_exception_handler`: 通用异常处理

### 5.3 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {},
    "timestamp": "2025-07-31T16:00:00"
  },
  "data": null,
  "meta": {
    "status_code": 400,
    "request_id": "req-uuid"
  }
}
```

## 6. 安全架构

### 6.1 认证机制
- **JWT令牌**: 访问令牌 + 刷新令牌
- **令牌过期**: 访问令牌30分钟，刷新令牌7天
- **密码哈希**: bcrypt算法
- **令牌验证**: 每个受保护端点验证

### 6.2 权限控制
- **基于范围**: read, write, admin, trading, monitoring, risk
- **权限检查**: 装饰器模式实现
- **用户上下文**: 请求级别的用户信息传递

### 6.3 安全配置
- **CORS**: 配置允许的源、方法、头部
- **安全头**: 防XSS、点击劫持等攻击
- **API限流**: 100请求/分钟（可配置）

## 7. 性能优化

### 7.1 异步处理
- 全面使用async/await
- 异步数据库操作
- 异步外部服务调用

### 7.2 连接池
- 数据库连接池配置
- Redis连接池（预留）
- HTTP客户端连接池

### 7.3 监控指标
- 请求响应时间
- 数据库查询时间
- 内存和CPU使用率
- 慢请求告警

## 8. 扩展性设计

### 8.1 模块化
- 按功能域划分模块
- 清晰的接口定义
- 依赖注入解耦

### 8.2 版本控制
- API版本隔离
- 向后兼容策略
- 渐进式升级

### 8.3 外部服务集成
- QMT接口适配器
- AI模型服务适配器
- 缓存服务适配器
- 消息队列适配器（预留）

## 9. 部署架构

### 9.1 容器化
- Docker镜像构建
- 多阶段构建优化
- 环境变量配置

### 9.2 配置管理
- 环境变量优先
- 配置文件支持
- 敏感信息加密

### 9.3 健康检查
- 应用健康检查端点
- 数据库连接检查
- 外部服务状态检查

## 10. 总结

本架构设计实现了：
- ✅ 分层架构和模块化设计
- ✅ 完整的中间件栈
- ✅ 依赖注入系统
- ✅ 版本化API路由
- ✅ 全面的异常处理
- ✅ JWT安全认证
- ✅ 性能监控和优化
- ✅ 扩展性和可维护性

为后续的业务逻辑实现和系统扩展奠定了坚实的基础。
