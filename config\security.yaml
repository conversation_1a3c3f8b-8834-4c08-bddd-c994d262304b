# ================================
# 安全配置文件
# ================================
# 
# 此文件配置AI交易系统的安全相关参数
# 包括认证、授权、加密、审计等安全措施

# JWT认证配置
jwt:
  algorithm: "HS256"
  access_token_expire_minutes: 30
  refresh_token_expire_days: 7
  issuer: "ai-trading-system"
  audience: "ai-trading-users"

# API安全配置
api_security:
  # 速率限制
  rate_limiting:
    enabled: true
    default_limit: 100  # 每分钟请求数
    window_seconds: 60
    
    # 不同端点的特殊限制
    endpoint_limits:
      "/api/v1/trading/execute": 10
      "/api/v1/ai/decision": 20
      "/api/v1/data/realtime": 200
  
  # CORS配置
  cors:
    allow_origins:
      - "http://localhost:3000"
      - "http://localhost:8080"
    allow_methods:
      - "GET"
      - "POST"
      - "PUT"
      - "DELETE"
      - "OPTIONS"
    allow_headers:
      - "Content-Type"
      - "Authorization"
      - "X-Requested-With"
    allow_credentials: true
    max_age: 86400

# 数据加密配置
encryption:
  # 敏感数据加密
  sensitive_data_encryption:
    enabled: true
    algorithm: "AES-256-GCM"
    key_rotation_days: 90
    
  # 数据库字段加密
  database_encryption:
    enabled: true
    encrypted_fields:
      - "ai_trader_profiles.trading_rules"
      - "trading_records.decision_details"
      - "user_accounts.api_keys"
    
  # 传输加密
  transport_encryption:
    tls_version: "1.3"
    cipher_suites:
      - "TLS_AES_256_GCM_SHA384"
      - "TLS_CHACHA20_POLY1305_SHA256"

# 访问控制配置
access_control:
  # 角色定义
  roles:
    admin:
      permissions:
        - "system.manage"
        - "trading.manage"
        - "ai.manage"
        - "data.read"
        - "data.write"
        - "audit.read"
    
    trader:
      permissions:
        - "trading.read"
        - "ai.read"
        - "data.read"
        - "profile.manage"
    
    viewer:
      permissions:
        - "trading.read"
        - "data.read"
        - "dashboard.view"
  
  # 资源保护
  protected_resources:
    - path: "/api/v1/admin/*"
      roles: ["admin"]
    - path: "/api/v1/trading/execute"
      roles: ["admin", "trader"]
    - path: "/api/v1/ai/config"
      roles: ["admin"]

# 审计配置
audit:
  # 审计日志
  logging:
    enabled: true
    log_level: "INFO"
    log_format: "json"
    
    # 审计事件
    events:
      - "user.login"
      - "user.logout"
      - "trading.execute"
      - "ai.decision"
      - "config.change"
      - "system.error"
      - "security.violation"
    
    # 日志保留
    retention_days: 365
    archive_after_days: 90
  
  # 实时监控
  real_time_monitoring:
    enabled: true
    alert_on_events:
      - "security.violation"
      - "trading.large_loss"
      - "system.critical_error"
    
    # 异常检测
    anomaly_detection:
      enabled: true
      thresholds:
        login_attempts: 5
        api_calls_per_minute: 1000
        trading_frequency: 50

# 密钥管理
key_management:
  # 密钥轮换
  rotation:
    enabled: true
    jwt_key_rotation_days: 30
    encryption_key_rotation_days: 90
    api_key_rotation_days: 180
  
  # 密钥存储
  storage:
    type: "environment"  # environment, vault, file
    backup_enabled: true
    backup_encryption: true
  
  # 密钥强度要求
  strength_requirements:
    min_length: 32
    require_special_chars: true
    require_numbers: true
    require_uppercase: true
    require_lowercase: true

# 网络安全
network_security:
  # IP白名单
  ip_whitelist:
    enabled: false
    allowed_ips: []
  
  # 防火墙规则
  firewall:
    enabled: true
    block_suspicious_ips: true
    rate_limit_violations_threshold: 10
  
  # DDoS防护
  ddos_protection:
    enabled: true
    max_connections_per_ip: 100
    connection_timeout: 30

# 数据保护
data_protection:
  # 个人信息保护
  privacy:
    data_minimization: true
    purpose_limitation: true
    retention_limits: true
  
  # 数据备份安全
  backup_security:
    encryption_enabled: true
    access_control_enabled: true
    integrity_verification: true
  
  # 数据销毁
  data_destruction:
    secure_deletion: true
    verification_required: true
    audit_trail: true

# 合规配置
compliance:
  # 监管要求
  regulatory:
    data_residency: "CN"  # 数据驻留要求
    audit_trail_required: true
    encryption_required: true
  
  # 标准遵循
  standards:
    - "ISO 27001"
    - "SOC 2"
    - "PCI DSS"
  
  # 报告要求
  reporting:
    security_reports: true
    compliance_reports: true
    incident_reports: true

# 安全监控
security_monitoring:
  # 威胁检测
  threat_detection:
    enabled: true
    ml_based_detection: true
    signature_based_detection: true
  
  # 入侵检测
  intrusion_detection:
    enabled: true
    real_time_alerts: true
    automated_response: true
  
  # 漏洞扫描
  vulnerability_scanning:
    enabled: true
    scan_frequency: "weekly"
    auto_remediation: false

# 事件响应
incident_response:
  # 响应团队
  response_team:
    primary_contact: "<EMAIL>"
    escalation_levels: 3
    response_time_sla: 30  # 分钟
  
  # 自动响应
  automated_response:
    enabled: true
    actions:
      - "block_suspicious_ip"
      - "disable_compromised_account"
      - "alert_security_team"
  
  # 恢复程序
  recovery_procedures:
    backup_restoration: true
    service_continuity: true
    forensic_analysis: true
