# =============================================================================
# 海天AI纳斯达克交易系统 - Docker Compose 统一配置
# 基于: 项目手册4.1节MVP版本技术栈配置
# 创建日期: 2025年7月13日
# 最后更新: 2025年7月30日 (前端安全漏洞修复后版本同步)
# 前端技术栈: Vue.js 3.5.x + TypeScript 5.7.x + Vite 6.0.x + Vitest 3.2.x + Element Plus 2.9.x
# 后端技术栈: FastAPI 0.116.1 + Python 3.13.2 + PostgreSQL 17.5
# 部署架构: 开发和生产统一环境
# =============================================================================

# 项目名称配置
name: AI_Nasdaq_trading

services:
  # =============================================================================
  # 网关服务 (Nginx 反向代理)
  # =============================================================================
  nginx:
    build:
      context: .
      dockerfile: ./nginx/Dockerfile
    restart: unless-stopped
    ports:
      - "${NGINX_HTTP_PORT}:80"
      - "${NGINX_HTTPS_PORT}:443"
    volumes:
      # 配置文件挂载
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      # 前端静态文件挂载
      - ../frontend/dist:/usr/share/nginx/html:ro
      # 上传文件目录
      - ../uploads:/var/www/uploads:ro
      # SSL证书目录
      - ./nginx/certs:/etc/nginx/ssl:ro
      # 日志目录
      - ./logs/nginx:/var/log/nginx
    depends_on:
      backend:
        condition: service_healthy
      frontend:
        condition: service_started
    networks:
      - ai_trading
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost/nginx-health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          memory: ${NGINX_MEMORY_LIMIT}
          cpus: "0.5"
        reservations:
          memory: ${NGINX_MEMORY_RESERVATION}
          cpus: "0.25"

  # =============================================================================
  # 前端服务 (Vue.js 3.5.x + TypeScript 5.7.x + Vite 6.0.x + Vitest 3.2.x)
  # =============================================================================
  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
    restart: unless-stopped
    ports:
      - "${FRONTEND_PORT}:80"
    env_file: .env
    environment:
      - VITE_API_BASE_URL=${VITE_API_BASE_URL}
    networks:
      - ai_trading
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost/nginx-health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    deploy:
      resources:
        limits:
          memory: ${FRONTEND_MEMORY_LIMIT}
        reservations:
          memory: ${FRONTEND_MEMORY_RESERVATION}

  # =============================================================================
  # 后端服务 (FastAPI 0.116.1 + Python 3.13.2)
  # =============================================================================
  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    restart: unless-stopped
    ports:
      - "${BACKEND_PORT}:8001"
    volumes:
      # 源代码挂载支持热重载
      - ../backend/app:/app/app
      - ../backend/tests:/app/tests
      - ../backend/requirements.txt:/app/requirements.txt
      # AI交易员档案文件存储
      - ../data/ai_traders:/app/data/ai_traders
      - ../data/logs:/app/data/logs
      - ../uploads:/app/uploads
    env_file: .env
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - PYTHONPATH=/app
    depends_on:
      db:
        condition: service_healthy
    networks:
      - ai_trading
    command: ${BACKEND_START_COMMAND}
    deploy:
      replicas: ${BACKEND_REPLICAS}
      resources:
        limits:
          memory: ${BACKEND_MEMORY_LIMIT}
        reservations:
          memory: ${BACKEND_MEMORY_RESERVATION}
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8001/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # =============================================================================
  # 数据库服务 (PostgreSQL 17.5)
  # =============================================================================
  db:
    image: postgres:17.5-alpine
    restart: unless-stopped
    env_file: .env
    ports:
      - "${POSTGRES_PORT}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
    networks:
      - ai_trading
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}" ]
      interval: ${DB_HEALTH_INTERVAL}
      timeout: ${DB_HEALTH_TIMEOUT}
      retries: ${DB_HEALTH_RETRIES}
    deploy:
      resources:
        limits:
          memory: ${DB_MEMORY_LIMIT}
          cpus: "1.0"
        reservations:
          memory: ${DB_MEMORY_RESERVATION}
          cpus: "0.5"

# =============================================================================
# 网络配置
# =============================================================================
networks:
  ai_trading:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# 数据卷配置
# =============================================================================
volumes:
  # PostgreSQL数据持久化
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_DIR}/postgres

  # 后端应用数据
  backend_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_DIR}/backend

  # 日志数据
  logs_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_DIR}/logs
