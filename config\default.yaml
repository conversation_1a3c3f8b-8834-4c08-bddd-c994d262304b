# ================================
# 海天AI纳斯达克交易系统默认配置
# ================================
# 
# 此文件包含系统的默认配置值
# 环境变量会覆盖这些默认值
# 请勿在此文件中存储敏感信息

# 应用配置
app:
  name: "AI Trading System"
  version: "1.0.0"
  description: "海天AI纳斯达克交易系统"
  
# 服务配置
server:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  
# 数据库默认配置
database:
  pool_size: 20
  max_overflow: 30
  pool_timeout: 30
  pool_recycle: 3600
  ssl_mode: "prefer"
  
# AI模型默认配置
ai_models:
  timeout: 60
  max_concurrent_requests: 10
  request_retry_times: 3
  request_retry_delay: 1.0
  
# QMT接口默认配置
qmt:
  session_timeout: 300
  max_concurrent_orders: 3
  order_timeout: 10
  data_update_interval: 1
  tick_data_buffer_size: 1000
  
# 交易系统默认配置
trading:
  # 全局参数
  max_usable_capital_ratio: 0.8
  max_single_trade_ratio: 0.1
  max_single_stock_ratio: 0.15
  
  # AI交易员配置
  ai_trader_count: 8
  
  # 风险控制
  daily_loss_limit_ratio: 0.03
  max_drawdown_ratio: 0.08
  
  # 交易时间
  market_open_time: "09:30"
  market_close_time: "15:00"
  daily_review_time: "15:10"
  
  # 数据配置
  data_snapshot_interval: 1
  
# 安全配置
security:
  jwt_algorithm: "HS256"
  access_token_expire_minutes: 30
  api_rate_limit: 100
  api_rate_limit_window: 60
  cors_origins:
    - "http://localhost:3000"
    - "http://localhost:8080"
    
# Redis配置
redis:
  database: 0
  max_connections: 20
  connection_timeout: 5
  default_ttl: 3600
  
# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_enabled: true
  file_path: "logs/app.log"
  file_max_size: "10MB"
  file_backup_count: 5
  console_enabled: true
