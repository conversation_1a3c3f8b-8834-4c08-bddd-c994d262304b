# 容器化部署运维指南

## 文档信息

| 项目 | 内容 |
|------|------|
| 创建日期 | 2025年7月13日 |
| 维护团队 | 海天AI量化交易开发团队 |
| 文档范围 | 容器化环境搭建 - 部署运维操作指南 |
| 技术基础 | Docker + Docker Compose + 运维最佳实践 |

## 版本更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 1.0.0 | 2025-07-13 | 初始版本，完整运维指南 | 开发团队 |

## 目录

- [1. 环境准备和安装](#1-环境准备和安装)
- [2. 容器构建和部署](#2-容器构建和部署)
- [3. 服务启动和停止](#3-服务启动和停止)
- [4. 健康检查和监控](#4-健康检查和监控)
- [5. 日志管理和调试](#5-日志管理和调试)
- [6. 数据备份和恢复](#6-数据备份和恢复)
- [7. 安全最佳实践](#7-安全最佳实践)
- [8. 故障排除指南](#8-故障排除指南)

## 1. 环境准备和安装

### 1.1 系统要求

#### 最低配置要求
| 组件 | 开发环境 | 生产环境 |
|------|----------|----------|
| CPU | 2核心 | 4核心 |
| 内存 | 8GB | 16GB |
| 存储 | 50GB | 200GB |
| 网络 | 100Mbps | 1Gbps |

#### 操作系统支持
- Ubuntu 20.04 LTS 或更高版本
- CentOS 8 或更高版本
- Windows 10/11 Pro（开发环境）
- macOS 12 或更高版本（开发环境）

### 1.2 Docker环境安装

#### Ubuntu/Debian系统
```bash
# 更新包索引
sudo apt-get update

# 安装必要的包
sudo apt-get install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release

# 添加Docker官方GPG密钥
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# 设置稳定版仓库
echo \
  "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
  $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# 安装Docker Engine
sudo apt-get update
sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 添加用户到docker组
sudo usermod -aG docker $USER
```

#### CentOS/RHEL系统
```bash
# 安装必要的包
sudo yum install -y yum-utils

# 添加Docker仓库
sudo yum-config-manager \
    --add-repo \
    https://download.docker.com/linux/centos/docker-ce.repo

# 安装Docker Engine
sudo yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 添加用户到docker组
sudo usermod -aG docker $USER
```

### 1.3 环境验证

```bash
# 验证Docker安装
docker --version
docker compose version

# 测试Docker运行
docker run hello-world

# 检查Docker服务状态
sudo systemctl status docker
```

## 2. 容器构建和部署

### 2.1 项目初始化

#### 克隆项目代码
```bash
# 克隆项目仓库
git clone <repository-url> ai_nasdaq_trading
cd ai_nasdaq_trading

# 检查项目结构
ls -la
```

#### 环境配置
```bash
# 进入基础设施目录
cd infrastructure

# 复制环境变量模板
cp .env.example .env

# 编辑环境变量（根据实际环境修改）
nano .env
```

### 2.2 统一环境部署

#### 构建和启动服务
```bash
# 构建所有服务镜像
docker compose build

# 启动所有服务
docker compose up -d

# 查看服务状态
docker compose ps

# 查看服务日志
docker compose logs -f
```

#### 验证服务运行
```bash
# 检查Nginx服务
curl http://localhost/nginx-health

# 检查后端API服务
curl http://localhost/api/health

# 检查数据库连接
docker compose exec backend python -c "from app.database import engine; print('Database connected')"

# 检查内存缓存状态
docker compose exec backend python -c "print('Memory cache ready')"
```

### 2.3 环境配置说明

根据项目手册要求，系统采用统一环境配置：

#### 统一环境优势
- **环境一致性**：开发即生产，避免环境差异导致的问题
- **部署简化**：一套配置，多环境复用
- **测试可靠**：开发环境测试结果与生产环境一致
- **维护便利**：统一的运维流程和故障排查
- **成本降低**：减少环境维护和配置管理成本

#### 配置管理
```bash
# 统一使用单一配置文件
docker compose up -d

# 通过环境变量调整配置
nano .env

# 重启应用配置生效
docker compose restart
```

## 3. 服务启动和停止

### 3.1 基本操作命令

#### 服务启动
```bash
# 启动所有服务
docker compose up -d

# 启动指定服务
docker compose up -d nginx backend

# 重新构建并启动
docker compose up -d --build

# 强制重新创建容器
docker compose up -d --force-recreate
```

#### 服务停止
```bash
# 停止所有服务
docker compose down

# 停止指定服务
docker compose stop nginx backend

# 停止并删除卷
docker compose down -v

# 停止并删除镜像
docker compose down --rmi all
```

### 3.2 服务管理

#### 重启服务
```bash
# 重启所有服务
docker compose restart

# 重启指定服务
docker compose restart backend

# 优雅重启（等待当前请求完成）
docker compose kill -s SIGTERM backend
docker compose up -d backend
```

#### 扩展服务
```bash
# 扩展后端服务实例
docker compose up -d --scale backend=3

# 查看扩展后的服务
docker compose ps
```

## 4. 健康检查和监控

### 4.1 健康检查配置

#### 内置健康检查
```bash
# 查看所有服务健康状态
docker compose ps

# 查看特定服务健康状态
docker inspect --format='{{.State.Health.Status}}' $(docker compose ps -q backend)

# 查看健康检查日志
docker inspect --format='{{range .State.Health.Log}}{{.Output}}{{end}}' $(docker compose ps -q backend)
```

#### 自定义健康检查
```bash
# 手动执行健康检查
docker compose exec nginx curl -f http://localhost/nginx-health
docker compose exec backend curl -f http://localhost:8000/health
docker compose exec db pg_isready -U postgres
```

### 4.2 性能监控

#### 资源使用监控
```bash
# 查看容器资源使用情况
docker stats

# 查看特定容器资源使用
docker stats $(docker compose ps -q)

# 查看容器详细信息
docker compose exec backend top
```

#### 日志监控
```bash
# 实时查看所有服务日志
docker compose logs -f

# 查看特定服务日志
docker compose logs -f backend

# 查看最近的日志
docker compose logs --tail=100 backend

# 查看特定时间段日志
docker compose logs --since="2025-01-01T00:00:00" --until="2025-01-01T23:59:59" backend
```

## 5. 日志管理和调试

### 5.1 日志收集

#### 容器日志
```bash
# 导出所有服务日志
docker compose logs > system_logs.txt

# 导出特定服务日志
docker compose logs backend > backend_logs.txt

# 导出JSON格式日志
docker compose logs --json backend > backend_logs.json
```

#### 应用日志
```bash
# 查看应用内部日志
docker compose exec backend ls -la /app/logs/
docker compose exec backend tail -f /app/logs/app.log

# 复制日志文件到主机
docker compose cp backend:/app/logs/app.log ./app.log
```

### 5.2 调试技巧

#### 容器调试
```bash
# 进入容器内部调试
docker compose exec backend bash
docker compose exec frontend sh
docker compose exec db psql -U postgres -d ai_nasdaq_trading

# 查看容器内部进程
docker compose exec backend ps aux

# 查看容器网络配置
docker compose exec backend ip addr show
```

#### 网络调试
```bash
# 测试容器间网络连通性
docker compose exec frontend ping backend
docker compose exec backend ping db

# 查看网络配置
docker network ls
docker network inspect ai_trading_ai_trading
```

## 6. 数据备份和恢复

### 6.1 数据库备份

#### PostgreSQL备份
```bash
# 创建数据库备份
docker compose exec db pg_dump -U postgres ai_nasdaq_trading > backup_$(date +%Y%m%d_%H%M%S).sql

# 压缩备份文件
gzip backup_$(date +%Y%m%d_%H%M%S).sql

# 自动化备份脚本
cat > backup_db.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/ai_trading/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR
docker compose exec -T db pg_dump -U postgres ai_nasdaq_trading | gzip > $BACKUP_DIR/db_backup_$TIMESTAMP.sql.gz
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete
EOF
chmod +x backup_db.sh
```

#### 内存缓存备份
```bash
# MVP版本使用内存缓存，无需单独备份
# 缓存数据会随应用重启自动重建
echo "Memory cache will be rebuilt on application restart"
```

### 6.2 数据恢复

#### PostgreSQL恢复
```bash
# 停止应用服务
docker compose stop backend

# 恢复数据库
gunzip -c backup_20250113_120000.sql.gz | docker compose exec -T db psql -U postgres ai_nasdaq_trading

# 重启应用服务
docker compose start backend
```

#### 内存缓存恢复
```bash
# MVP版本内存缓存无需恢复
# 重启应用服务即可自动重建缓存
docker compose restart backend
```

## 7. 安全最佳实践

### 7.1 容器安全

#### 镜像安全
```bash
# 扫描镜像漏洞
docker scout cves ai_nasdaq_trading_backend

# 更新基础镜像
docker compose build --no-cache

# 清理未使用的镜像
docker image prune -a
```

#### 运行时安全
```bash
# 检查容器权限
docker compose exec backend id
docker compose exec backend cat /proc/1/status | grep Cap

# 检查网络安全
docker network inspect ai_trading_ai_trading

# 检查文件权限
docker compose exec backend ls -la /app/
```

### 7.2 访问控制

#### 网络访问控制
```bash
# 配置防火墙规则
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 5432/tcp

# 检查端口监听
netstat -tlnp | grep -E ':(80|443|5432|8000)'
```

#### 密钥管理
```bash
# 生成强密码
openssl rand -base64 32

# 检查环境变量安全
docker compose config | grep -i password

# 使用Docker secrets（生产环境推荐）
echo "your-secret-password" | docker secret create db_password -
```

## 8. 故障排除指南

### 8.1 常见问题

#### 服务启动失败
```bash
# 检查服务状态
docker compose ps

# 查看错误日志
docker compose logs backend

# 检查端口占用
netstat -tlnp | grep :8000

# 检查磁盘空间
df -h
docker system df
```

#### 网络连接问题
```bash
# 检查网络配置
docker network ls
docker network inspect ai_trading_ai_trading

# 测试DNS解析
docker compose exec backend nslookup db
docker compose exec backend ping db

# 重建网络
docker compose down
docker network prune
docker compose up -d
```

### 8.2 性能问题

#### 内存不足
```bash
# 检查内存使用
free -h
docker stats

# 清理系统缓存
sudo sync && sudo sysctl vm.drop_caches=3

# 调整容器内存限制
# 编辑docker-compose.yml中的memory限制
```

#### 磁盘空间不足
```bash
# 检查磁盘使用
df -h
docker system df

# 清理Docker资源
docker system prune -a
docker volume prune

# 清理日志文件
sudo journalctl --vacuum-time=7d
```

### 8.3 应急处理

#### 服务紧急重启
```bash
# 快速重启所有服务
docker compose restart

# 强制重启问题服务
docker compose kill backend
docker compose up -d backend

# 回滚到上一个版本
git checkout HEAD~1
docker compose up -d --build
```

#### 数据紧急恢复
```bash
# 从最新备份恢复
./backup_db.sh restore latest

# 检查数据完整性
docker compose exec db psql -U postgres -d ai_nasdaq_trading -c "SELECT COUNT(*) FROM users;"

# 验证应用功能
curl http://localhost/api/health
```
