"""
Configuration management and validation for AI Trading System.
"""

import os
import logging
from pathlib import Path
from typing import Optional
from functools import lru_cache

from .settings import Settings


logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self._settings: Optional[Settings] = None
        self._config_loaded = False
    
    @property
    def settings(self) -> Settings:
        """获取配置实例"""
        if self._settings is None:
            self._settings = self._load_settings()
        return self._settings
    
    def _load_settings(self) -> Settings:
        """加载配置"""
        try:
            # 创建配置实例
            settings = Settings()
            
            # 加载YAML配置文件
            settings.load_yaml_configs()
            
            # 验证配置
            self._validate_configuration(settings)
            
            # 标记配置已加载
            self._config_loaded = True
            
            logger.info("Configuration loaded successfully")
            return settings
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
    
    def _validate_configuration(self, settings: Settings) -> None:
        """验证配置"""
        
        # 验证数据库连接
        self._validate_database_config(settings.database)
        
        # 验证AI模型配置
        self._validate_ai_models_config(settings.ai_models)
        
        # 验证QMT配置
        self._validate_qmt_config(settings.qmt)
        
        # 验证交易配置
        self._validate_trading_config(settings.trading)
        
        # 验证安全配置
        self._validate_security_config(settings.security)
        
        logger.info("Configuration validation completed")
    
    def _validate_database_config(self, db_config) -> None:
        """验证数据库配置"""
        # 在开发环境中，数据库密码可以为空（使用默认值）
        # 在生产环境中，应该设置密码

        if db_config.port < 1 or db_config.port > 65535:
            raise ValueError("Database port must be between 1 and 65535")

        if db_config.pool_size < 1:
            raise ValueError("Database pool size must be greater than 0")
    
    def _validate_ai_models_config(self, ai_config) -> None:
        """验证AI模型配置"""
        # 至少需要一个AI模型的API密钥
        has_api_key = any([
            ai_config.openai_api_key,
            ai_config.anthropic_api_key,
            ai_config.google_api_key
        ])
        
        # 在开发环境中，可以没有AI模型API密钥
        # 在生产环境中，至少需要一个API密钥
        if not has_api_key:
            logger.warning("No AI model API keys configured - system will run in development mode")
        
        if ai_config.max_concurrent_requests < 1:
            raise ValueError("Max concurrent requests must be greater than 0")
    
    def _validate_qmt_config(self, qmt_config) -> None:
        """验证QMT配置"""
        # 在开发环境中，QMT配置可以为空
        if not qmt_config.username or not qmt_config.password:
            logger.warning("QMT credentials not configured - trading functionality will be disabled")
        
        if not qmt_config.account_id:
            logger.warning("QMT account ID not configured")
        
        if qmt_config.port < 1 or qmt_config.port > 65535:
            raise ValueError("QMT port must be between 1 and 65535")
    
    def _validate_trading_config(self, trading_config) -> None:
        """验证交易配置"""
        if trading_config.total_capital <= 0:
            raise ValueError("Total capital must be greater than 0")
        
        if not (0 < trading_config.max_usable_capital_ratio <= 1):
            raise ValueError("Max usable capital ratio must be between 0 and 1")
        
        if not (0 < trading_config.max_single_trade_ratio <= 1):
            raise ValueError("Max single trade ratio must be between 0 and 1")
        
        if trading_config.ai_trader_count < 1:
            raise ValueError("AI trader count must be greater than 0")
    
    def _validate_security_config(self, security_config) -> None:
        """验证安全配置"""
        if not security_config.secret_key:
            logger.warning("Secret key not configured - using default development key")
        
        if security_config.secret_key and len(security_config.secret_key) < 32:
            logger.warning(f"Secret key is too short ({len(security_config.secret_key)} chars) - should be at least 32 characters")
        
        if security_config.access_token_expire_minutes < 1:
            raise ValueError("Access token expire minutes must be greater than 0")
    
    def reload_configuration(self) -> None:
        """重新加载配置"""
        self._settings = None
        self._config_loaded = False
        logger.info("Configuration reloaded")
    
    def is_configuration_loaded(self) -> bool:
        """检查配置是否已加载"""
        return self._config_loaded
    
    def get_config_summary(self) -> dict:
        """获取配置摘要（不包含敏感信息）"""
        if not self._config_loaded:
            return {"status": "not_loaded"}
        
        settings = self.settings
        return {
            "status": "loaded",
            "app_name": settings.app_name,
            "app_version": settings.app_version,
            "debug": settings.debug,
            "database": {
                "host": settings.database.host,
                "port": settings.database.port,
                "database": settings.database.database,
                "pool_size": settings.database.pool_size,
            },
            "ai_models": {
                "openai_enabled": bool(settings.ai_models.openai_api_key),
                "anthropic_enabled": bool(settings.ai_models.anthropic_api_key),
                "google_enabled": bool(settings.ai_models.google_api_key),
                "max_concurrent": settings.ai_models.max_concurrent_requests,
            },
            "qmt": {
                "host": settings.qmt.host,
                "port": settings.qmt.port,
                "account_configured": bool(settings.qmt.account_id),
            },
            "trading": {
                "total_capital": settings.trading.total_capital,
                "ai_trader_count": settings.trading.ai_trader_count,
                "max_usable_ratio": settings.trading.max_usable_capital_ratio,
            },
            "redis": {
                "host": settings.redis.host,
                "port": settings.redis.port,
                "database": settings.redis.database,
            }
        }


# 全局配置管理器实例
config_manager = ConfigManager()

# 便捷访问配置的函数
@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（带缓存）"""
    return config_manager.settings


# 导出配置实例
settings = get_settings()


def validate_environment() -> bool:
    """验证环境配置"""
    try:
        # 检查必需的环境变量
        required_env_vars = [
            "APP_DB_PASSWORD",
            "SECRET_KEY"
        ]
        
        missing_vars = []
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            logger.error(f"Missing required environment variables: {missing_vars}")
            return False
        
        # 验证配置加载
        config_manager.settings
        
        logger.info("Environment validation passed")
        return True
        
    except Exception as e:
        logger.error(f"Environment validation failed: {e}")
        return False


def create_config_directories() -> None:
    """创建配置相关目录"""
    directories = [
        "config",
        "logs",
        "data/ai_traders",
        "data/backups"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        logger.info(f"Created directory: {directory}")


# 初始化时创建必要目录
create_config_directories()
