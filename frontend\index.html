<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>海天AI纳斯达克交易系统</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    h1 {
      color: #409eff;
      text-align: center;
      margin-bottom: 30px;
    }

    .status {
      text-align: center;
      padding: 20px;
      background: #e8f4fd;
      border-radius: 4px;
      margin: 20px 0;
    }

    .btn {
      background: #409eff;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      margin: 10px;
    }

    .btn:hover {
      background: #337ab7;
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>海天AI纳斯达克交易系统</h1>
    <div class="status">
      <p>系统状态: <span id="status">检查中...</span></p>
      <button class="btn" onclick="checkHealth()">检查系统健康状态</button>
    </div>
    <div>
      <h3>系统功能</h3>
      <ul>
        <li>AI交易员管理</li>
        <li>实时市场数据</li>
        <li>交易策略配置</li>
        <li>风险控制</li>
        <li>性能监控</li>
      </ul>
    </div>
  </div>

  <script>
    async function checkHealth() {
      const statusEl = document.getElementById('status');
      statusEl.textContent = '检查中...';

      try {
        const response = await fetch('/api/health');
        if (response.ok) {
          statusEl.textContent = '系统正常';
          statusEl.style.color = 'green';
        } else {
          statusEl.textContent = '系统异常';
          statusEl.style.color = 'red';
        }
      } catch (error) {
        statusEl.textContent = '连接失败';
        statusEl.style.color = 'orange';
      }
    }

    // 页面加载时自动检查
    window.onload = function () {
      checkHealth();
    };
  </script>
</body>

</html>