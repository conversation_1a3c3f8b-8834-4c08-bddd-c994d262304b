# 海天AI纳斯达克交易系统 - Pre-commit钩子配置
# 基于Python 3.13.2 + FastAPI 0.116.1技术栈
# 创建日期: 2025年7月13日

repos:
  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black
        language_version: python3.13
        args: [--line-length=88]
        files: ^backend/.*\.py$

  # Python导入排序
  - repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
      - id: isort
        args: [--profile=black, --line-length=88]
        files: ^backend/.*\.py$

  # Python代码风格检查
  - repo: https://github.com/pycqa/flake8
    rev: 7.1.1
    hooks:
      - id: flake8
        args: [--config=.flake8]
        files: ^backend/.*\.py$

  # Python类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.14.0
    hooks:
      - id: mypy
        args: [--config-file=backend/pyproject.toml]
        files: ^backend/.*\.py$
        additional_dependencies:
          - types-requests
          - types-PyYAML
          - types-python-dateutil

  # Python安全漏洞扫描
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.10
    hooks:
      - id: bandit
        args: [-c, bandit.yaml]
        files: ^backend/.*\.py$

  # 通用文件检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      # 检查文件大小
      - id: check-added-large-files
        args: [--maxkb=1000]
      
      # 检查合并冲突标记
      - id: check-merge-conflict
      
      # 检查YAML语法
      - id: check-yaml
        files: \.(yaml|yml)$
        exclude: ^infrastructure/
      
      # 检查JSON语法
      - id: check-json
        files: \.json$
      
      # 检查TOML语法
      - id: check-toml
        files: \.toml$
      
      # 移除行尾空白
      - id: trailing-whitespace
        args: [--markdown-linebreak-ext=md]
      
      # 确保文件以换行符结尾
      - id: end-of-file-fixer
      
      # 检查私钥文件
      - id: detect-private-key
      
      # 检查AWS凭证
      - id: detect-aws-credentials
        args: [--allow-missing-credentials]

  # Dockerfile检查
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        files: Dockerfile.*

  # 前端代码检查（如果存在）
  - repo: local
    hooks:
      # ESLint检查
      - id: eslint
        name: ESLint
        entry: bash -c 'cd frontend && npm run lint'
        language: system
        files: ^frontend/.*\.(js|ts|vue)$
        pass_filenames: false
        
      # Prettier格式化
      - id: prettier
        name: Prettier
        entry: bash -c 'cd frontend && npm run format'
        language: system
        files: ^frontend/.*\.(js|ts|vue|json|css|scss|md)$
        pass_filenames: false

# 全局配置
default_language_version:
  python: python3.13

# 排除的文件和目录
exclude: |
  (?x)^(
    migrations/.*|
    .*\.min\.(js|css)|
    node_modules/.*|
    dist/.*|
    build/.*|
    \.git/.*|
    \.venv/.*|
    venv/.*|
    __pycache__/.*|
    \.pytest_cache/.*|
    \.mypy_cache/.*|
    logs/.*|
    data/.*|
    uploads/.*
  )$

# 失败时的行为
fail_fast: false

# 最小pre-commit版本要求
minimum_pre_commit_version: 3.0.0
