import vue from '@vitejs/plugin-vue'
import { defineConfig } from 'vite'

/**
 * 海天AI纳斯达克交易系统 - 前端构建配置
 * 技术栈版本: Vite 6.0.x + Vue.js 3.5.x + TypeScript 5.7.x
 * 测试框架: Vitest 3.2.x
 * 最后更新: 2025-07-30
 */
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  server: {
    host: '0.0.0.0',
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8001',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser'
  }
})
