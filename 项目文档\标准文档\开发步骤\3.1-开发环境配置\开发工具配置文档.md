# 开发工具配置文档

## 文档信息
- **创建日期**: 2025年7月13日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: VS Code IDE配置和开发工具集成
- **技术基础**: Vue.js 3.5.x + FastAPI 0.116.1 + Python 3.13.2

## 版本更新记录
- v1.0: 初始版本，完成VS Code配置和插件推荐

## 目录
1. [VS Code配置指南](#1-vs-code配置指南)
2. [推荐插件列表](#2-推荐插件列表)
3. [代码格式化配置](#3-代码格式化配置)
4. [调试环境配置](#4-调试环境配置)
5. [快捷键设置](#5-快捷键设置)

---

## 1. VS Code配置指南

### 1.1 工作区设置

项目已配置完整的VS Code工作区设置，位于`.vscode/settings.json`：

```json
{
  "python.defaultInterpreterPath": "D:\\GHJ\\AI\\conda_envs_pkgs\\envs\\AI_Nasdaq_trading\\python.exe",
  "python.terminal.activateEnvironment": true,
  "editor.formatOnSave": true,
  "editor.tabSize": 2,
  "editor.rulers": [88, 120]
}
```

### 1.2 Python解释器配置

1. **设置Python解释器**
   - 按 `Ctrl+Shift+P` 打开命令面板
   - 输入 `Python: Select Interpreter`
   - 选择 `AI_Nasdaq_trading` conda环境

2. **验证Python路径**
   ```
   D:\GHJ\AI\conda_envs_pkgs\envs\AI_Nasdaq_trading\python.exe
   ```

### 1.3 终端配置

- **默认终端**: Command Prompt (Windows)
- **环境变量**: 自动设置PYTHONPATH
- **自动激活**: conda环境自动激活

## 2. 推荐插件列表

### 2.1 Python开发插件

| 插件名称 | 插件ID | 用途 | 必需性 |
|---------|--------|------|--------|
| **Python** | ms-python.python | Python语言支持 | ✅ 必需 |
| **Pylint** | ms-python.pylint | 代码质量检查 | ✅ 必需 |
| **Black Formatter** | ms-python.black-formatter | 代码格式化 | ✅ 必需 |
| **isort** | ms-python.isort | 导入排序 | ✅ 必需 |
| **Flake8** | ms-python.flake8 | 代码风格检查 | 🔶 推荐 |

### 2.2 Vue.js和前端插件

| 插件名称 | 插件ID | 用途 | 必需性 |
|---------|--------|------|--------|
| **Vue - Official** | Vue.volar | Vue.js语言支持 | ✅ 必需 |
| **TypeScript Vue Plugin** | Vue.vscode-typescript-vue-plugin | Vue TypeScript支持 | ✅ 必需 |
| **ESLint** | dbaeumer.vscode-eslint | JavaScript代码检查 | ✅ 必需 |
| **Prettier** | esbenp.prettier-vscode | 代码格式化 | ✅ 必需 |
| **Tailwind CSS IntelliSense** | bradlc.vscode-tailwindcss | CSS智能提示 | 🔶 推荐 |

### 2.3 开发工具插件

| 插件名称 | 插件ID | 用途 | 必需性 |
|---------|--------|------|--------|
| **Docker** | ms-azuretools.vscode-docker | Docker支持 | ✅ 必需 |
| **PostgreSQL** | ms-ossdata.vscode-postgresql | 数据库管理 | ✅ 必需 |
| **GitLens** | eamodio.gitlens | Git增强 | 🔶 推荐 |
| **Thunder Client** | ms-vscode.vscode-thunder-client | API测试 | 🔶 推荐 |
| **REST Client** | humao.rest-client | HTTP请求测试 | 🔶 推荐 |

### 2.4 代码质量插件

| 插件名称 | 插件ID | 用途 | 必需性 |
|---------|--------|------|--------|
| **Code Spell Checker** | streetsidesoftware.code-spell-checker | 拼写检查 | 🔶 推荐 |
| **Trailing Spaces** | shardulm94.trailing-spaces | 清理多余空格 | 🔶 推荐 |
| **Indent Rainbow** | oderwat.indent-rainbow | 缩进可视化 | 🔶 推荐 |

### 2.5 插件安装命令

```bash
# 一键安装所有推荐插件
code --install-extension ms-python.python
code --install-extension Vue.volar
code --install-extension ms-azuretools.vscode-docker
code --install-extension dbaeumer.vscode-eslint
code --install-extension esbenp.prettier-vscode
```

## 3. 代码格式化配置

### 3.1 Python代码格式化

**Black配置** (pyproject.toml):
```toml
[tool.black]
line-length = 88
target-version = ['py313']
include = '\.pyi?$'
extend-exclude = '''
/(
  migrations
  | venv
  | env
)/
'''
```

**isort配置** (pyproject.toml):
```toml
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
```

### 3.2 前端代码格式化

**Prettier配置** (.prettierrc):
```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 80,
  "endOfLine": "lf"
}
```

**ESLint配置** (.eslintrc.js):
```javascript
module.exports = {
  extends: [
    '@vue/typescript/recommended',
    'prettier'
  ],
  rules: {
    'no-console': 'warn',
    'no-debugger': 'warn'
  }
}
```

### 3.3 自动格式化设置

VS Code设置中已配置：
- **保存时格式化**: `"editor.formatOnSave": true`
- **保存时自动修复**: `"editor.codeActionsOnSave"`
- **导入自动排序**: `"source.organizeImports"`

## 4. 调试环境配置

### 4.1 Python调试配置

已配置的调试配置 (`.vscode/launch.json`):

```json
{
  "name": "Python: FastAPI Backend",
  "type": "python",
  "request": "launch",
  "module": "uvicorn",
  "args": [
    "app.main:app",
    "--reload",
    "--host", "0.0.0.0",
    "--port", "8000"
  ],
  "cwd": "${workspaceFolder}/backend"
}
```

### 4.2 调试功能

1. **断点调试**
   - 点击行号左侧设置断点
   - F5启动调试
   - F10单步执行，F11进入函数

2. **变量监视**
   - 调试面板查看变量值
   - 添加监视表达式
   - 调用堆栈跟踪

3. **调试控制台**
   - 实时执行Python代码
   - 查看变量状态
   - 执行调试命令

### 4.3 Docker调试

```json
{
  "name": "Docker: Attach to Backend",
  "type": "python",
  "request": "attach",
  "connect": {
    "host": "localhost",
    "port": 5678
  },
  "pathMappings": [
    {
      "localRoot": "${workspaceFolder}/backend",
      "remoteRoot": "/app"
    }
  ]
}
```

## 5. 快捷键设置

### 5.1 常用快捷键

| 功能 | 快捷键 | 说明 |
|------|--------|------|
| **命令面板** | `Ctrl+Shift+P` | 打开命令面板 |
| **快速打开** | `Ctrl+P` | 快速打开文件 |
| **格式化文档** | `Shift+Alt+F` | 格式化当前文档 |
| **查找替换** | `Ctrl+H` | 查找和替换 |
| **多光标选择** | `Ctrl+D` | 选择下一个相同内容 |
| **注释切换** | `Ctrl+/` | 切换行注释 |

### 5.2 调试快捷键

| 功能 | 快捷键 | 说明 |
|------|--------|------|
| **开始调试** | `F5` | 启动调试 |
| **单步执行** | `F10` | 逐行执行 |
| **进入函数** | `F11` | 进入函数内部 |
| **跳出函数** | `Shift+F11` | 跳出当前函数 |
| **停止调试** | `Shift+F5` | 停止调试会话 |

### 5.3 自定义快捷键

可在 `keybindings.json` 中添加自定义快捷键：

```json
[
  {
    "key": "ctrl+shift+t",
    "command": "workbench.action.terminal.new"
  },
  {
    "key": "ctrl+shift+r",
    "command": "python.execInTerminal"
  }
]
```

## 6. 开发工具集成

### 6.1 Git集成

- **源代码管理**: 内置Git支持
- **GitLens**: 增强Git功能
- **分支管理**: 可视化分支操作
- **提交历史**: 查看文件修改历史

### 6.2 终端集成

- **集成终端**: `Ctrl+`` 打开终端
- **多终端**: 支持多个终端实例
- **任务运行**: 通过tasks.json配置任务
- **自动激活**: conda环境自动激活

### 6.3 任务配置

已配置的开发任务 (`.vscode/tasks.json`):

- **激活Conda环境**
- **安装Python依赖**
- **安装前端依赖**
- **启动后端服务**
- **启动前端服务**
- **Docker Compose操作**
- **运行测试**
- **代码格式化**

### 6.4 使用方法

1. **运行任务**: `Ctrl+Shift+P` → `Tasks: Run Task`
2. **选择任务**: 从列表中选择要执行的任务
3. **查看输出**: 在终端面板查看任务执行结果

---

## 附录

### A. 故障排除

**常见问题**:
1. **Python解释器未找到**: 检查conda环境路径
2. **插件无法安装**: 检查网络连接和VS Code版本
3. **格式化不生效**: 检查插件是否启用
4. **调试无法启动**: 检查Python路径和工作目录

### B. 性能优化

**VS Code性能优化**:
- 关闭不必要的插件
- 排除大文件夹的搜索
- 调整自动保存频率
- 使用工作区而非文件夹模式

### C. 团队协作

**统一配置**:
- 使用工作区设置文件
- 共享插件推荐列表
- 统一代码格式化规则
- 版本控制配置文件
