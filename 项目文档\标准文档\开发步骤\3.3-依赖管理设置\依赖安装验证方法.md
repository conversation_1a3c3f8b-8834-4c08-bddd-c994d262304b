# 依赖安装验证方法

## 文档信息
- **创建日期**: 2025年7月13日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: 海天AI纳斯达克交易系统依赖安装验证和测试
- **技术基础**: Poetry + npm + 自动化测试

## 版本更新记录
- v1.0: 初始版本，完成依赖安装验证方法

## 目录
1. [依赖安装验证步骤](#1-依赖安装验证步骤)
2. [版本冲突检测方法](#2-版本冲突检测方法)
3. [环境一致性验证标准](#3-环境一致性验证标准)
4. [自动化验证工具](#4-自动化验证工具)

---

## 1. 依赖安装验证步骤

### 1.1 Python依赖安装验证

**基础安装验证**:
```bash
# 1. 激活虚拟环境
conda activate AI_Nasdaq_trading

# 2. 验证Poetry配置
poetry check
poetry config --list

# 3. 安装依赖
poetry install

# 4. 验证安装结果
poetry show
poetry show --tree
```

**核心依赖验证**:
```bash
# 验证关键包版本
poetry show fastapi | grep "version"
poetry show sqlalchemy | grep "version"
poetry show openai | grep "version"

# 验证包导入
python -c "import fastapi; print(fastapi.__version__)"
python -c "import sqlalchemy; print(sqlalchemy.__version__)"
python -c "import openai; print(openai.__version__)"
```

**功能性验证脚本**:
```python
#!/usr/bin/env python3
# scripts/verify_python_deps.py

import sys
import importlib
from typing import Dict, List

REQUIRED_PACKAGES = {
    'fastapi': '0.116.1',
    'sqlalchemy': '2.0.36',
    'pydantic': '2.10.3',
    'openai': '1.58.1',
    'anthropic': '0.40.0',
    'pandas': '2.2.3',
    'numpy': '2.2.1',
}

def verify_package(package_name: str, expected_version: str) -> bool:
    """验证包的安装和版本"""
    try:
        module = importlib.import_module(package_name)
        actual_version = getattr(module, '__version__', 'unknown')
        
        print(f"✅ {package_name}: {actual_version}")
        
        # 检查版本兼容性
        if expected_version not in actual_version:
            print(f"⚠️  版本不匹配: 期望 {expected_version}, 实际 {actual_version}")
            return False
        
        return True
    except ImportError as e:
        print(f"❌ {package_name}: 导入失败 - {e}")
        return False

def main():
    """主验证函数"""
    print("🔍 开始验证Python依赖...")
    
    failed_packages = []
    
    for package, version in REQUIRED_PACKAGES.items():
        if not verify_package(package, version):
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n❌ 验证失败的包: {', '.join(failed_packages)}")
        sys.exit(1)
    else:
        print("\n✅ 所有Python依赖验证通过!")

if __name__ == "__main__":
    main()
```

### 1.2 前端依赖安装验证

**基础安装验证**:
```bash
# 1. 切换到前端目录
cd frontend

# 2. 验证Node.js环境
node --version  # 应显示 v24.1.0
npm --version   # 应显示 11.2.0+

# 3. 清理并安装依赖
rm -rf node_modules package-lock.json
npm install

# 4. 验证安装结果
npm ls
npm ls --depth=0
```

**核心依赖验证**:
```bash
# 验证关键包版本
npm list vue
npm list typescript
npm list vite
npm list element-plus

# 验证包可用性
npx vue --version
npx tsc --version
npx vite --version
```

**功能性验证脚本**:
```javascript
#!/usr/bin/env node
// scripts/verify_frontend_deps.js

const fs = require('fs');
const path = require('path');

const REQUIRED_PACKAGES = {
  'vue': '^3.5.13',
  'vue-router': '^4.5.0',
  'pinia': '^2.3.0',
  'element-plus': '^2.9.1',
  'axios': '^1.7.9',
  'echarts': '^5.6.0',
  'typescript': '^5.7.2',
  'vite': '^6.0.5'
};

function verifyPackage(packageName, expectedVersion) {
  try {
    const packagePath = path.join('node_modules', packageName, 'package.json');
    
    if (!fs.existsSync(packagePath)) {
      console.log(`❌ ${packageName}: 包未安装`);
      return false;
    }
    
    const packageInfo = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    const actualVersion = packageInfo.version;
    
    console.log(`✅ ${packageName}: ${actualVersion}`);
    
    // 简单版本兼容性检查
    const majorVersion = expectedVersion.replace(/[\^~]/, '').split('.')[0];
    if (!actualVersion.startsWith(majorVersion)) {
      console.log(`⚠️  版本不兼容: 期望 ${expectedVersion}, 实际 ${actualVersion}`);
      return false;
    }
    
    return true;
  } catch (error) {
    console.log(`❌ ${packageName}: 验证失败 - ${error.message}`);
    return false;
  }
}

function main() {
  console.log('🔍 开始验证前端依赖...');
  
  const failedPackages = [];
  
  for (const [packageName, expectedVersion] of Object.entries(REQUIRED_PACKAGES)) {
    if (!verifyPackage(packageName, expectedVersion)) {
      failedPackages.push(packageName);
    }
  }
  
  if (failedPackages.length > 0) {
    console.log(`\n❌ 验证失败的包: ${failedPackages.join(', ')}`);
    process.exit(1);
  } else {
    console.log('\n✅ 所有前端依赖验证通过!');
  }
}

main();
```

### 1.3 构建验证测试

**Python应用构建验证**:
```bash
# 验证FastAPI应用启动
cd backend
poetry run python -c "from app.main import app; print('FastAPI应用导入成功')"

# 验证数据库连接
poetry run python -c "from app.core.database import engine; print('数据库连接配置正确')"

# 验证AI客户端
poetry run python -c "from openai import OpenAI; print('OpenAI客户端可用')"
```

**前端应用构建验证**:
```bash
# 验证TypeScript编译
cd frontend
npm run type-check

# 验证构建过程
npm run build

# 验证构建产物
ls -la dist/
```

## 2. 版本冲突检测方法

### 2.1 Python版本冲突检测

**Poetry冲突检测**:
```bash
# 检查依赖解析冲突
poetry lock --check

# 显示依赖树查看冲突
poetry show --tree

# 检查过时的依赖
poetry show --outdated

# 详细依赖分析
poetry show --tree | grep -E "(├|└)"
```

**手动冲突检测脚本**:
```python
#!/usr/bin/env python3
# scripts/detect_python_conflicts.py

import pkg_resources
from collections import defaultdict

def detect_version_conflicts():
    """检测Python包版本冲突"""
    conflicts = defaultdict(list)
    
    # 获取所有已安装的包
    installed_packages = {pkg.project_name: pkg.version 
                         for pkg in pkg_resources.working_set}
    
    # 检查每个包的依赖
    for pkg in pkg_resources.working_set:
        try:
            for requirement in pkg.requires():
                req_name = requirement.project_name
                req_specs = requirement.specs
                
                if req_name in installed_packages:
                    installed_version = installed_packages[req_name]
                    
                    # 检查版本是否满足要求
                    if not requirement.specifier.contains(installed_version):
                        conflicts[req_name].append({
                            'package': pkg.project_name,
                            'required': str(requirement),
                            'installed': installed_version
                        })
        except Exception as e:
            print(f"检查 {pkg.project_name} 时出错: {e}")
    
    return conflicts

def main():
    print("🔍 检测Python包版本冲突...")
    
    conflicts = detect_version_conflicts()
    
    if conflicts:
        print("\n❌ 发现版本冲突:")
        for package, conflict_list in conflicts.items():
            print(f"\n📦 {package}:")
            for conflict in conflict_list:
                print(f"  - {conflict['package']} 需要 {conflict['required']}")
                print(f"    但安装的是 {conflict['installed']}")
    else:
        print("\n✅ 未发现版本冲突")

if __name__ == "__main__":
    main()
```

### 2.2 前端版本冲突检测

**npm冲突检测**:
```bash
# 检查依赖冲突
npm ls

# 检查过时依赖
npm outdated

# 审计安全问题
npm audit

# 修复自动可修复的问题
npm audit fix
```

**依赖分析工具**:
```bash
# 安装依赖分析工具
npm install -g npm-check-updates

# 检查可更新的依赖
ncu

# 检查主要版本更新
ncu --target major

# 分析包大小
npm install -g webpack-bundle-analyzer
npm run build
npx webpack-bundle-analyzer dist/
```

### 2.3 跨平台兼容性检测

**操作系统兼容性**:
```bash
# 检查平台特定依赖
poetry show --tree | grep -i "platform\|win\|linux\|darwin"

# 检查Python版本兼容性
poetry run python -c "import sys; print(f'Python {sys.version}')"

# 检查关键路径
poetry run python -c "import os; print(f'PYTHONPATH: {os.environ.get(\"PYTHONPATH\", \"未设置\")}')"
```

## 3. 环境一致性验证标准

### 3.1 开发环境一致性检查

**环境指纹生成**:
```bash
#!/bin/bash
# scripts/generate_env_fingerprint.sh

echo "=== 环境指纹报告 ==="
echo "生成时间: $(date)"
echo ""

echo "=== 系统信息 ==="
echo "操作系统: $(uname -s)"
echo "架构: $(uname -m)"
echo ""

echo "=== Python环境 ==="
echo "Python版本: $(python --version)"
echo "Conda环境: $CONDA_DEFAULT_ENV"
echo "Poetry版本: $(poetry --version)"
echo ""

echo "=== Node.js环境 ==="
echo "Node.js版本: $(node --version)"
echo "npm版本: $(npm --version)"
echo ""

echo "=== 依赖哈希 ==="
echo "Python依赖哈希: $(poetry lock --check 2>&1 | grep -o '[a-f0-9]\{64\}' | head -1)"
echo "前端依赖哈希: $(cd frontend && npm ls --json | sha256sum | cut -d' ' -f1)"
```

### 3.2 CI/CD环境验证

**GitHub Actions验证配置**:
```yaml
# .github/workflows/dependency-check.yml
name: Dependency Verification

on: [push, pull_request]

jobs:
  verify-python:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.13.2'
      
      - name: Install Poetry
        run: |
          curl -sSL https://install.python-poetry.org | python3 -
          echo "$HOME/.local/bin" >> $GITHUB_PATH
      
      - name: Verify Python Dependencies
        run: |
          cd backend
          poetry install
          poetry run python scripts/verify_python_deps.py
  
  verify-frontend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '24.1.0'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
      
      - name: Verify Frontend Dependencies
        run: |
          cd frontend
          npm ci
          node scripts/verify_frontend_deps.js
```

### 3.3 团队环境同步验证

**环境同步检查清单**:
```markdown
## 环境同步验证清单

### Python环境
- [ ] Python版本: 3.13.2
- [ ] Conda环境: AI_Nasdaq_trading
- [ ] Poetry版本: 最新稳定版
- [ ] 依赖安装: poetry install 成功
- [ ] 核心包导入: 无错误

### Node.js环境
- [ ] Node.js版本: 24.1.0
- [ ] npm版本: 11.2.0+
- [ ] 依赖安装: npm install 成功
- [ ] 构建测试: npm run build 成功

### 开发工具
- [ ] VS Code配置: Python解释器正确
- [ ] 调试配置: 可以启动调试
- [ ] 代码格式化: Black和Prettier工作正常
- [ ] 类型检查: mypy和TypeScript检查通过
```

## 4. 自动化验证工具

### 4.1 验证脚本集成

**主验证脚本**:
```bash
#!/bin/bash
# scripts/verify_all_dependencies.sh

set -e

echo "🚀 开始完整依赖验证..."

# 验证Python依赖
echo "📍 验证Python依赖..."
cd backend
poetry install
poetry run python scripts/verify_python_deps.py

# 验证前端依赖
echo "📍 验证前端依赖..."
cd ../frontend
npm install
node scripts/verify_frontend_deps.js

# 运行构建测试
echo "📍 运行构建测试..."
npm run build

# 验证后端启动
echo "📍 验证后端启动..."
cd ../backend
timeout 10s poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 &
sleep 5
curl -f http://localhost:8000/health || echo "后端健康检查失败"
pkill -f uvicorn

echo "✅ 所有依赖验证完成!"
```

### 4.2 持续监控工具

**依赖监控脚本**:
```python
#!/usr/bin/env python3
# scripts/monitor_dependencies.py

import subprocess
import json
import smtplib
from datetime import datetime
from email.mime.text import MIMEText

def check_security_vulnerabilities():
    """检查安全漏洞"""
    vulnerabilities = []
    
    # 检查Python安全问题
    try:
        result = subprocess.run(['poetry', 'audit'], 
                              capture_output=True, text=True, cwd='backend')
        if result.returncode != 0:
            vulnerabilities.append(f"Python安全问题: {result.stdout}")
    except Exception as e:
        vulnerabilities.append(f"Python安全检查失败: {e}")
    
    # 检查Node.js安全问题
    try:
        result = subprocess.run(['npm', 'audit', '--json'], 
                              capture_output=True, text=True, cwd='frontend')
        audit_data = json.loads(result.stdout)
        if audit_data.get('metadata', {}).get('vulnerabilities', {}).get('total', 0) > 0:
            vulnerabilities.append(f"Node.js安全问题: {audit_data['metadata']['vulnerabilities']}")
    except Exception as e:
        vulnerabilities.append(f"Node.js安全检查失败: {e}")
    
    return vulnerabilities

def send_alert(vulnerabilities):
    """发送安全警报"""
    if not vulnerabilities:
        return
    
    message = f"""
    依赖安全监控警报
    
    时间: {datetime.now()}
    
    发现的问题:
    {chr(10).join(vulnerabilities)}
    
    请及时处理这些安全问题。
    """
    
    # 这里可以集成邮件或Slack通知
    print("🚨 安全警报:")
    print(message)

def main():
    vulnerabilities = check_security_vulnerabilities()
    send_alert(vulnerabilities)

if __name__ == "__main__":
    main()
```

### 4.3 定期维护任务

**依赖更新检查**:
```bash
#!/bin/bash
# scripts/weekly_dependency_check.sh

echo "📅 每周依赖检查 - $(date)"

# 检查Python依赖更新
echo "🐍 检查Python依赖更新..."
cd backend
poetry show --outdated

# 检查前端依赖更新
echo "🌐 检查前端依赖更新..."
cd ../frontend
npm outdated

# 生成依赖报告
echo "📊 生成依赖报告..."
{
    echo "# 依赖状态报告 - $(date)"
    echo ""
    echo "## Python依赖"
    cd backend && poetry show --tree
    echo ""
    echo "## 前端依赖"
    cd ../frontend && npm ls --depth=0
} > dependency_report_$(date +%Y%m%d).md

echo "✅ 依赖检查完成，报告已生成"
```

---

## 总结

本文档提供了完整的依赖安装验证方法，包括安装验证步骤、版本冲突检测、环境一致性验证和自动化验证工具。通过这些验证方法可以确保海天AI纳斯达克交易系统的依赖环境稳定可靠，为项目开发提供坚实的基础保障。
