"""
Core configuration and settings module for AI Trading System.
"""

from .config import settings
from .settings import (
    DatabaseSettings,
    AIModelSettings,
    QMTSettings,
    TradingSettings,
    SecuritySettings,
    LoggingSettings,
    RedisSettings,
    Settings
)
from .security import (
    verify_password,
    get_password_hash,
    create_access_token,
    create_refresh_token,
    verify_token,
    decode_token,
    TokenData,
    Token
)
from .exceptions import (
    AITradingException,
    DatabaseException,
    AuthenticationException,
    AuthorizationException,
    ValidationException,
    BusinessLogicException,
    ExternalServiceException,
    AIModelException,
    QMTException,
    TradingException,
    RiskControlException
)

__all__ = [
    "settings",
    "DatabaseSettings",
    "AIModelSettings",
    "QMTSettings",
    "TradingSettings",
    "SecuritySettings",
    "LoggingSettings",
    "RedisSettings",
    "Settings",
    # Security functions
    "verify_password",
    "get_password_hash",
    "create_access_token",
    "create_refresh_token",
    "verify_token",
    "decode_token",
    "TokenData",
    "Token",
    # Exception classes
    "AITradingException",
    "DatabaseException",
    "AuthenticationException",
    "AuthorizationException",
    "ValidationException",
    "BusinessLogicException",
    "ExternalServiceException",
    "AIModelException",
    "QMTException",
    "TradingException",
    "RiskControlException"
]
