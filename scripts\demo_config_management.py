#!/usr/bin/env python3
"""
配置管理功能演示脚本

展示AI交易系统配置管理的各种功能
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.app.core.config import config_manager
from backend.app.core.config_validator import validate_configuration


def print_section(title: str):
    """打印章节标题"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")


def print_subsection(title: str):
    """打印子章节标题"""
    print(f"\n{'-'*40}")
    print(f" {title}")
    print(f"{'-'*40}")


def demo_basic_config():
    """演示基础配置功能"""
    print_section("基础配置功能演示")
    
    try:
        # 获取配置实例
        settings = config_manager.settings
        
        print_subsection("应用基础配置")
        print(f"应用名称: {settings.app_name}")
        print(f"应用版本: {settings.app_version}")
        print(f"调试模式: {settings.debug}")
        print(f"服务地址: {settings.host}:{settings.port}")
        
        print_subsection("数据库配置")
        db = settings.database
        print(f"数据库主机: {db.host}")
        print(f"数据库端口: {db.port}")
        print(f"数据库名称: {db.database}")
        print(f"连接池大小: {db.pool_size}")
        print(f"数据库URL: {db.database_url}")
        
        print_subsection("AI模型配置")
        ai = settings.ai_models
        print(f"OpenAI模型: {ai.openai_model}")
        print(f"OpenAI API密钥: {'已配置' if ai.openai_api_key else '未配置'}")
        print(f"Anthropic模型: {ai.anthropic_model}")
        print(f"Anthropic API密钥: {'已配置' if ai.anthropic_api_key else '未配置'}")
        print(f"Google模型: {ai.google_model}")
        print(f"Google API密钥: {'已配置' if ai.google_api_key else '未配置'}")
        print(f"最大并发请求: {ai.max_concurrent_requests}")
        
        print_subsection("QMT交易接口配置")
        qmt = settings.qmt
        print(f"QMT主机: {qmt.host}")
        print(f"QMT端口: {qmt.port}")
        print(f"QMT用户名: {'已配置' if qmt.username else '未配置'}")
        print(f"QMT密码: {'已配置' if qmt.password else '未配置'}")
        print(f"QMT账户ID: {'已配置' if qmt.account_id else '未配置'}")
        
        print_subsection("交易系统配置")
        trading = settings.trading
        print(f"总资金: {trading.total_capital:,.2f} 元")
        print(f"最大可用资金比例: {trading.max_usable_capital_ratio:.1%}")
        print(f"单笔交易最大比例: {trading.max_single_trade_ratio:.1%}")
        print(f"单股票最大仓位比例: {trading.max_single_stock_ratio:.1%}")
        print(f"AI交易员数量: {trading.ai_trader_count}")
        print(f"日最大亏损限制: {trading.daily_loss_limit_ratio:.1%}")
        print(f"最大回撤比例: {trading.max_drawdown_ratio:.1%}")
        
        print_subsection("安全配置")
        security = settings.security
        print(f"JWT算法: {security.algorithm}")
        print(f"访问令牌过期时间: {security.access_token_expire_minutes} 分钟")
        print(f"密钥配置状态: {'已配置' if security.secret_key else '未配置'}")
        print(f"CORS源: {security.cors_origins}")
        
        print_subsection("Redis配置")
        redis = settings.redis
        print(f"Redis主机: {redis.host}")
        print(f"Redis端口: {redis.port}")
        print(f"Redis数据库: {redis.database}")
        print(f"Redis密码: {'已配置' if redis.password else '未配置'}")
        print(f"Redis URL: {redis.redis_url}")
        
        print_subsection("日志配置")
        logging = settings.logging
        print(f"日志级别: {logging.level}")
        print(f"文件日志: {'启用' if logging.file_enabled else '禁用'}")
        print(f"控制台日志: {'启用' if logging.console_enabled else '禁用'}")
        print(f"日志文件路径: {logging.file_path}")
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")


def demo_config_summary():
    """演示配置摘要功能"""
    print_section("配置摘要功能演示")
    
    try:
        summary = config_manager.get_config_summary()
        
        print_subsection("系统状态")
        print(f"配置状态: {summary.get('status', 'Unknown')}")
        print(f"应用名称: {summary.get('app_name', 'Unknown')}")
        print(f"应用版本: {summary.get('app_version', 'Unknown')}")
        print(f"调试模式: {summary.get('debug', False)}")
        
        print_subsection("数据库状态")
        db_info = summary.get('database', {})
        print(f"数据库主机: {db_info.get('host', 'Unknown')}")
        print(f"数据库端口: {db_info.get('port', 'Unknown')}")
        print(f"数据库名称: {db_info.get('database', 'Unknown')}")
        print(f"连接池大小: {db_info.get('pool_size', 'Unknown')}")
        
        print_subsection("AI模型状态")
        ai_info = summary.get('ai_models', {})
        print(f"OpenAI: {'✅ 已启用' if ai_info.get('openai_enabled') else '❌ 未启用'}")
        print(f"Anthropic: {'✅ 已启用' if ai_info.get('anthropic_enabled') else '❌ 未启用'}")
        print(f"Google: {'✅ 已启用' if ai_info.get('google_enabled') else '❌ 未启用'}")
        print(f"最大并发: {ai_info.get('max_concurrent', 'Unknown')}")
        
        print_subsection("QMT状态")
        qmt_info = summary.get('qmt', {})
        print(f"QMT主机: {qmt_info.get('host', 'Unknown')}")
        print(f"QMT端口: {qmt_info.get('port', 'Unknown')}")
        print(f"账户配置: {'✅ 已配置' if qmt_info.get('account_configured') else '❌ 未配置'}")
        
        print_subsection("交易状态")
        trading_info = summary.get('trading', {})
        print(f"总资金: {trading_info.get('total_capital', 'Unknown'):,.2f} 元")
        print(f"AI交易员数量: {trading_info.get('ai_trader_count', 'Unknown')}")
        print(f"最大可用比例: {trading_info.get('max_usable_ratio', 'Unknown'):.1%}")
        
    except Exception as e:
        print(f"❌ 配置摘要生成失败: {e}")


async def demo_config_validation():
    """演示配置验证功能"""
    print_section("配置验证功能演示")
    
    try:
        print("🔍 开始配置验证...")
        results = await validate_configuration()
        
        print_subsection("验证结果总览")
        overall_status = results.get('overall_status', 'unknown')
        status_emoji = {
            'success': '✅',
            'warning': '⚠️',
            'failed': '❌',
            'error': '❌'
        }
        
        print(f"总体状态: {status_emoji.get(overall_status, '❓')} {overall_status.upper()}")
        
        summary = results.get('summary', {})
        print(f"总验证项: {summary.get('total_validations', 0)}")
        print(f"成功: {summary.get('successes', 0)}")
        print(f"警告: {summary.get('warnings', 0)}")
        print(f"错误: {summary.get('errors', 0)}")
        
        print_subsection("详细验证结果")
        validations = results.get('validations', {})
        for name, result in validations.items():
            status = result.get('status', 'unknown')
            emoji = status_emoji.get(status, '❓')
            message = result.get('message', 'No message')
            print(f"{emoji} {name}: {message}")
        
        # 显示错误和警告
        errors = results.get('errors', [])
        if errors:
            print_subsection("错误详情")
            for error in errors:
                print(f"❌ {error}")
        
        warnings = results.get('warnings', [])
        if warnings:
            print_subsection("警告详情")
            for warning in warnings:
                print(f"⚠️ {warning}")
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")


def demo_environment_variables():
    """演示环境变量功能"""
    print_section("环境变量功能演示")
    
    print_subsection("当前环境变量")
    
    # 显示重要的环境变量
    important_vars = [
        "POSTGRES_HOST",
        "POSTGRES_PORT", 
        "POSTGRES_DB",
        "APP_DB_PASSWORD",
        "AI_MODEL_API_KEY",
        "QMT_USERNAME",
        "QMT_PASSWORD",
        "QMT_ACCOUNT_ID",
        "SECRET_KEY",
        "AI_TRADER_COUNT",
        "MAX_USABLE_FUNDS"
    ]
    
    configured_vars = []
    missing_vars = []
    
    for var in important_vars:
        value = os.getenv(var)
        if value:
            configured_vars.append(var)
            # 对敏感信息进行掩码处理
            if any(keyword in var.lower() for keyword in ['password', 'key', 'secret']):
                display_value = "*" * min(len(value), 8)
            else:
                display_value = value
            print(f"✅ {var}: {display_value}")
        else:
            missing_vars.append(var)
            print(f"❌ {var}: 未配置")
    
    print_subsection("环境变量统计")
    print(f"已配置变量: {len(configured_vars)}")
    print(f"缺失变量: {len(missing_vars)}")
    
    if missing_vars:
        print_subsection("建议配置的变量")
        for var in missing_vars:
            print(f"export {var}=your_value_here")


def demo_config_files():
    """演示配置文件功能"""
    print_section("配置文件功能演示")
    
    config_files = [
        "config/default.yaml",
        "config/ai_models.yaml", 
        "config/trading.yaml",
        "config/security.yaml"
    ]
    
    print_subsection("配置文件状态")
    for file_path in config_files:
        path = Path(file_path)
        if path.exists():
            size = path.stat().st_size
            print(f"✅ {file_path}: {size} 字节")
        else:
            print(f"❌ {file_path}: 文件不存在")
    
    print_subsection("环境文件状态")
    env_files = [
        "infrastructure/.env",
        ".env.template"
    ]
    
    for file_path in env_files:
        path = Path(file_path)
        if path.exists():
            size = path.stat().st_size
            print(f"✅ {file_path}: {size} 字节")
        else:
            print(f"❌ {file_path}: 文件不存在")


async def main():
    """主演示函数"""
    print("🚀 AI交易系统配置管理功能演示")
    print("=" * 60)
    
    # 演示各种功能
    demo_basic_config()
    demo_config_summary()
    await demo_config_validation()
    demo_environment_variables()
    demo_config_files()
    
    print_section("演示完成")
    print("✅ 配置管理功能演示已完成")
    print("📖 更多信息请参考: 项目文档/标准文档/开发步骤/3.7-环境变量和配置管理/配置管理完整指南.md")


if __name__ == "__main__":
    asyncio.run(main())
