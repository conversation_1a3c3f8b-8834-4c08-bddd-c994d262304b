"""
Configuration validation and testing utilities.
"""

import os
import asyncio
import logging
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path
import yaml
import psycopg2
import redis
import httpx
from pydantic import ValidationError

from .settings import Settings
from .config import config_manager


logger = logging.getLogger(__name__)


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.validation_results: Dict[str, Any] = {}
    
    async def validate_all(self) -> Dict[str, Any]:
        """验证所有配置"""
        logger.info("Starting comprehensive configuration validation")
        
        # 重置验证结果
        self.validation_results = {
            "overall_status": "unknown",
            "validations": {},
            "errors": [],
            "warnings": []
        }
        
        # 执行各项验证
        validations = [
            ("environment_variables", self._validate_environment_variables),
            ("database_connection", self._validate_database_connection),
            ("redis_connection", self._validate_redis_connection),
            ("ai_models_config", self._validate_ai_models_config),
            ("qmt_config", self._validate_qmt_config),
            ("trading_config", self._validate_trading_config),
            ("security_config", self._validate_security_config),
            ("file_permissions", self._validate_file_permissions),
            ("directory_structure", self._validate_directory_structure),
        ]
        
        for name, validator_func in validations:
            try:
                result = await validator_func()
                self.validation_results["validations"][name] = result
                logger.info(f"Validation '{name}': {'PASSED' if result['status'] == 'success' else 'FAILED'}")
            except Exception as e:
                error_msg = f"Validation '{name}' failed with exception: {str(e)}"
                logger.error(error_msg)
                self.validation_results["validations"][name] = {
                    "status": "error",
                    "message": error_msg,
                    "details": str(e)
                }
                self.validation_results["errors"].append(error_msg)
        
        # 计算总体状态
        self._calculate_overall_status()
        
        logger.info(f"Configuration validation completed. Overall status: {self.validation_results['overall_status']}")
        return self.validation_results
    
    async def _validate_environment_variables(self) -> Dict[str, Any]:
        """验证环境变量"""
        required_vars = [
            "APP_DB_PASSWORD",
            "SECRET_KEY"
        ]
        
        missing_vars = []
        weak_vars = []
        
        for var in required_vars:
            value = os.getenv(var)
            if not value:
                missing_vars.append(var)
            elif var in ["APP_DB_PASSWORD", "SECRET_KEY"] and len(value) < 16:
                weak_vars.append(f"{var} (length: {len(value)})")
        
        if missing_vars:
            return {
                "status": "error",
                "message": f"Missing required environment variables: {missing_vars}",
                "missing_variables": missing_vars
            }
        
        result = {"status": "success", "message": "All required environment variables are present"}
        
        if weak_vars:
            result["warnings"] = f"Weak passwords/keys detected: {weak_vars}"
            self.validation_results["warnings"].append(result["warnings"])
        
        return result
    
    async def _validate_database_connection(self) -> Dict[str, Any]:
        """验证数据库连接"""
        try:
            db_config = self.settings.database
            
            # 测试连接
            conn = psycopg2.connect(
                host=db_config.host,
                port=db_config.port,
                user=db_config.user,
                password=db_config.password,
                database=db_config.database,
                connect_timeout=10
            )
            
            # 测试基本查询
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            
            cursor.close()
            conn.close()
            
            return {
                "status": "success",
                "message": "Database connection successful",
                "database_version": version,
                "connection_details": {
                    "host": db_config.host,
                    "port": db_config.port,
                    "database": db_config.database
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Database connection failed: {str(e)}",
                "error_details": str(e)
            }
    
    async def _validate_redis_connection(self) -> Dict[str, Any]:
        """验证Redis连接"""
        try:
            redis_config = self.settings.redis
            
            # 创建Redis连接
            r = redis.Redis(
                host=redis_config.host,
                port=redis_config.port,
                password=redis_config.password,
                db=redis_config.database,
                socket_timeout=5
            )
            
            # 测试连接
            r.ping()
            
            # 测试基本操作
            test_key = "config_validation_test"
            r.set(test_key, "test_value", ex=10)
            value = r.get(test_key)
            r.delete(test_key)
            
            if value != b"test_value":
                raise Exception("Redis read/write test failed")
            
            return {
                "status": "success",
                "message": "Redis connection successful",
                "connection_details": {
                    "host": redis_config.host,
                    "port": redis_config.port,
                    "database": redis_config.database
                }
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Redis connection failed: {str(e)}",
                "error_details": str(e)
            }
    
    async def _validate_ai_models_config(self) -> Dict[str, Any]:
        """验证AI模型配置"""
        ai_config = self.settings.ai_models
        
        # 检查API密钥
        available_models = []
        unavailable_models = []
        
        if ai_config.openai_api_key:
            available_models.append("OpenAI GPT-4")
        else:
            unavailable_models.append("OpenAI GPT-4")
        
        if ai_config.anthropic_api_key:
            available_models.append("Anthropic Claude")
        else:
            unavailable_models.append("Anthropic Claude")
        
        if ai_config.google_api_key:
            available_models.append("Google Gemini")
        else:
            unavailable_models.append("Google Gemini")
        
        if not available_models:
            return {
                "status": "error",
                "message": "No AI model API keys configured",
                "available_models": available_models,
                "unavailable_models": unavailable_models
            }
        
        # 测试API连接（可选，避免消耗配额）
        result = {
            "status": "success",
            "message": f"AI models configuration valid. Available: {len(available_models)}",
            "available_models": available_models,
            "unavailable_models": unavailable_models
        }
        
        if unavailable_models:
            warning = f"Some AI models are not configured: {unavailable_models}"
            result["warnings"] = warning
            self.validation_results["warnings"].append(warning)
        
        return result
    
    async def _validate_qmt_config(self) -> Dict[str, Any]:
        """验证QMT配置"""
        qmt_config = self.settings.qmt
        
        # 检查必需配置
        required_fields = ["username", "password", "account_id"]
        missing_fields = []
        
        for field in required_fields:
            if not getattr(qmt_config, field):
                missing_fields.append(field)
        
        if missing_fields:
            return {
                "status": "error",
                "message": f"Missing QMT configuration fields: {missing_fields}",
                "missing_fields": missing_fields
            }
        
        # 验证端口范围
        if not (1 <= qmt_config.port <= 65535):
            return {
                "status": "error",
                "message": f"Invalid QMT port: {qmt_config.port}",
                "port": qmt_config.port
            }
        
        return {
            "status": "success",
            "message": "QMT configuration is valid",
            "config_summary": {
                "host": qmt_config.host,
                "port": qmt_config.port,
                "account_configured": bool(qmt_config.account_id)
            }
        }
    
    async def _validate_trading_config(self) -> Dict[str, Any]:
        """验证交易配置"""
        trading_config = self.settings.trading
        
        # 验证资金配置
        if trading_config.total_capital <= 0:
            return {
                "status": "error",
                "message": "Total capital must be greater than 0",
                "total_capital": trading_config.total_capital
            }
        
        # 验证比例配置
        ratio_checks = [
            ("max_usable_capital_ratio", trading_config.max_usable_capital_ratio),
            ("max_single_trade_ratio", trading_config.max_single_trade_ratio),
            ("max_single_stock_ratio", trading_config.max_single_stock_ratio),
            ("daily_loss_limit_ratio", trading_config.daily_loss_limit_ratio),
            ("max_drawdown_ratio", trading_config.max_drawdown_ratio)
        ]
        
        for name, value in ratio_checks:
            if not (0 < value <= 1):
                return {
                    "status": "error",
                    "message": f"Invalid ratio for {name}: {value}",
                    "field": name,
                    "value": value
                }
        
        # 验证AI交易员数量
        if trading_config.ai_trader_count < 1:
            return {
                "status": "error",
                "message": "AI trader count must be greater than 0",
                "ai_trader_count": trading_config.ai_trader_count
            }
        
        return {
            "status": "success",
            "message": "Trading configuration is valid",
            "config_summary": {
                "total_capital": trading_config.total_capital,
                "ai_trader_count": trading_config.ai_trader_count,
                "max_usable_ratio": trading_config.max_usable_capital_ratio
            }
        }
    
    async def _validate_security_config(self) -> Dict[str, Any]:
        """验证安全配置"""
        security_config = self.settings.security
        
        # 验证密钥强度
        if not security_config.secret_key:
            return {
                "status": "warning",
                "message": "Secret key not configured - using development mode",
                "config_summary": {
                    "secret_key_configured": False,
                    "jwt_algorithm": security_config.algorithm,
                    "token_expire_minutes": security_config.access_token_expire_minutes
                }
            }

        if len(security_config.secret_key) < 32:
            return {
                "status": "error",
                "message": f"Secret key too short: {len(security_config.secret_key)} characters",
                "min_required": 32
            }
        
        # 验证JWT配置
        if security_config.access_token_expire_minutes < 1:
            return {
                "status": "error",
                "message": "Access token expire time must be greater than 0",
                "expire_minutes": security_config.access_token_expire_minutes
            }
        
        return {
            "status": "success",
            "message": "Security configuration is valid",
            "config_summary": {
                "secret_key_length": len(security_config.secret_key),
                "jwt_algorithm": security_config.algorithm,
                "token_expire_minutes": security_config.access_token_expire_minutes
            }
        }
    
    async def _validate_file_permissions(self) -> Dict[str, Any]:
        """验证文件权限"""
        critical_files = [
            ".env",
            "config/",
            "logs/",
            "data/"
        ]
        
        permission_issues = []
        
        for file_path in critical_files:
            path = Path(file_path)
            if path.exists():
                # 检查权限（简化版本）
                if not os.access(path, os.R_OK):
                    permission_issues.append(f"{file_path}: not readable")
                if path.is_dir() and not os.access(path, os.W_OK):
                    permission_issues.append(f"{file_path}: not writable")
        
        if permission_issues:
            return {
                "status": "warning",
                "message": f"File permission issues detected: {permission_issues}",
                "issues": permission_issues
            }
        
        return {
            "status": "success",
            "message": "File permissions are adequate"
        }
    
    async def _validate_directory_structure(self) -> Dict[str, Any]:
        """验证目录结构"""
        required_dirs = [
            "config",
            "logs", 
            "data",
            "data/ai_traders",
            "data/backups"
        ]
        
        missing_dirs = []
        created_dirs = []
        
        for dir_path in required_dirs:
            path = Path(dir_path)
            if not path.exists():
                try:
                    path.mkdir(parents=True, exist_ok=True)
                    created_dirs.append(dir_path)
                except Exception as e:
                    missing_dirs.append(f"{dir_path}: {str(e)}")
        
        if missing_dirs:
            return {
                "status": "error",
                "message": f"Failed to create required directories: {missing_dirs}",
                "missing_directories": missing_dirs
            }
        
        result = {
            "status": "success",
            "message": "Directory structure is valid"
        }
        
        if created_dirs:
            result["created_directories"] = created_dirs
        
        return result
    
    def _calculate_overall_status(self) -> None:
        """计算总体验证状态"""
        error_count = 0
        warning_count = 0
        success_count = 0
        
        for validation in self.validation_results["validations"].values():
            status = validation.get("status", "unknown")
            if status == "error":
                error_count += 1
            elif status == "warning":
                warning_count += 1
            elif status == "success":
                success_count += 1
        
        if error_count > 0:
            self.validation_results["overall_status"] = "failed"
        elif warning_count > 0:
            self.validation_results["overall_status"] = "warning"
        else:
            self.validation_results["overall_status"] = "success"
        
        self.validation_results["summary"] = {
            "total_validations": len(self.validation_results["validations"]),
            "errors": error_count,
            "warnings": warning_count,
            "successes": success_count
        }


async def validate_configuration() -> Dict[str, Any]:
    """验证配置的便捷函数"""
    try:
        settings = config_manager.settings
        validator = ConfigValidator(settings)
        return await validator.validate_all()
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        return {
            "overall_status": "failed",
            "error": str(e),
            "validations": {},
            "errors": [str(e)],
            "warnings": []
        }


def generate_config_report() -> str:
    """生成配置报告"""
    try:
        summary = config_manager.get_config_summary()
        
        report = f"""
# 配置状态报告

## 基本信息
- 应用名称: {summary.get('app_name', 'Unknown')}
- 应用版本: {summary.get('app_version', 'Unknown')}
- 配置状态: {summary.get('status', 'Unknown')}
- 调试模式: {summary.get('debug', False)}

## 数据库配置
- 主机: {summary.get('database', {}).get('host', 'Unknown')}
- 端口: {summary.get('database', {}).get('port', 'Unknown')}
- 数据库: {summary.get('database', {}).get('database', 'Unknown')}
- 连接池大小: {summary.get('database', {}).get('pool_size', 'Unknown')}

## AI模型配置
- OpenAI: {'已配置' if summary.get('ai_models', {}).get('openai_enabled') else '未配置'}
- Anthropic: {'已配置' if summary.get('ai_models', {}).get('anthropic_enabled') else '未配置'}
- Google: {'已配置' if summary.get('ai_models', {}).get('google_enabled') else '未配置'}
- 最大并发: {summary.get('ai_models', {}).get('max_concurrent', 'Unknown')}

## QMT配置
- 主机: {summary.get('qmt', {}).get('host', 'Unknown')}
- 端口: {summary.get('qmt', {}).get('port', 'Unknown')}
- 账户配置: {'已配置' if summary.get('qmt', {}).get('account_configured') else '未配置'}

## 交易配置
- 总资金: {summary.get('trading', {}).get('total_capital', 'Unknown')}
- AI交易员数量: {summary.get('trading', {}).get('ai_trader_count', 'Unknown')}
- 最大可用比例: {summary.get('trading', {}).get('max_usable_ratio', 'Unknown')}

## Redis配置
- 主机: {summary.get('redis', {}).get('host', 'Unknown')}
- 端口: {summary.get('redis', {}).get('port', 'Unknown')}
- 数据库: {summary.get('redis', {}).get('database', 'Unknown')}
"""
        return report
        
    except Exception as e:
        return f"生成配置报告失败: {str(e)}"
