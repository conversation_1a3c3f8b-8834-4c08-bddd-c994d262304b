# 海天AI纳斯达克交易系统 - Git忽略文件配置
# 基于Python 3.13.2 + FastAPI 0.116.1 + Vue.js 3.5.x + TypeScript技术栈

# ===== Python相关 =====
# 字节码文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre
.pyre/

# ===== Node.js/前端相关 =====
# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# ===== Vue.js特定 =====
# Vue.js构建输出
dist/
dist-ssr/
*.local

# Vite
.vite/

# ===== 数据库相关 =====
# SQLite
*.db
*.sqlite
*.sqlite3

# PostgreSQL
*.dump

# ===== 环境配置和密钥 =====
# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# 配置文件
config.ini
config.yaml
config.json
secrets.json

# SSL证书
*.pem
*.key
*.crt
*.csr

# ===== AI模型和数据 =====
# AI模型文件
*.pkl
*.joblib
*.h5
*.pb
*.onnx
models/
checkpoints/

# 数据文件
*.csv
*.json
*.parquet
*.feather
data/
datasets/

# ===== 日志文件 =====
# 应用日志
logs/
*.log
log/

# ===== 临时文件 =====
# 系统临时文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 编辑器临时文件
*~
*.swp
*.swo
*tmp*

# ===== IDE和编辑器 =====
# VSCode
.vscode/settings.json
.vscode/tasks.json
.vscode/launch.json
.vscode/extensions.json
.vscode/*.code-snippets

# PyCharm
.idea/
*.iws
*.iml
*.ipr

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== 容器化相关 =====
# Docker
.dockerignore

# ===== 测试相关 =====
# 测试输出
test-results/
test-reports/
.coverage
htmlcov/

# ===== 项目特定 =====
# AI交易员个人档案（敏感数据）
ai_profiles/*/personal_config.json
ai_profiles/*/trading_secrets.json

# QMT接口配置
qmt_config.json
trading_credentials.json

# 性能测试数据
performance_data/
benchmark_results/

# 部署相关
deployment/secrets/
deployment/production/

# 备份文件
*.bak
*.backup
backup/

# 临时脚本（以tmp_开头的文件）
tmp_*
temp_*

# 测试文件（非标准测试目录中的）
test_*
*_test_*

# 文档生成临时文件
docs/_build/
docs/.doctrees/
