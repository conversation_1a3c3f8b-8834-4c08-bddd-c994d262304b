#!/usr/bin/env python3
"""
配置文件验证脚本
验证所有代码质量工具的配置文件语法是否正确
"""

import json
import yaml
import toml
import os
from pathlib import Path
from typing import Dict, List, Tuple


class ConfigValidator:
    """配置文件验证器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.validation_results: Dict[str, Dict] = {}
    
    def validate_yaml_file(self, file_path: Path) -> Tuple[bool, str]:
        """验证YAML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                yaml.safe_load(f)
            return True, "YAML语法正确"
        except yaml.YAMLError as e:
            return False, f"YAML语法错误: {e}"
        except Exception as e:
            return False, f"文件读取错误: {e}"
    
    def validate_json_file(self, file_path: Path) -> <PERSON><PERSON>[bool, str]:
        """验证JSON文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json.load(f)
            return True, "JSON语法正确"
        except json.JSONDecodeError as e:
            return False, f"JSON语法错误: {e}"
        except Exception as e:
            return False, f"文件读取错误: {e}"
    
    def validate_toml_file(self, file_path: Path) -> Tuple[bool, str]:
        """验证TOML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                toml.load(f)
            return True, "TOML语法正确"
        except toml.TomlDecodeError as e:
            return False, f"TOML语法错误: {e}"
        except Exception as e:
            return False, f"文件读取错误: {e}"
    
    def validate_js_file(self, file_path: Path) -> Tuple[bool, str]:
        """验证JavaScript文件（基本语法检查）"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 基本的语法检查
            if 'module.exports' in content and content.count('{') == content.count('}'):
                return True, "JavaScript语法基本正确"
            else:
                return False, "JavaScript语法可能有问题"
        except Exception as e:
            return False, f"文件读取错误: {e}"
    
    def check_file_exists(self, file_path: Path) -> Tuple[bool, str]:
        """检查文件是否存在"""
        if file_path.exists():
            return True, "文件存在"
        else:
            return False, "文件不存在"
    
    def validate_all_configs(self):
        """验证所有配置文件"""
        print("🔍 验证代码质量工具配置文件...")
        
        # 配置文件列表
        config_files = [
            # 根目录配置文件
            (".pre-commit-config.yaml", "yaml", "Pre-commit钩子配置"),
            (".flake8", "ini", "Flake8配置"),
            ("bandit.yaml", "yaml", "Bandit安全扫描配置"),
            
            # 后端配置文件
            ("backend/pyproject.toml", "toml", "Python项目配置"),
            ("backend/requirements.txt", "text", "Python依赖"),
            
            # 前端配置文件
            ("frontend/package.json", "json", "Node.js项目配置"),
            ("frontend/.eslintrc.js", "js", "ESLint配置"),
            ("frontend/.prettierrc", "json", "Prettier配置"),
            ("frontend/.stylelintrc.js", "js", "Stylelint配置"),
            ("frontend/tsconfig.json", "json", "TypeScript配置"),
            ("frontend/vite.config.ts", "text", "Vite配置"),
        ]
        
        for file_path, file_type, description in config_files:
            full_path = self.project_root / file_path
            
            print(f"  📄 检查 {description} ({file_path})...")
            
            # 检查文件是否存在
            exists, exists_msg = self.check_file_exists(full_path)
            if not exists:
                self.validation_results[file_path] = {
                    "status": "不存在",
                    "message": exists_msg,
                    "description": description
                }
                continue
            
            # 根据文件类型验证语法
            if file_type == "yaml":
                valid, message = self.validate_yaml_file(full_path)
            elif file_type == "json":
                valid, message = self.validate_json_file(full_path)
            elif file_type == "toml":
                valid, message = self.validate_toml_file(full_path)
            elif file_type == "js":
                valid, message = self.validate_js_file(full_path)
            else:  # text files
                valid, message = True, "文本文件，跳过语法检查"
            
            self.validation_results[file_path] = {
                "status": "有效" if valid else "无效",
                "message": message,
                "description": description
            }
    
    def check_config_content(self):
        """检查配置文件内容的关键配置项"""
        print("\n🔧 检查配置文件关键内容...")
        
        # 检查pyproject.toml中的工具配置
        pyproject_path = self.project_root / "backend/pyproject.toml"
        if pyproject_path.exists():
            try:
                with open(pyproject_path, 'r', encoding='utf-8') as f:
                    pyproject_data = toml.load(f)
                
                tools_found = []
                if 'tool' in pyproject_data:
                    for tool in ['black', 'isort', 'mypy', 'bandit']:
                        if tool in pyproject_data['tool']:
                            tools_found.append(tool)
                
                print(f"  ✅ pyproject.toml中配置的工具: {', '.join(tools_found)}")
                
            except Exception as e:
                print(f"  ❌ 读取pyproject.toml失败: {e}")
        
        # 检查package.json中的脚本和依赖
        package_json_path = self.project_root / "frontend/package.json"
        if package_json_path.exists():
            try:
                with open(package_json_path, 'r', encoding='utf-8') as f:
                    package_data = json.load(f)
                
                scripts = package_data.get('scripts', {})
                dev_deps = package_data.get('devDependencies', {})
                
                quality_scripts = [s for s in scripts.keys() if 'lint' in s or 'format' in s]
                quality_deps = [d for d in dev_deps.keys() if any(tool in d for tool in ['eslint', 'prettier', 'stylelint'])]
                
                print(f"  ✅ package.json中的质量脚本: {', '.join(quality_scripts)}")
                print(f"  ✅ package.json中的质量工具依赖: {', '.join(quality_deps)}")
                
            except Exception as e:
                print(f"  ❌ 读取package.json失败: {e}")
    
    def print_results(self):
        """打印验证结果"""
        print("\n" + "="*60)
        print("📊 配置文件验证结果")
        print("="*60)
        
        valid_count = 0
        invalid_count = 0
        missing_count = 0
        
        for file_path, result in self.validation_results.items():
            status = result["status"]
            description = result["description"]
            message = result["message"]
            
            if status == "有效":
                icon = "✅"
                valid_count += 1
            elif status == "无效":
                icon = "❌"
                invalid_count += 1
            else:  # 不存在
                icon = "⚠️"
                missing_count += 1
            
            print(f"\n{icon} {description}")
            print(f"   文件: {file_path}")
            print(f"   状态: {status}")
            if status != "有效":
                print(f"   信息: {message}")
        
        # 统计
        total = len(self.validation_results)
        print(f"\n📈 验证统计:")
        print(f"   总计: {total}")
        print(f"   有效: {valid_count}")
        print(f"   无效: {invalid_count}")
        print(f"   缺失: {missing_count}")
        
        if invalid_count == 0 and missing_count == 0:
            print(f"\n🎉 所有配置文件都已正确创建和配置！")
        elif invalid_count > 0:
            print(f"\n⚠️ 有 {invalid_count} 个配置文件语法错误，需要修复。")
        elif missing_count > 0:
            print(f"\n⚠️ 有 {missing_count} 个配置文件缺失，需要创建。")


def main():
    """主函数"""
    print("🚀 开始验证代码质量工具配置文件...")
    
    validator = ConfigValidator()
    
    # 验证配置文件
    validator.validate_all_configs()
    
    # 检查配置内容
    validator.check_config_content()
    
    # 打印结果
    validator.print_results()
    
    print("\n📝 注意: 此脚本只验证配置文件语法，不检查工具是否已安装。")
    print("要测试工具功能，请先安装依赖，然后运行 test_code_quality_tools.py")


if __name__ == "__main__":
    main()
