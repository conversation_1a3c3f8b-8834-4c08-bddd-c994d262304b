<template>
  <div class="home">
    <el-card class="welcome-card">
      <h2>欢迎使用海天AI纳斯达克交易系统</h2>
      <p>系统状态: <el-tag :type="systemStatus.type">{{ systemStatus.text }}</el-tag></p>
      <el-button type="primary" @click="checkSystemHealth">检查系统健康状态</el-button>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const systemStatus = ref({
  type: 'info' as const,
  text: '检查中...'
})

const checkSystemHealth = async () => {
  try {
    const response = await fetch('/api/health')
    if (response.ok) {
      systemStatus.value = { type: 'success', text: '系统正常' }
      ElMessage.success('系统健康检查通过')
    } else {
      systemStatus.value = { type: 'danger', text: '系统异常' }
      ElMessage.error('系统健康检查失败')
    }
  } catch (error) {
    systemStatus.value = { type: 'warning', text: '连接失败' }
    ElMessage.warning('无法连接到后端服务')
  }
}

onMounted(() => {
  checkSystemHealth()
})
</script>

<style scoped>
.home {
  padding: 20px;
}

.welcome-card {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.welcome-card h2 {
  color: #409eff;
  margin-bottom: 20px;
}
</style>
