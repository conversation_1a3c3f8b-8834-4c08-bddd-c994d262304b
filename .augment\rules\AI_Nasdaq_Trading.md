---
type: "always_apply"
---

# Always respond in Chinese-simplified
# 严格遵循奥卡姆剃刀原则和MVP原则
# 代码编写和目录结构必须遵守项目规范，代码必须采用通用组件和模板体系。如果不清楚，请查阅文档或者实际代码结构，或者直接询问我
# 保持独立思考，不要盲目附和，勇敢说"不"
# 在进行网络搜索时，只使用英文进行搜索
# 查错时如果有使用方面问题需使用context7工具搜索学习相应技术栈版本官方文档
# 随时保持系统干净，不再使用的代码、文件、注释要及时删除
# 前后端所有测试代码文件名必须包含test_前缀，并放入前后端各自的tests目录下。如果是临时或是不再使用的测试代码，用完后必须立刻删除
# 生成的所有临时代码文件名必须包含tmp_前缀，用完后必须立刻删除
# 需将工作分解成多个task, 然后依次执行task来完成工作。
# 你打开的终端窗口都需要切换到conda AI_Nasdaq_Trading环境下再执行命令
# 代码重构，对齐和改错等大型任务不要偷懒只做重要任务，不准少执行任务，不要试图绕过错误投机取巧，必须检查出问题根本原因并彻底消除。另外对大量修改不要编写脚本批量修改，只能采用手工修改的方式。没有时间限制，可以接受效率低，但是准确性必须要保证。
# 这是全新系统开发，所有结构更改不用考虑向后兼容，也不用考虑旧数据迁移，直接按照最优原则设计
# 所有工作的最后你都要以质量审计专家的身份对所作工作的完整性和一致性进行复核检查总结，确保全面完成
# 你在为我服务时只使用Claude Sonnet 4 模型