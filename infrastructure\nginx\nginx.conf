# =============================================================================
# 海天AI纳斯达克交易系统 - Nginx主配置文件
# 基于: 项目手册4.1节MVP版本技术栈配置
# 创建日期: 2025年7月13日
# 用途: 全局Nginx配置，性能优化和安全设置
# =============================================================================

# 运行用户
user nginx;

# 工作进程数（自动检测CPU核心数）
worker_processes auto;

# 错误日志
error_log /var/log/nginx/error.log warn;

# PID文件
pid /var/run/nginx.pid;

# 工作连接数配置
events {
    # 每个工作进程的最大连接数
    worker_connections 1024;
    
    # 使用epoll事件模型（Linux优化）
    use epoll;
    
    # 允许一个工作进程同时接受多个连接
    multi_accept on;
}

# HTTP配置块
http {
    # =============================================================================
    # 基础配置
    # =============================================================================
    
    # MIME类型
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 字符集
    charset utf-8;
    
    # 服务器标识隐藏（安全）
    server_tokens off;
    
    # =============================================================================
    # 性能优化配置
    # =============================================================================
    
    # 高效文件传输
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    
    # 连接超时设置
    keepalive_timeout 65;
    keepalive_requests 100;
    
    # 客户端请求限制
    client_max_body_size 100M;
    client_body_timeout 60;
    client_header_timeout 60;
    send_timeout 60;
    
    # 缓冲区设置
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    
    # =============================================================================
    # Gzip压缩配置
    # =============================================================================
    
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/xml
        image/svg+xml;
    
    # =============================================================================
    # 安全配置
    # =============================================================================
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # =============================================================================
    # 日志配置
    # =============================================================================
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    # 访问日志
    access_log /var/log/nginx/access.log main;
    
    # =============================================================================
    # 上游服务器配置
    # =============================================================================
    
    # 后端API服务器池
    upstream backend_api {
        server backend:8001 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # 前端服务器池
    upstream frontend_app {
        server frontend:80 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }
    
    # =============================================================================
    # 限流配置
    # =============================================================================
    
    # 限制请求频率
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    
    # 限制连接数
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    
    # =============================================================================
    # 包含其他配置文件
    # =============================================================================
    
    # 包含站点配置
    include /etc/nginx/conf.d/*.conf;
}
