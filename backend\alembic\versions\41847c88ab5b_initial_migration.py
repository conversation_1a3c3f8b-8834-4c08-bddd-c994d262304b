"""Initial migration

Revision ID: 41847c88ab5b
Revises: 
Create Date: 2025-07-30 11:34:09.781168

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '41847c88ab5b'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('market_data',
    sa.Column('symbol', sa.String(length=20), nullable=False, comment='159509代码（系统强制限制）'),
    sa.Column('data_type', sa.String(length=20), nullable=False, comment='数据类型(TICK/1MIN/5MIN/DAILY)'),
    sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False, comment='时间戳'),
    sa.Column('open_price', sa.DECIMAL(precision=10, scale=4), nullable=True, comment='159509开盘价'),
    sa.Column('high_price', sa.DECIMAL(precision=10, scale=4), nullable=True, comment='159509最高价'),
    sa.Column('low_price', sa.DECIMAL(precision=10, scale=4), nullable=True, comment='159509最低价'),
    sa.Column('close_price', sa.DECIMAL(precision=10, scale=4), nullable=False, comment='159509收盘价'),
    sa.Column('volume', sa.BigInteger(), nullable=False, comment='成交量'),
    sa.Column('amount', sa.DECIMAL(precision=18, scale=2), nullable=True, comment='成交额'),
    sa.Column('id', sa.UUID(), nullable=False, comment='唯一标识符'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.CheckConstraint("symbol = '159509'", name='chk_market_symbol_159509_only'),
    sa.PrimaryKeyConstraint('id'),
    comment='市场数据表（159509专用）'
    )
    op.create_table('technical_indicators',
    sa.Column('symbol', sa.String(length=20), nullable=False, comment='159509代码（系统强制限制）'),
    sa.Column('indicator_type', sa.String(length=50), nullable=False, comment='ETF适用指标类型(RSI/MACD/MA/BOLL等)'),
    sa.Column('timeframe', sa.String(length=20), nullable=False, comment='时间框架(1MIN/5MIN/15MIN/DAILY)'),
    sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False, comment='时间戳'),
    sa.Column('indicator_values', postgresql.JSONB(astext_type=sa.Text()), nullable=False, comment='159509指标值'),
    sa.Column('signal_strength', sa.DECIMAL(precision=3, scale=2), nullable=True, comment='159509择时信号强度'),
    sa.Column('signal_direction', sa.String(length=10), nullable=True, comment='信号方向(BUY/SELL/HOLD)'),
    sa.Column('id', sa.UUID(), nullable=False, comment='唯一标识符'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.CheckConstraint("symbol = '159509'", name='chk_indicator_symbol_159509_only'),
    sa.PrimaryKeyConstraint('id'),
    comment='技术指标表（159509专用ETF指标）'
    )
    op.create_table('users',
    sa.Column('username', sa.String(length=50), nullable=False, comment='用户名'),
    sa.Column('email', sa.String(length=100), nullable=False, comment='邮箱地址'),
    sa.Column('password_hash', sa.String(length=255), nullable=False, comment='密码哈希'),
    sa.Column('full_name', sa.String(length=100), nullable=False, comment='用户全名'),
    sa.Column('role', sa.String(length=20), nullable=False, comment='用户角色(ADMIN/USER/VIEWER)'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('id', sa.UUID(), nullable=False, comment='唯一标识符'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('username'),
    comment='用户表 - 存储系统用户基本信息'
    )
    op.create_table('ai_traders',
    sa.Column('name', sa.String(length=100), nullable=False, comment='交易员名称'),
    sa.Column('display_name', sa.String(length=100), nullable=False, comment='显示名称'),
    sa.Column('model_type', sa.String(length=50), nullable=False, comment='AI模型类型(GPT-4/Claude-3/Gemini等)'),
    sa.Column('model_version', sa.String(length=20), nullable=False, comment='模型版本'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='当前状态(CREATED/ACTIVE/SUSPENDED/RETIRED)'),
    sa.Column('max_position_quantity', sa.Integer(), nullable=False, comment='最大持仓数量配额'),
    sa.Column('max_single_trade_quantity', sa.Integer(), nullable=False, comment='单笔最大交易数量'),
    sa.Column('daily_trade_limit', sa.Integer(), nullable=False, comment='日交易次数限制'),
    sa.Column('profit_threshold_pct', sa.DECIMAL(precision=5, scale=2), nullable=False, comment='止盈阈值百分比'),
    sa.Column('loss_threshold_pct', sa.DECIMAL(precision=5, scale=2), nullable=False, comment='止损阈值百分比'),
    sa.Column('model_temperature', sa.DECIMAL(precision=3, scale=2), nullable=False, comment='AI模型温度参数'),
    sa.Column('risk_preference', sa.DECIMAL(precision=3, scale=2), nullable=False, comment='风险偏好系数(0-1)'),
    sa.Column('trading_style', sa.String(length=50), nullable=True, comment='交易风格描述'),
    sa.Column('is_config_active', sa.Boolean(), nullable=False, comment='配置是否激活'),
    sa.Column('created_by', sa.UUID(), nullable=False, comment='创建者ID'),
    sa.Column('id', sa.UUID(), nullable=False, comment='唯一标识符'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name'),
    comment='AI交易员信息和配置表'
    )
    op.create_table('audit_logs',
    sa.Column('action_type', sa.String(length=50), nullable=False, comment='操作类型'),
    sa.Column('table_name', sa.String(length=50), nullable=False, comment='表名'),
    sa.Column('record_id', sa.UUID(), nullable=False, comment='记录ID'),
    sa.Column('old_values', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='旧值'),
    sa.Column('new_values', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='新值'),
    sa.Column('user_id', sa.UUID(), nullable=False, comment='操作用户ID'),
    sa.Column('ip_address', sa.String(length=45), nullable=True, comment='IP地址'),
    sa.Column('user_agent', sa.Text(), nullable=True, comment='用户代理'),
    sa.Column('id', sa.UUID(), nullable=False, comment='唯一标识符'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    comment='审计日志表'
    )
    op.create_table('ai_trader_profiles',
    sa.Column('trader_id', sa.UUID(), nullable=False, comment='交易员ID'),
    sa.Column('profile_type', sa.String(length=50), nullable=False, comment='档案类型(TRADING_RULES/LEARNING_NOTES)'),
    sa.Column('profile_data', postgresql.JSONB(astext_type=sa.Text()), nullable=False, comment='档案数据'),
    sa.Column('version', sa.Integer(), nullable=False, comment='档案版本'),
    sa.Column('is_current', sa.Boolean(), nullable=False, comment='是否为当前版本'),
    sa.Column('id', sa.UUID(), nullable=False, comment='唯一标识符'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['trader_id'], ['ai_traders.id'], ),
    sa.PrimaryKeyConstraint('id'),
    comment='AI交易员档案表'
    )
    op.create_table('ai_trader_states',
    sa.Column('trader_id', sa.UUID(), nullable=False, comment='交易员ID'),
    sa.Column('work_status', sa.String(length=20), nullable=False, comment='工作状态(IDLE/ANALYZING/TRADING/LEARNING/ERROR)'),
    sa.Column('health_status', sa.String(length=20), nullable=False, comment='健康状态(HEALTHY/WARNING/ERROR/OFFLINE)'),
    sa.Column('current_positions', sa.Integer(), nullable=False, comment='当前持仓数量'),
    sa.Column('daily_trades_count', sa.Integer(), nullable=False, comment='当日交易次数'),
    sa.Column('daily_pnl', sa.DECIMAL(precision=12, scale=4), nullable=False, comment='当日盈亏'),
    sa.Column('performance_level', sa.String(length=10), nullable=False, comment='绩效等级(S/A/B/C/D)'),
    sa.Column('total_trades', sa.Integer(), nullable=False, comment='累计交易次数'),
    sa.Column('win_rate', sa.DECIMAL(precision=5, scale=2), nullable=False, comment='胜率百分比'),
    sa.Column('total_return', sa.DECIMAL(precision=10, scale=4), nullable=False, comment='累计收益率'),
    sa.Column('last_decision_at', sa.DateTime(timezone=True), nullable=True, comment='最后决策时间'),
    sa.Column('error_count', sa.Integer(), nullable=False, comment='错误计数'),
    sa.Column('id', sa.UUID(), nullable=False, comment='唯一标识符'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['trader_id'], ['ai_traders.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('trader_id'),
    comment='AI交易员状态表'
    )
    op.create_table('performance_metrics',
    sa.Column('trader_id', sa.UUID(), nullable=False, comment='交易员ID'),
    sa.Column('metric_date', sa.Date(), nullable=False, comment='指标日期'),
    sa.Column('total_return', sa.DECIMAL(precision=10, scale=4), nullable=False, comment='总收益率'),
    sa.Column('win_rate', sa.DECIMAL(precision=5, scale=2), nullable=False, comment='胜率'),
    sa.Column('max_drawdown', sa.DECIMAL(precision=10, scale=4), nullable=False, comment='最大回撤'),
    sa.Column('sharpe_ratio', sa.DECIMAL(precision=6, scale=3), nullable=False, comment='夏普比率'),
    sa.Column('total_trades', sa.Integer(), nullable=False, comment='总交易次数'),
    sa.Column('profit_trades', sa.Integer(), nullable=False, comment='盈利交易次数'),
    sa.Column('loss_trades', sa.Integer(), nullable=False, comment='亏损交易次数'),
    sa.Column('id', sa.UUID(), nullable=False, comment='唯一标识符'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['trader_id'], ['ai_traders.id'], ),
    sa.PrimaryKeyConstraint('id'),
    comment='绩效指标表'
    )
    op.create_table('position_history',
    sa.Column('trader_id', sa.UUID(), nullable=False, comment='交易员ID'),
    sa.Column('symbol', sa.String(length=20), nullable=False, comment='159509代码（系统强制限制）'),
    sa.Column('action_type', sa.String(length=20), nullable=False, comment='操作类型(BUY/SELL)'),
    sa.Column('quantity', sa.Integer(), nullable=False, comment='159509数量变化'),
    sa.Column('price', sa.DECIMAL(precision=10, scale=4), nullable=False, comment='159509成交价格'),
    sa.Column('amount', sa.DECIMAL(precision=15, scale=2), nullable=False, comment='159509成交金额'),
    sa.Column('position_before', sa.Integer(), nullable=False, comment='159509操作前持仓'),
    sa.Column('position_after', sa.Integer(), nullable=False, comment='操作后持仓'),
    sa.Column('realized_pnl', sa.DECIMAL(precision=12, scale=4), nullable=False, comment='本次实现盈亏'),
    sa.Column('trade_date', sa.Date(), nullable=False, comment='交易日期'),
    sa.Column('id', sa.UUID(), nullable=False, comment='唯一标识符'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.CheckConstraint("action_type IN ('BUY', 'SELL')", name='chk_action_type_valid'),
    sa.CheckConstraint("symbol = '159509'", name='chk_history_symbol_159509_only'),
    sa.ForeignKeyConstraint(['trader_id'], ['ai_traders.id'], ),
    sa.PrimaryKeyConstraint('id'),
    comment='持仓历史表'
    )
    op.create_table('positions',
    sa.Column('trader_id', sa.UUID(), nullable=False, comment='交易员ID'),
    sa.Column('symbol', sa.String(length=20), nullable=False, comment='159509代码（系统强制限制）'),
    sa.Column('quantity', sa.Integer(), nullable=False, comment='159509持仓数量'),
    sa.Column('available_quantity', sa.Integer(), nullable=False, comment='159509可卖数量'),
    sa.Column('avg_cost', sa.DECIMAL(precision=10, scale=4), nullable=False, comment='159509平均成本价'),
    sa.Column('market_value', sa.DECIMAL(precision=15, scale=2), nullable=False, comment='159509市值'),
    sa.Column('unrealized_pnl', sa.DECIMAL(precision=12, scale=4), nullable=False, comment='159509浮动盈亏'),
    sa.Column('first_buy_at', sa.DateTime(timezone=True), nullable=False, comment='首次买入时间'),
    sa.Column('last_trade_at', sa.DateTime(timezone=True), nullable=False, comment='最后交易时间'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否活跃持仓'),
    sa.Column('id', sa.UUID(), nullable=False, comment='唯一标识符'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.CheckConstraint("symbol = '159509'", name='chk_position_symbol_159509_only'),
    sa.ForeignKeyConstraint(['trader_id'], ['ai_traders.id'], ),
    sa.PrimaryKeyConstraint('id'),
    comment='持仓表'
    )
    op.create_table('risk_events',
    sa.Column('event_type', sa.String(length=50), nullable=False, comment='事件类型(POSITION_LIMIT/LOSS_LIMIT/FREQUENCY_LIMIT)'),
    sa.Column('risk_level', sa.String(length=20), nullable=False, comment='风险级别(LOW/MEDIUM/HIGH/CRITICAL)'),
    sa.Column('trader_id', sa.UUID(), nullable=False, comment='相关交易员ID'),
    sa.Column('symbol', sa.String(length=20), nullable=True, comment='159509代码（系统强制限制）'),
    sa.Column('event_description', sa.Text(), nullable=False, comment='159509风险事件描述'),
    sa.Column('trigger_conditions', postgresql.JSONB(astext_type=sa.Text()), nullable=False, comment='159509风险触发条件'),
    sa.Column('current_values', postgresql.JSONB(astext_type=sa.Text()), nullable=False, comment='159509当前值'),
    sa.Column('threshold_values', postgresql.JSONB(astext_type=sa.Text()), nullable=False, comment='159509风险阈值'),
    sa.Column('resolution_status', sa.String(length=20), nullable=False, comment='解决状态'),
    sa.Column('resolution_time', sa.DateTime(timezone=True), nullable=True, comment='解决时间'),
    sa.Column('id', sa.UUID(), nullable=False, comment='唯一标识符'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.CheckConstraint("symbol = '159509' OR symbol IS NULL", name='chk_risk_symbol_159509_only'),
    sa.ForeignKeyConstraint(['trader_id'], ['ai_traders.id'], ),
    sa.PrimaryKeyConstraint('id'),
    comment='风险事件表（159509专用风险控制）'
    )
    op.create_table('system_logs',
    sa.Column('log_level', sa.String(length=20), nullable=False, comment='日志级别(DEBUG/INFO/WARNING/ERROR/CRITICAL)'),
    sa.Column('module', sa.String(length=50), nullable=False, comment='模块名称'),
    sa.Column('message', sa.Text(), nullable=False, comment='日志消息'),
    sa.Column('details', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='详细信息'),
    sa.Column('user_id', sa.UUID(), nullable=True, comment='相关用户ID'),
    sa.Column('trader_id', sa.UUID(), nullable=True, comment='相关交易员ID'),
    sa.Column('id', sa.UUID(), nullable=False, comment='唯一标识符'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.ForeignKeyConstraint(['trader_id'], ['ai_traders.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    comment='系统日志表'
    )
    op.create_table('trading_orders',
    sa.Column('trader_id', sa.UUID(), nullable=False, comment='交易员ID'),
    sa.Column('symbol', sa.String(length=20), nullable=False, comment='159509代码（系统强制限制）'),
    sa.Column('order_type', sa.String(length=20), nullable=False, comment='订单类型(MARKET/LIMIT/STOP)'),
    sa.Column('side', sa.String(length=10), nullable=False, comment='买卖方向(BUY/SELL)'),
    sa.Column('quantity', sa.Integer(), nullable=False, comment='159509委托数量'),
    sa.Column('price', sa.DECIMAL(precision=10, scale=4), nullable=True, comment='159509委托价格'),
    sa.Column('filled_quantity', sa.Integer(), nullable=False, comment='159509成交数量'),
    sa.Column('avg_fill_price', sa.DECIMAL(precision=10, scale=4), nullable=True, comment='平均成交价'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='订单状态(PENDING/FILLED/CANCELLED/REJECTED)'),
    sa.Column('decision_confidence', sa.DECIMAL(precision=3, scale=2), nullable=True, comment='决策置信度'),
    sa.Column('decision_reason', sa.Text(), nullable=True, comment='决策依据'),
    sa.Column('ai_analysis_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='AI分析数据'),
    sa.Column('position_check_passed', sa.Boolean(), nullable=False, comment='仓位验证是否通过'),
    sa.Column('risk_check_passed', sa.Boolean(), nullable=False, comment='风险检查是否通过'),
    sa.Column('qmt_order_id', sa.String(length=50), nullable=True, comment='QMT订单ID'),
    sa.Column('submit_time', sa.DateTime(timezone=True), nullable=True, comment='提交时间'),
    sa.Column('fill_time', sa.DateTime(timezone=True), nullable=True, comment='成交时间'),
    sa.Column('cancel_time', sa.DateTime(timezone=True), nullable=True, comment='撤销时间'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('id', sa.UUID(), nullable=False, comment='唯一标识符'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.CheckConstraint("side IN ('BUY', 'SELL')", name='chk_side_valid'),
    sa.CheckConstraint("symbol = '159509'", name='chk_symbol_159509_only'),
    sa.CheckConstraint('price IS NULL OR price > 0', name='chk_price_positive'),
    sa.CheckConstraint('quantity > 0', name='chk_quantity_positive'),
    sa.ForeignKeyConstraint(['trader_id'], ['ai_traders.id'], ),
    sa.PrimaryKeyConstraint('id'),
    comment='交易订单表'
    )
    op.create_table('trading_executions',
    sa.Column('order_id', sa.UUID(), nullable=False, comment='订单ID'),
    sa.Column('trader_id', sa.UUID(), nullable=False, comment='交易员ID'),
    sa.Column('symbol', sa.String(length=20), nullable=False, comment='159509代码（系统强制限制）'),
    sa.Column('side', sa.String(length=10), nullable=False, comment='买卖方向'),
    sa.Column('quantity', sa.Integer(), nullable=False, comment='159509成交数量'),
    sa.Column('price', sa.DECIMAL(precision=10, scale=4), nullable=False, comment='159509成交价格'),
    sa.Column('amount', sa.DECIMAL(precision=15, scale=2), nullable=False, comment='159509成交金额'),
    sa.Column('commission', sa.DECIMAL(precision=8, scale=4), nullable=False, comment='佣金费用'),
    sa.Column('net_amount', sa.DECIMAL(precision=15, scale=2), nullable=False, comment='净成交金额'),
    sa.Column('execution_time', sa.DateTime(timezone=True), nullable=False, comment='执行时间'),
    sa.Column('id', sa.UUID(), nullable=False, comment='唯一标识符'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='更新时间'),
    sa.CheckConstraint("symbol = '159509'", name='chk_execution_symbol_159509_only'),
    sa.CheckConstraint('price > 0', name='chk_execution_price_positive'),
    sa.CheckConstraint('quantity > 0', name='chk_execution_quantity_positive'),
    sa.ForeignKeyConstraint(['order_id'], ['trading_orders.id'], ),
    sa.ForeignKeyConstraint(['trader_id'], ['ai_traders.id'], ),
    sa.PrimaryKeyConstraint('id'),
    comment='交易执行表'
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('trading_executions')
    op.drop_table('trading_orders')
    op.drop_table('system_logs')
    op.drop_table('risk_events')
    op.drop_table('positions')
    op.drop_table('position_history')
    op.drop_table('performance_metrics')
    op.drop_table('ai_trader_states')
    op.drop_table('ai_trader_profiles')
    op.drop_table('audit_logs')
    op.drop_table('ai_traders')
    op.drop_table('users')
    op.drop_table('technical_indicators')
    op.drop_table('market_data')
    # ### end Alembic commands ###
