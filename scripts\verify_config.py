#!/usr/bin/env python3
"""
依赖管理配置验证脚本
验证 pyproject.toml 和 package.json 的配置正确性
"""

import json
import tomllib
import sys
from pathlib import Path

def verify_python_config():
    """验证Python配置文件"""
    print("🐍 验证Python配置 (pyproject.toml)...")
    
    backend_dir = Path("backend")
    pyproject_file = backend_dir / "pyproject.toml"
    
    if not pyproject_file.exists():
        print("❌ pyproject.toml 文件不存在")
        return False
    
    try:
        with open(pyproject_file, 'rb') as f:
            data = tomllib.load(f)
        
        # 验证基本结构
        required_sections = ['build-system', 'tool.poetry', 'tool.poetry.dependencies']
        for section in required_sections:
            keys = section.split('.')
            current = data
            for key in keys:
                if key not in current:
                    print(f"❌ 缺少必需的配置节: {section}")
                    return False
                current = current[key]
        
        # 验证关键依赖
        deps = data['tool']['poetry']['dependencies']
        critical_deps = {
            'python': '^3.13.2',
            'fastapi': '0.116.1'
        }
        
        print("  ✅ 配置文件结构正确")
        print("  📦 关键依赖检查:")
        
        for pkg, expected in critical_deps.items():
            actual = deps.get(pkg, 'N/A')
            if isinstance(actual, dict):
                actual = actual.get('version', 'N/A')
            
            status = '✅' if actual == expected else '⚠️'
            print(f"    {status} {pkg}: 期望 {expected}, 实际 {actual}")
        
        # 统计依赖数量
        prod_deps = len([k for k, v in deps.items() if k != 'python'])
        dev_deps = len(data.get('tool', {}).get('poetry', {}).get('group', {}).get('dev', {}).get('dependencies', {}))
        
        print(f"  📊 生产依赖: {prod_deps} 个")
        print(f"  📊 开发依赖: {dev_deps} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ pyproject.toml 验证失败: {e}")
        return False

def verify_frontend_config():
    """验证前端配置文件"""
    print("\n🌐 验证前端配置 (package.json)...")
    
    frontend_dir = Path("frontend")
    package_file = frontend_dir / "package.json"
    
    if not package_file.exists():
        print("❌ package.json 文件不存在")
        return False
    
    try:
        with open(package_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 验证基本结构
        required_fields = ['name', 'version', 'dependencies', 'devDependencies', 'scripts']
        for field in required_fields:
            if field not in data:
                print(f"❌ 缺少必需的字段: {field}")
                return False
        
        # 验证关键依赖
        deps = data['dependencies']
        critical_deps = {
            'vue': '^3.5.13',
            'vue-router': '^4.5.0',
            'element-plus': '^2.9.1'
        }
        
        print("  ✅ 配置文件结构正确")
        print("  📦 关键依赖检查:")
        
        for pkg, expected in critical_deps.items():
            actual = deps.get(pkg, 'N/A')
            status = '✅' if actual == expected else '⚠️'
            print(f"    {status} {pkg}: 期望 {expected}, 实际 {actual}")
        
        # 验证脚本配置
        scripts = data['scripts']
        required_scripts = ['dev', 'build', 'lint', 'test']
        
        print("  🔧 脚本配置检查:")
        for script in required_scripts:
            status = '✅' if script in scripts else '❌'
            print(f"    {status} {script}: {scripts.get(script, '未配置')}")
        
        # 统计依赖数量
        prod_deps = len(data['dependencies'])
        dev_deps = len(data['devDependencies'])
        
        print(f"  📊 生产依赖: {prod_deps} 个")
        print(f"  📊 开发依赖: {dev_deps} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ package.json 验证失败: {e}")
        return False

def verify_version_compatibility():
    """验证版本兼容性"""
    print("\n🔍 验证版本兼容性...")
    
    # 这里可以添加更复杂的版本兼容性检查
    # 例如检查Python和Node.js版本要求
    
    print("  ✅ 基本版本兼容性检查通过")
    return True

def main():
    """主验证函数"""
    print("🚀 开始依赖管理配置验证...\n")
    
    results = []
    
    # 验证Python配置
    results.append(verify_python_config())
    
    # 验证前端配置
    results.append(verify_frontend_config())
    
    # 验证版本兼容性
    results.append(verify_version_compatibility())
    
    # 输出总结
    print("\n" + "="*50)
    if all(results):
        print("✅ 所有配置验证通过!")
        print("🎉 依赖管理配置正确，可以开始安装依赖")
        return 0
    else:
        print("❌ 配置验证失败，请检查并修复问题")
        return 1

if __name__ == "__main__":
    sys.exit(main())
