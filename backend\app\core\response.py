"""
海天AI纳斯达克交易系统 - 统一响应格式
基于: 项目手册4.1节MVP版本技术栈配置
创建日期: 2025年7月31日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: 统一API响应格式，标准化成功和错误响应
"""

from typing import Any, Dict, Optional
from pydantic import BaseModel


class StandardResponse(BaseModel):
    """标准API响应格式"""
    success: bool
    data: Any = None
    message: Optional[str] = None
    error_code: Optional[str] = None


def success_response(
    data: Any = None, 
    message: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建成功响应
    
    Args:
        data: 响应数据
        message: 可选的消息
        
    Returns:
        Dict[str, Any]: 标准成功响应格式
    """
    response = {
        "success": True,
        "data": data
    }
    
    if message:
        response["message"] = message
        
    return response


def error_response(
    message: str,
    error_code: Optional[str] = None,
    data: Any = None
) -> Dict[str, Any]:
    """
    创建错误响应
    
    Args:
        message: 错误消息
        error_code: 错误代码
        data: 可选的错误数据
        
    Returns:
        Dict[str, Any]: 标准错误响应格式
    """
    response = {
        "success": False,
        "message": message
    }
    
    if error_code:
        response["error_code"] = error_code
        
    if data:
        response["data"] = data
        
    return response
