# 海天AI纳斯达克交易系统 - 代码规范和质量标准

## 文档信息

| 项目 | 内容 |
|------|------|
| **创建日期** | 2025年7月13日 |
| **维护团队** | 海天AI开发团队 |
| **文档范围** | 代码编写规范和质量标准 |
| **技术基础** | Python 3.13.2 + FastAPI 0.116.1 + Vue.js 3.5.x + TypeScript |

## 版本更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| v1.0 | 2025-07-13 | 初始版本，完整的代码规范和质量标准 | 海天AI开发团队 |

## 目录

- [1. 总体原则](#1-总体原则)
- [2. Python代码规范](#2-python代码规范)
- [3. TypeScript代码规范](#3-typescript代码规范)
- [4. CSS/SCSS代码规范](#4-cssscss代码规范)
- [5. 代码质量标准](#5-代码质量标准)
- [6. 安全编码规范](#6-安全编码规范)

## 1. 总体原则

### 1.1 核心理念

- **可读性优先**: 代码应该易于理解和维护
- **一致性**: 整个项目保持统一的编码风格
- **简洁性**: 避免不必要的复杂性
- **安全性**: 遵循安全编码最佳实践
- **性能**: 在保证可读性的前提下优化性能

### 1.2 通用规则

- 使用有意义的变量和函数名
- 保持函数和类的单一职责
- 添加必要的注释和文档
- 遵循DRY（Don't Repeat Yourself）原则
- 进行充分的错误处理

## 2. Python代码规范

### 2.1 命名规范

**变量和函数**: 使用snake_case
```python
# 正确
user_name = "john_doe"
def calculate_total_amount():
    pass

# 错误
userName = "john_doe"
def calculateTotalAmount():
    pass
```

**类名**: 使用PascalCase
```python
# 正确
class TradingEngine:
    pass

# 错误
class trading_engine:
    pass
```

**常量**: 使用UPPER_SNAKE_CASE
```python
# 正确
MAX_RETRY_COUNT = 3
API_BASE_URL = "https://api.example.com"

# 错误
max_retry_count = 3
apiBaseUrl = "https://api.example.com"
```

### 2.2 代码格式

**行长度**: 最大88字符（Black标准）

**导入顺序**:
```python
# 1. 标准库导入
import os
import sys
from datetime import datetime

# 2. 第三方库导入
import pandas as pd
from fastapi import FastAPI

# 3. 本地应用导入
from app.core.config import settings
from app.models.user import User
```

**函数定义**:
```python
def process_trading_data(
    symbol: str,
    start_date: datetime,
    end_date: datetime,
    *,
    include_volume: bool = True
) -> pd.DataFrame:
    """处理交易数据。
    
    Args:
        symbol: 股票代码
        start_date: 开始日期
        end_date: 结束日期
        include_volume: 是否包含成交量数据
        
    Returns:
        处理后的交易数据DataFrame
        
    Raises:
        ValueError: 当日期范围无效时
    """
    pass
```

### 2.3 类型注解

**必须使用类型注解**:
```python
from typing import List, Dict, Optional, Union

def get_user_portfolio(user_id: int) -> Dict[str, Union[str, float]]:
    """获取用户投资组合。"""
    pass

class Portfolio:
    def __init__(self, user_id: int, assets: List[str]) -> None:
        self.user_id = user_id
        self.assets = assets
    
    def calculate_value(self) -> float:
        """计算投资组合总价值。"""
        pass
```

### 2.4 错误处理

**使用具体的异常类型**:
```python
# 正确
try:
    result = api_call()
except requests.ConnectionError as e:
    logger.error(f"网络连接错误: {e}")
    raise
except requests.Timeout as e:
    logger.error(f"请求超时: {e}")
    raise

# 错误
try:
    result = api_call()
except Exception as e:
    pass
```

### 2.5 文档字符串

**使用Google风格的docstring**:
```python
def calculate_moving_average(prices: List[float], window: int) -> List[float]:
    """计算移动平均线。
    
    Args:
        prices: 价格列表
        window: 移动窗口大小
        
    Returns:
        移动平均线数据列表
        
    Raises:
        ValueError: 当窗口大小无效时
        
    Example:
        >>> prices = [1.0, 2.0, 3.0, 4.0, 5.0]
        >>> calculate_moving_average(prices, 3)
        [2.0, 3.0, 4.0]
    """
    pass
```

## 3. TypeScript代码规范

### 3.1 命名规范

**变量和函数**: 使用camelCase
```typescript
// 正确
const userName = 'john_doe'
function calculateTotalAmount(): number {
  return 0
}

// 错误
const user_name = 'john_doe'
function calculate_total_amount(): number {
  return 0
}
```

**类和接口**: 使用PascalCase
```typescript
// 正确
class TradingEngine {
  // ...
}

interface UserPortfolio {
  userId: number
  assets: string[]
}

// 错误
class tradingEngine {
  // ...
}

interface userPortfolio {
  userId: number
  assets: string[]
}
```

**常量**: 使用UPPER_SNAKE_CASE
```typescript
// 正确
const MAX_RETRY_COUNT = 3
const API_BASE_URL = 'https://api.example.com'

// 错误
const maxRetryCount = 3
const apiBaseUrl = 'https://api.example.com'
```

### 3.2 类型定义

**优先使用interface而非type**:
```typescript
// 正确
interface User {
  id: number
  name: string
  email: string
}

// 避免（除非需要联合类型等特殊情况）
type User = {
  id: number
  name: string
  email: string
}
```

**使用严格的类型检查**:
```typescript
// 正确
function processUserData(user: User): UserProfile {
  return {
    displayName: user.name,
    contactEmail: user.email
  }
}

// 错误
function processUserData(user: any): any {
  return {
    displayName: user.name,
    contactEmail: user.email
  }
}
```

### 3.3 Vue.js组件规范

**组件命名**: 使用PascalCase
```vue
<!-- 正确 -->
<template>
  <TradingDashboard />
  <UserPortfolio />
</template>

<!-- 错误 -->
<template>
  <trading-dashboard />
  <user-portfolio />
</template>
```

**Props定义**:
```vue
<script setup lang="ts">
interface Props {
  userId: number
  portfolioData: Portfolio[]
  isLoading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
})
</script>
```

### 3.4 代码组织

**导入顺序**:
```typescript
// 1. Vue相关导入
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

// 2. 第三方库导入
import axios from 'axios'
import { ElMessage } from 'element-plus'

// 3. 本地导入
import { useUserStore } from '@/stores/user'
import { Portfolio } from '@/types/portfolio'
```

## 4. CSS/SCSS代码规范

### 4.1 命名规范

**使用BEM方法论**:
```scss
// 正确
.trading-dashboard {
  &__header {
    // ...
  }
  
  &__content {
    // ...
  }
  
  &--loading {
    // ...
  }
}

// 错误
.tradingDashboard {
  .header {
    // ...
  }
}
```

### 4.2 属性顺序

**遵循逻辑分组**:
```scss
.component {
  // 定位
  position: relative;
  top: 0;
  left: 0;
  z-index: 1;
  
  // 盒模型
  display: flex;
  width: 100%;
  height: auto;
  margin: 0;
  padding: 16px;
  
  // 边框
  border: 1px solid #ccc;
  border-radius: 4px;
  
  // 背景
  background-color: #fff;
  
  // 文本
  color: #333;
  font-size: 14px;
  line-height: 1.5;
  
  // 其他
  opacity: 1;
  transition: all 0.3s ease;
}
```

### 4.3 响应式设计

**使用移动优先的方法**:
```scss
.trading-card {
  // 移动端样式（默认）
  padding: 8px;
  font-size: 14px;
  
  // 平板端
  @media (min-width: 768px) {
    padding: 16px;
    font-size: 16px;
  }
  
  // 桌面端
  @media (min-width: 1024px) {
    padding: 24px;
    font-size: 18px;
  }
}
```

## 5. 代码质量标准

### 5.1 复杂度控制

**函数复杂度**: 圈复杂度不超过10
**函数长度**: 不超过50行
**类长度**: 不超过200行
**文件长度**: 不超过1000行

### 5.2 测试覆盖率

**单元测试覆盖率**: ≥80%
**集成测试覆盖率**: ≥70%
**关键业务逻辑覆盖率**: ≥95%

### 5.3 性能标准

**API响应时间**: ≤200ms（95%的请求）
**页面加载时间**: ≤3秒
**内存使用**: 合理范围内，无内存泄漏

### 5.4 代码审查标准

**必须审查项目**:
- 业务逻辑正确性
- 代码风格一致性
- 安全性检查
- 性能影响评估
- 测试覆盖率

## 6. 安全编码规范

### 6.1 输入验证

**所有外部输入必须验证**:
```python
from pydantic import BaseModel, validator

class TradingRequest(BaseModel):
    symbol: str
    quantity: int
    price: float
    
    @validator('symbol')
    def validate_symbol(cls, v):
        if not v or len(v) > 10:
            raise ValueError('股票代码无效')
        return v.upper()
    
    @validator('quantity')
    def validate_quantity(cls, v):
        if v <= 0:
            raise ValueError('数量必须大于0')
        return v
```

### 6.2 敏感信息处理

**不在代码中硬编码敏感信息**:
```python
# 正确
import os
API_KEY = os.getenv('TRADING_API_KEY')

# 错误
API_KEY = 'sk-1234567890abcdef'
```

### 6.3 SQL注入防护

**使用参数化查询**:
```python
# 正确
cursor.execute(
    "SELECT * FROM trades WHERE user_id = %s AND symbol = %s",
    (user_id, symbol)
)

# 错误
cursor.execute(
    f"SELECT * FROM trades WHERE user_id = {user_id} AND symbol = '{symbol}'"
)
```

### 6.4 XSS防护

**前端输出转义**:
```typescript
// 正确
function displayUserInput(input: string): string {
  return escapeHtml(input)
}

// 错误
function displayUserInput(input: string): string {
  return input // 直接输出用户输入
}
```

---

**遵循承诺**: 所有开发人员必须严格遵循本规范，通过代码审查确保标准执行。违反规范的代码不得合并到主分支。
