"""
海天AI纳斯达克交易系统 - 异常处理模块
基于: 项目手册4.1节MVP版本技术栈配置
创建日期: 2025年7月31日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: 全局异常处理、自定义异常类、错误响应格式化
"""

from typing import Any, Dict, Optional, Union
from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging
from datetime import datetime

# 配置日志
logger = logging.getLogger(__name__)

# =============================================================================
# 自定义异常类
# =============================================================================

class AITradingException(Exception):
    """AI交易系统基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        error_code: str = "SYSTEM_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class DatabaseException(AITradingException):
    """数据库操作异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="DATABASE_ERROR",
            details=details
        )


class AuthenticationException(AITradingException):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="AUTHENTICATION_ERROR",
            details=details
        )


class AuthorizationException(AITradingException):
    """授权异常"""
    
    def __init__(self, message: str = "权限不足", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="AUTHORIZATION_ERROR",
            details=details
        )


class ValidationException(AITradingException):
    """数据验证异常"""
    
    def __init__(self, message: str, field: str = None, details: Optional[Dict[str, Any]] = None):
        details = details or {}
        if field:
            details["field"] = field
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details=details
        )


class BusinessLogicException(AITradingException):
    """业务逻辑异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="BUSINESS_LOGIC_ERROR",
            details=details
        )


class ExternalServiceException(AITradingException):
    """外部服务异常"""
    
    def __init__(
        self, 
        message: str, 
        service_name: str = "unknown",
        details: Optional[Dict[str, Any]] = None
    ):
        details = details or {}
        details["service_name"] = service_name
        super().__init__(
            message=message,
            error_code="EXTERNAL_SERVICE_ERROR",
            details=details
        )


class AIModelException(ExternalServiceException):
    """AI模型服务异常"""
    
    def __init__(self, message: str, model_name: str = "unknown", details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            service_name=f"ai_model_{model_name}",
            details=details
        )


class QMTException(ExternalServiceException):
    """QMT接口异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            service_name="qmt",
            details=details
        )


class TradingException(AITradingException):
    """交易相关异常"""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=message,
            error_code="TRADING_ERROR",
            details=details
        )


class RiskControlException(TradingException):
    """风险控制异常"""
    
    def __init__(self, message: str, risk_level: str = "medium", details: Optional[Dict[str, Any]] = None):
        details = details or {}
        details["risk_level"] = risk_level
        super().__init__(
            message=message,
            details=details
        )


# =============================================================================
# 错误响应格式化
# =============================================================================

def create_error_response(
    error_code: str,
    message: str,
    details: Optional[Dict[str, Any]] = None,
    status_code: int = 500,
    request_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    创建标准化错误响应
    
    Args:
        error_code: 错误代码
        message: 错误消息
        details: 错误详情
        status_code: HTTP状态码
        
    Returns:
        Dict: 标准化错误响应
    """
    return {
        "success": False,
        "error": {
            "code": error_code,
            "message": message,
            "details": details or {},
            "timestamp": datetime.now().isoformat()
        },
        "data": None,
        "meta": {
            "status_code": status_code,
            "request_id": request_id
        }
    }


# =============================================================================
# 全局异常处理器
# =============================================================================

async def ai_trading_exception_handler(request: Request, exc: AITradingException) -> JSONResponse:
    """
    AI交易系统自定义异常处理器
    
    Args:
        request: 请求对象
        exc: 异常对象
        
    Returns:
        JSONResponse: JSON错误响应
    """
    logger.error(f"AI Trading Exception: {exc.error_code} - {exc.message}", extra={
        "error_code": exc.error_code,
        "details": exc.details,
        "path": request.url.path,
        "method": request.method
    })
    
    # 根据异常类型确定HTTP状态码
    status_code_map = {
        "AUTHENTICATION_ERROR": status.HTTP_401_UNAUTHORIZED,
        "AUTHORIZATION_ERROR": status.HTTP_403_FORBIDDEN,
        "VALIDATION_ERROR": status.HTTP_400_BAD_REQUEST,
        "BUSINESS_LOGIC_ERROR": status.HTTP_400_BAD_REQUEST,
        "DATABASE_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
        "EXTERNAL_SERVICE_ERROR": status.HTTP_503_SERVICE_UNAVAILABLE,
        "TRADING_ERROR": status.HTTP_400_BAD_REQUEST,
    }
    
    status_code = status_code_map.get(exc.error_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return JSONResponse(
        status_code=status_code,
        content=create_error_response(
            error_code=exc.error_code,
            message=exc.message,
            details=exc.details,
            status_code=status_code,
            request_id=request.headers.get("request-id")
        )
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """
    HTTP异常处理器
    
    Args:
        request: 请求对象
        exc: HTTP异常对象
        
    Returns:
        JSONResponse: JSON错误响应
    """
    logger.warning(f"HTTP Exception: {exc.status_code} - {exc.detail}", extra={
        "status_code": exc.status_code,
        "path": request.url.path,
        "method": request.method
    })
    
    return JSONResponse(
        status_code=exc.status_code,
        content=create_error_response(
            error_code=f"HTTP_{exc.status_code}",
            message=exc.detail,
            status_code=exc.status_code
        )
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """
    请求验证异常处理器
    
    Args:
        request: 请求对象
        exc: 验证异常对象
        
    Returns:
        JSONResponse: JSON错误响应
    """
    logger.warning(f"Validation Exception: {exc.errors()}", extra={
        "errors": exc.errors(),
        "path": request.url.path,
        "method": request.method
    })
    
    # 格式化验证错误
    validation_errors = []
    try:
        # 处理Pydantic v2格式
        if hasattr(exc, 'errors') and callable(exc.errors):
            errors = exc.errors()
        else:
            # 处理直接的ValidationError对象
            errors = exc.error_dict() if hasattr(exc, 'error_dict') else [{"loc": ["unknown"], "msg": str(exc), "type": "validation_error"}]

        for error in errors:
            validation_errors.append({
                "field": ".".join(str(loc) for loc in error.get("loc", ["unknown"])),
                "message": error.get("msg", str(error)),
                "type": error.get("type", "validation_error")
            })
    except Exception as e:
        # 如果解析失败，使用简单格式
        validation_errors.append({
            "field": "unknown",
            "message": str(exc),
            "type": "validation_error"
        })
    
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=create_error_response(
            error_code="VALIDATION_ERROR",
            message="请求数据验证失败",
            details={"validation_errors": validation_errors},
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY
        )
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    通用异常处理器
    
    Args:
        request: 请求对象
        exc: 异常对象
        
    Returns:
        JSONResponse: JSON错误响应
    """
    logger.error(f"Unhandled Exception: {type(exc).__name__} - {str(exc)}", extra={
        "exception_type": type(exc).__name__,
        "path": request.url.path,
        "method": request.method
    }, exc_info=True)
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=create_error_response(
            error_code="INTERNAL_SERVER_ERROR",
            message="服务器内部错误，请稍后重试",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    )
