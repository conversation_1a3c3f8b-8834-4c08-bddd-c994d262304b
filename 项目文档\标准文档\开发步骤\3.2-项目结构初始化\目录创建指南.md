# 海天AI纳斯达克交易系统 - 目录创建指南

## 文档信息
- **创建日期**: 2025年7月13日
- **最后更新**: 2025年7月13日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: 项目目录结构创建和初始化指导
- **技术基础**: FastAPI 0.116.1 + Vue.js 3.5.x + Docker

## 版本更新记录
| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 1.0.0 | 2025-07-13 | 初始版本，完整目录创建指南 | 海天AI开发团队 |

## 目录
1. [项目目录结构创建步骤](#1-项目目录结构创建步骤)
2. [FastAPI标准目录结构说明](#2-fastapi标准目录结构说明)
3. [基础文件和配置模板说明](#3-基础文件和配置模板说明)
4. [__init__.py文件创建规范](#4-__init__py文件创建规范)
5. [权限设置和目录保护方法](#5-权限设置和目录保护方法)

## 1. 项目目录结构创建步骤

### 1.1 主要目录创建

**步骤1：创建根级目录**
```bash
# 在项目根目录执行
mkdir -p frontend backend infrastructure tests deployment
```

**步骤2：创建后端应用目录**
```bash
# 创建后端主要目录
mkdir -p backend/app backend/tests backend/alembic

# 创建应用分层目录
mkdir -p backend/app/api backend/app/core backend/app/models backend/app/schemas backend/app/crud
```

**步骤3：创建API版本控制目录**
```bash
# 创建API版本化目录结构
mkdir -p backend/app/api/v1/endpoints
```

**步骤4：创建业务模块目录**
```bash
# 创建核心业务模块
mkdir -p backend/app/ai-traders backend/app/trading-engine backend/app/supervisor backend/app/shared
```

**步骤5：创建基础设施目录**
```bash
# 创建基础设施配置目录
mkdir -p infrastructure/nginx infrastructure/nginx/ssl
mkdir -p infrastructure/postgres infrastructure/redis
mkdir -p infrastructure/monitoring infrastructure/monitoring/grafana
```

### 1.2 完整目录创建脚本

```bash
#!/bin/bash
# 海天AI纳斯达克交易系统目录创建脚本

echo "开始创建项目目录结构..."

# 主要目录
mkdir -p frontend backend infrastructure tests deployment

# 后端目录结构
mkdir -p backend/app backend/tests backend/alembic

# 后端应用分层
mkdir -p backend/app/api/v1/endpoints
mkdir -p backend/app/core
mkdir -p backend/app/ai-traders
mkdir -p backend/app/trading-engine
mkdir -p backend/app/supervisor
mkdir -p backend/app/shared
mkdir -p backend/app/models
mkdir -p backend/app/schemas
mkdir -p backend/app/crud

# 基础设施目录
mkdir -p infrastructure/nginx/ssl
mkdir -p infrastructure/postgres
mkdir -p infrastructure/redis
mkdir -p infrastructure/monitoring/grafana

echo "项目目录结构创建完成！"
```

### 1.3 目录创建验证

**验证命令**：
```bash
# 查看项目根目录结构
tree -L 2

# 查看后端应用结构
tree backend/app -L 2

# 查看基础设施结构
tree infrastructure -L 2
```

**预期输出**：
```
.
├── backend/
│   ├── alembic/
│   ├── app/
│   └── tests/
├── deployment/
├── frontend/
├── infrastructure/
│   ├── monitoring/
│   ├── nginx/
│   ├── postgres/
│   └── redis/
└── tests/
```

## 2. FastAPI标准目录结构说明

### 2.1 FastAPI应用架构

FastAPI推荐的项目结构遵循以下原则：

**分层架构**：
- `api/` - API路由层，处理HTTP请求
- `core/` - 核心配置和工具
- `models/` - 数据库模型定义
- `schemas/` - Pydantic数据验证模型
- `crud/` - 数据库操作层

**模块化设计**：
- 按业务领域组织代码模块
- 每个模块包含完整的业务逻辑
- 模块间通过明确的接口通信

### 2.2 目录职责说明

**backend/app/api/**
- 功能：HTTP路由定义和请求处理
- 结构：按版本和功能模块组织
- 文件：路由定义、依赖注入、中间件

**backend/app/core/**
- 功能：应用核心配置和工具
- 内容：配置管理、安全策略、数据库连接
- 特点：全局共享、高度稳定

**backend/app/models/**
- 功能：数据库模型定义
- 技术：SQLModel（SQLAlchemy + Pydantic）
- 职责：表结构、关系映射、约束定义

**backend/app/schemas/**
- 功能：API数据验证和序列化
- 技术：Pydantic模型
- 用途：请求验证、响应格式化、类型检查

**backend/app/crud/**
- 功能：数据库操作封装
- 模式：Repository模式
- 内容：增删改查、复杂查询、事务管理

### 2.3 业务模块结构

每个业务模块（ai-traders、trading-engine、supervisor）采用统一结构：

```
module_name/
├── __init__.py          # 模块初始化
├── service.py           # 业务逻辑服务
├── models.py            # 模块特定数据模型
├── schemas.py           # 模块API数据模型
├── dependencies.py      # 模块依赖注入
└── utils.py            # 模块工具函数
```

## 3. 基础文件和配置模板说明

### 3.1 Python包初始化文件

**__init__.py文件作用**：
- 标识目录为Python包
- 控制包的导入行为
- 定义包级别的API

**创建规范**：
```python
# backend/app/__init__.py
"""
海天AI纳斯达克交易系统 - 应用包
"""

__version__ = "1.0.0"
__author__ = "海天AI量化交易开发团队"
```

### 3.2 FastAPI应用入口文件

**backend/app/main.py模板**：
```python
"""
FastAPI应用主入口
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.v1.api import api_router
from app.core.config import settings

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(api_router, prefix=settings.API_V1_STR)
```

### 3.3 配置文件模板

**backend/requirements.txt模板**：
```
fastapi==0.116.1
uvicorn[standard]==0.32.1
sqlmodel==0.0.22
alembic==1.14.0
python-multipart==0.0.12
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-decouple==3.8
asyncpg==0.29.0
redis==5.2.0
celery==5.4.0
pytest==8.3.3
pytest-asyncio==0.24.0
```

**backend/pyproject.toml模板**：
```toml
[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "ai-nasdaq-trading"
version = "1.0.0"
description = "海天AI纳斯达克交易系统"
authors = [{name = "海天AI开发团队"}]
dependencies = [
    "fastapi>=0.116.1",
    "uvicorn[standard]>=0.32.1",
    "sqlmodel>=0.0.22",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
```

### 3.4 Docker配置文件

**backend/Dockerfile模板**：
```dockerfile
FROM python:3.13.2-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

## 4. __init__.py文件创建规范

### 4.1 创建原则

**必须创建的位置**：
- 所有Python包目录
- 所有业务模块目录
- 所有子包目录

**可选创建的位置**：
- 配置目录（如果包含Python模块）
- 工具目录（如果需要包导入）

### 4.2 内容规范

**基础包初始化**：
```python
# backend/app/__init__.py
"""
海天AI纳斯达克交易系统 - 核心应用包
"""

__version__ = "1.0.0"
```

**API包初始化**：
```python
# backend/app/api/__init__.py
"""
API路由包
"""
```

**业务模块初始化**：
```python
# backend/app/ai-traders/__init__.py
"""
AI交易员服务模块
"""

from .service import AITraderService
from .schemas import AITraderCreate, AITraderUpdate

__all__ = ["AITraderService", "AITraderCreate", "AITraderUpdate"]
```

### 4.3 批量创建脚本

```bash
#!/bin/bash
# 批量创建__init__.py文件

# 定义需要创建__init__.py的目录
DIRS=(
    "backend/app"
    "backend/app/api"
    "backend/app/api/v1"
    "backend/app/api/v1/endpoints"
    "backend/app/core"
    "backend/app/ai-traders"
    "backend/app/trading-engine"
    "backend/app/supervisor"
    "backend/app/shared"
    "backend/app/models"
    "backend/app/schemas"
    "backend/app/crud"
)

# 创建__init__.py文件
for dir in "${DIRS[@]}"; do
    if [ -d "$dir" ]; then
        touch "$dir/__init__.py"
        echo "创建 $dir/__init__.py"
    fi
done

echo "__init__.py文件创建完成！"
```

## 5. 权限设置和目录保护方法

### 5.1 Linux/Unix权限设置

**基本权限配置**：
```bash
# 设置项目根目录权限
chmod 755 .

# 设置源代码目录权限
find backend/app -type d -exec chmod 755 {} \;
find backend/app -type f -exec chmod 644 {} \;

# 设置可执行脚本权限
chmod +x scripts/*.sh
```

**敏感文件保护**：
```bash
# 保护配置文件
chmod 600 infrastructure/.env
chmod 600 infrastructure/nginx/ssl/*

# 保护数据目录
chmod 700 data/
chmod 700 logs/
```

### 5.2 Docker容器权限

**Dockerfile用户设置**：
```dockerfile
# 创建非root用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录权限
RUN chown -R appuser:appuser /app

# 切换到非root用户
USER appuser
```

**Docker Compose权限配置**：
```yaml
services:
  backend:
    user: "1000:1000"  # 指定用户ID和组ID
    volumes:
      - ./backend/app:/app/app:ro  # 只读挂载
```

### 5.3 Git忽略配置

**.gitignore文件**：
```
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/

# 环境配置
.env
.env.local
.env.production

# 日志文件
logs/
*.log

# 数据库
*.db
*.sqlite3

# IDE
.vscode/
.idea/
*.swp
*.swo

# 操作系统
.DS_Store
Thumbs.db

# 临时文件
tmp/
temp/
*.tmp
```

### 5.4 安全检查清单

**目录权限检查**：
- [ ] 源代码目录权限正确（755/644）
- [ ] 配置文件权限安全（600）
- [ ] 日志目录权限适当（700）
- [ ] SSL证书权限保护（600）

**文件保护检查**：
- [ ] 敏感配置文件已加入.gitignore
- [ ] 数据库文件不在版本控制中
- [ ] 日志文件不在版本控制中
- [ ] 临时文件已正确忽略

**容器安全检查**：
- [ ] 使用非root用户运行
- [ ] 最小权限原则
- [ ] 只读挂载敏感目录
- [ ] 网络隔离配置

---

*本指南确保项目目录结构的正确创建和安全配置，为后续开发工作提供坚实基础。*
