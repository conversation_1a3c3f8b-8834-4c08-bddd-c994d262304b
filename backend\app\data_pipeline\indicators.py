"""
海天AI纳斯达克交易系统 - 技术指标计算模块
基于: 项目手册4.3节数据处理管道设计
创建日期: 2025年8月1日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: 实时计算技术指标，为AI决策提供数据支持
"""

import logging
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from decimal import Decimal
from collections import deque
import math

from app.schemas.market import MarketData, TechnicalIndicator

logger = logging.getLogger(__name__)


class TechnicalIndicatorCalculator(ABC):
    """技术指标计算器抽象基类"""
    
    def __init__(self, period: int = 14):
        self.period = period
        self.data_buffer = deque(maxlen=period * 2)  # 保留更多历史数据
        self.max_history_size = period * 2
        self.price_history = []  # 为了兼容测试
        self.calculated_count = 0
    
    @abstractmethod
    async def calculate(self, market_data: MarketData) -> Optional[TechnicalIndicator]:
        """计算技术指标"""
        pass
    
    @abstractmethod
    def get_indicator_name(self) -> str:
        """获取指标名称"""
        pass
    
    def add_data(self, market_data: MarketData):
        """添加市场数据到缓冲区"""
        self.data_buffer.append(market_data)
        # 同时更新price_history以兼容测试
        if hasattr(self, 'price_history'):
            self.price_history.append(market_data.current_price)
    
    def has_enough_data(self) -> bool:
        """检查是否有足够的数据进行计算"""
        return len(self.data_buffer) >= self.period
    
    def get_prices(self, price_type: str = "close") -> List[Decimal]:
        """获取价格序列"""
        prices = []
        for data in self.data_buffer:
            if price_type == "close":
                prices.append(data.current_price)
            elif price_type == "open":
                prices.append(data.open_price or data.current_price)
            elif price_type == "high":
                prices.append(data.high_price or data.current_price)
            elif price_type == "low":
                prices.append(data.low_price or data.current_price)
        return prices


class RSICalculator(TechnicalIndicatorCalculator):
    """RSI相对强弱指数计算器"""
    
    def __init__(self, period: int = 14):
        super().__init__(period)
        self.gains = deque(maxlen=period)
        self.losses = deque(maxlen=period)
        self.indicator_name = f"RSI_{period}"
    
    def get_indicator_name(self) -> str:
        return "RSI"
    
    async def calculate(self, market_data: MarketData) -> Optional[TechnicalIndicator]:
        """计算RSI指标"""
        self.add_data(market_data)
        
        if len(self.data_buffer) < 2:
            return None
        
        # 计算价格变化
        current_price = self.data_buffer[-1].current_price
        previous_price = self.data_buffer[-2].current_price
        price_change = current_price - previous_price
        
        # 分离涨跌
        gain = max(price_change, Decimal('0'))
        loss = abs(min(price_change, Decimal('0')))
        
        self.gains.append(gain)
        self.losses.append(loss)
        
        if not self.has_enough_data():
            return None
        
        # 计算平均涨跌幅
        avg_gain = sum(self.gains) / len(self.gains)
        avg_loss = sum(self.losses) / len(self.losses)
        
        if avg_loss == 0:
            rsi_value = Decimal('100')
        else:
            rs = avg_gain / avg_loss
            rsi_value = Decimal('100') - (Decimal('100') / (Decimal('1') + rs))
        
        self.calculated_count += 1
        
        return TechnicalIndicator(
            symbol=market_data.symbol,
            indicator_name=self.get_indicator_name(),
            indicator_value=rsi_value,
            period=self.period,
            timestamp=market_data.timestamp or datetime.now()
        )


class MACDCalculator(TechnicalIndicatorCalculator):
    """MACD指标计算器"""
    
    def __init__(self, fast_period: int = 12, slow_period: int = 26, signal_period: int = 9):
        super().__init__(slow_period)
        self.fast_period = fast_period
        self.slow_period = slow_period
        self.signal_period = signal_period
        self.fast_ema = None
        self.slow_ema = None
        self.signal_ema = None
        self.macd_history = deque(maxlen=signal_period * 2)
        self.indicator_name = "MACD"
    
    def get_indicator_name(self) -> str:
        return "MACD"
    
    async def calculate(self, market_data: MarketData) -> Optional[TechnicalIndicator]:
        """计算MACD指标"""
        self.add_data(market_data)
        
        if not self.has_enough_data():
            return None
        
        current_price = market_data.current_price
        
        # 计算快速EMA
        if self.fast_ema is None:
            prices = self.get_prices()[-self.fast_period:]
            self.fast_ema = sum(prices) / len(prices)
        else:
            multiplier = Decimal('2') / (self.fast_period + 1)
            self.fast_ema = (current_price * multiplier) + (self.fast_ema * (Decimal('1') - multiplier))
        
        # 计算慢速EMA
        if self.slow_ema is None:
            prices = self.get_prices()[-self.slow_period:]
            self.slow_ema = sum(prices) / len(prices)
        else:
            multiplier = Decimal('2') / (self.slow_period + 1)
            self.slow_ema = (current_price * multiplier) + (self.slow_ema * (Decimal('1') - multiplier))
        
        # 计算MACD线
        macd_line = self.fast_ema - self.slow_ema
        self.macd_history.append(macd_line)
        
        # 计算信号线
        if self.signal_ema is None and len(self.macd_history) >= self.signal_period:
            self.signal_ema = sum(list(self.macd_history)[-self.signal_period:]) / self.signal_period
        elif self.signal_ema is not None:
            multiplier = Decimal('2') / (self.signal_period + 1)
            self.signal_ema = (macd_line * multiplier) + (self.signal_ema * (Decimal('1') - multiplier))
        
        # 计算柱状图
        histogram = macd_line - (self.signal_ema or Decimal('0'))
        
        self.calculated_count += 1
        
        return TechnicalIndicator(
            symbol=market_data.symbol,
            indicator_name=self.get_indicator_name(),
            indicator_value=macd_line,
            period=self.slow_period,
            timestamp=market_data.timestamp or datetime.now(),
            additional_data={
                "macd_line": float(macd_line),
                "signal_line": float(self.signal_ema or 0),
                "histogram": float(histogram)
            }
        )


class MACalculator(TechnicalIndicatorCalculator):
    """移动平均线计算器"""
    
    def __init__(self, period: int = 20, ma_type: str = "SMA"):
        super().__init__(period)
        self.ma_type = ma_type  # SMA, EMA
        self.ema_value = None
        self.indicator_name = f"MA_{ma_type}_{period}"
    
    def get_indicator_name(self) -> str:
        return f"MA_{self.ma_type}_{self.period}"
    
    async def calculate(self, market_data: MarketData) -> Optional[TechnicalIndicator]:
        """计算移动平均线"""
        self.add_data(market_data)
        
        if not self.has_enough_data():
            return None
        
        current_price = market_data.current_price
        
        if self.ma_type == "SMA":
            # 简单移动平均
            prices = self.get_prices()[-self.period:]
            ma_value = sum(prices) / len(prices)
        elif self.ma_type == "EMA":
            # 指数移动平均
            if self.ema_value is None:
                prices = self.get_prices()[-self.period:]
                self.ema_value = sum(prices) / len(prices)
            else:
                multiplier = Decimal('2') / (self.period + 1)
                self.ema_value = (current_price * multiplier) + (self.ema_value * (Decimal('1') - multiplier))
            ma_value = self.ema_value
        else:
            return None
        
        self.calculated_count += 1
        
        return TechnicalIndicator(
            symbol=market_data.symbol,
            indicator_name=self.get_indicator_name(),
            indicator_value=ma_value,
            period=self.period,
            timestamp=market_data.timestamp or datetime.now()
        )


class BollingerBandsCalculator(TechnicalIndicatorCalculator):
    """布林带计算器"""
    
    def __init__(self, period: int = 20, std_dev: float = 2.0):
        super().__init__(period)
        self.std_dev = std_dev
        self.indicator_name = "BB"  # 匹配测试期望
    
    def get_indicator_name(self) -> str:
        return "BOLLINGER_BANDS"
    
    async def calculate(self, market_data: MarketData) -> Optional[TechnicalIndicator]:
        """计算布林带"""
        self.add_data(market_data)
        
        if not self.has_enough_data():
            return None
        
        prices = self.get_prices()[-self.period:]
        
        # 计算中轨（简单移动平均）
        middle_band = sum(prices) / len(prices)
        
        # 计算标准差
        variance = sum([(price - middle_band) ** 2 for price in prices]) / len(prices)
        std_deviation = Decimal(str(math.sqrt(float(variance))))
        
        # 计算上下轨
        upper_band = middle_band + (std_deviation * Decimal(str(self.std_dev)))
        lower_band = middle_band - (std_deviation * Decimal(str(self.std_dev)))
        
        # 计算带宽和位置
        bandwidth = (upper_band - lower_band) / middle_band * 100
        current_price = market_data.current_price
        bb_position = (current_price - lower_band) / (upper_band - lower_band) * 100
        
        self.calculated_count += 1
        
        return TechnicalIndicator(
            symbol=market_data.symbol,
            indicator_name=self.get_indicator_name(),
            indicator_value=middle_band,
            period=self.period,
            timestamp=market_data.timestamp or datetime.now(),
            additional_data={
                "upper_band": float(upper_band),
                "middle_band": float(middle_band),
                "lower_band": float(lower_band),
                "bandwidth": float(bandwidth),
                "bb_position": float(bb_position)
            }
        )


class VolumeIndicatorCalculator(TechnicalIndicatorCalculator):
    """成交量指标计算器"""
    
    def __init__(self, period: int = 20):
        super().__init__(period)
        self.indicator_name = "VOLUME"  # 匹配测试期望
    
    def get_indicator_name(self) -> str:
        return "VOLUME"  # 匹配测试期望
    
    async def calculate(self, market_data: MarketData) -> Optional[TechnicalIndicator]:
        """计算成交量移动平均"""
        self.add_data(market_data)
        
        if not self.has_enough_data():
            return None
        
        volumes = [data.volume for data in list(self.data_buffer)[-self.period:]]
        avg_volume = sum(volumes) / len(volumes)
        
        # 计算成交量比率
        current_volume = market_data.volume
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
        
        self.calculated_count += 1
        
        return TechnicalIndicator(
            symbol=market_data.symbol,
            indicator_name=self.get_indicator_name(),
            indicator_value=Decimal(str(avg_volume)),
            period=self.period,
            timestamp=market_data.timestamp or datetime.now(),
            additional_data={
                "current_volume": current_volume,
                "average_volume": avg_volume,
                "volume_ratio": float(volume_ratio)
            }
        )
