# =============================================================================
# 海天AI纳斯达克交易系统 - Nginx反向代理服务容器配置
# 基于: 项目手册4.1节MVP版本技术栈配置
# 创建日期: 2025年7月13日
# 用途: 反向代理、负载均衡、SSL终端、静态文件服务
# =============================================================================

FROM nginx:1.25-alpine

# 设置环境变量
ENV NGINX_ENVSUBST_TEMPLATE_DIR=/etc/nginx/templates
ENV NGINX_ENVSUBST_OUTPUT_DIR=/etc/nginx/conf.d

# 安装系统依赖
RUN apk add --no-cache \
    curl \
    openssl \
    gettext \
    certbot \
    certbot-nginx

# 创建非root用户
RUN addgroup -g 1000 appgroup \
    && adduser -u 1000 -G appgroup -s /bin/sh -D appuser

# 创建必要的目录
RUN mkdir -p /etc/nginx/ssl \
    && mkdir -p /var/log/nginx \
    && mkdir -p /var/cache/nginx \
    && mkdir -p /etc/letsencrypt

# 复制配置文件
COPY nginx/nginx.conf /etc/nginx/nginx.conf
COPY nginx/conf.d/ /etc/nginx/conf.d/

# 设置权限
RUN chown -R appuser:appgroup /var/cache/nginx \
    && chown -R appuser:appgroup /var/log/nginx \
    && chown -R appuser:appgroup /etc/nginx/ssl \
    && chmod -R 755 /etc/nginx

# 暴露端口
EXPOSE 80 443

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=10s --retries=3 \
    CMD curl -f http://localhost/nginx-health || exit 1

# 启动命令
CMD ["nginx", "-g", "daemon off;"]

# =============================================================================
# 镜像元数据
# =============================================================================
LABEL maintainer="海天AI量化交易开发团队"
LABEL version="1.0.0"
LABEL description="海天AI纳斯达克交易系统Nginx反向代理服务"
LABEL tech.stack="Nginx 1.25 + Alpine Linux"
LABEL build.date="2025-07-13"
