# 海天AI纳斯达克交易系统 - 项目结构设计文档

## 文档信息
- **创建日期**: 2025年7月13日
- **最后更新**: 2025年7月13日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: AI驱动的纳斯达克实时量化交易系统项目结构设计
- **技术基础**: FastAPI 0.116.1 + Vue.js 3.5.x + PostgreSQL 17.5

## 版本更新记录
| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 1.0.0 | 2025-07-13 | 初始版本，完整项目结构设计 | 海天AI开发团队 |

## 目录
1. [项目结构概览](#1-项目结构概览)
2. [FastAPI分层架构设计原理](#2-fastapi分层架构设计原理)
3. [各模块职责和边界定义](#3-各模块职责和边界定义)
4. [模块间依赖关系](#4-模块间依赖关系)
5. [API版本控制策略](#5-api版本控制策略)

## 1. 项目结构概览

### 1.1 完整目录结构图

```
ai-trading-system/
├── frontend/                 # 前端应用（Vue.js 3.5.x + TypeScript）
├── backend/                  # 后端服务集群
│   ├── app/                 # 应用代码目录（核心应用层）
│   │   ├── api/             # API路由层
│   │   │   ├── v1/          # API版本控制
│   │   │   │   ├── endpoints/  # 具体端点实现
│   │   │   │   │   ├── auth.py      # 认证相关端点
│   │   │   │   │   ├── traders.py   # AI交易员管理端点
│   │   │   │   │   ├── trading.py   # 交易相关端点
│   │   │   │   │   └── monitoring.py # 监控相关端点
│   │   │   │   └── api.py   # API路由汇总
│   │   │   └── deps.py      # 全局依赖
│   │   ├── core/            # 核心配置和工具
│   │   │   ├── config.py    # 应用配置
│   │   │   ├── security.py  # 安全相关
│   │   │   ├── database.py  # 数据库连接
│   │   │   └── exceptions.py # 全局异常处理
│   │   ├── ai-traders/      # AI交易员服务模块
│   │   │   ├── service.py   # AI交易员业务逻辑
│   │   │   ├── models.py    # AI交易员数据模型
│   │   │   ├── schemas.py   # AI交易员Pydantic模型
│   │   │   ├── dependencies.py # 模块特定依赖
│   │   │   └── utils.py     # 工具函数
│   │   ├── trading-engine/  # 交易执行引擎模块
│   │   │   ├── service.py   # 交易执行业务逻辑
│   │   │   ├── qmt_client.py # QMT接口客户端
│   │   │   ├── models.py    # 交易相关数据模型
│   │   │   └── schemas.py   # 交易Pydantic模型
│   │   ├── supervisor/      # AI交易总监模块
│   │   │   ├── service.py   # 监督业务逻辑
│   │   │   ├── analyzer.py  # 多模型分析器
│   │   │   └── schemas.py   # 监督相关模型
│   │   ├── shared/          # 共享组件和工具
│   │   │   ├── utils.py     # 通用工具函数
│   │   │   ├── constants.py # 全局常量
│   │   │   ├── enums.py     # 枚举定义
│   │   │   └── validators.py # 自定义验证器
│   │   ├── models/          # 数据库模型（SQLModel）
│   │   │   ├── base.py      # 基础模型类
│   │   │   ├── user.py      # 用户模型
│   │   │   ├── trader.py    # 交易员模型
│   │   │   └── trading.py   # 交易记录模型
│   │   ├── schemas/         # Pydantic数据模型
│   │   │   ├── base.py      # 基础Schema类
│   │   │   ├── user.py      # 用户相关Schema
│   │   │   ├── trader.py    # 交易员相关Schema
│   │   │   └── trading.py   # 交易相关Schema
│   │   ├── crud/            # 数据库操作层
│   │   │   ├── base.py      # 基础CRUD操作
│   │   │   ├── user.py      # 用户CRUD操作
│   │   │   ├── trader.py    # 交易员CRUD操作
│   │   │   └── trading.py   # 交易记录CRUD操作
│   │   └── main.py          # FastAPI应用入口
│   ├── tests/               # 后端测试用例
│   ├── alembic/             # 数据库迁移
│   ├── requirements.txt     # Python依赖
│   ├── pyproject.toml       # 项目配置文件
│   └── README.md            # 后端说明文档
├── infrastructure/          # 基础设施配置
│   ├── docker-compose.yml   # Docker容器编排配置
│   ├── .env                 # 环境变量配置
│   ├── nginx/               # Nginx配置
│   │   ├── nginx.conf       # Nginx主配置
│   │   ├── default.conf     # 默认站点配置
│   │   └── ssl/             # SSL证书目录
│   ├── postgres/            # PostgreSQL配置
│   │   ├── init.sql         # 数据库初始化脚本
│   │   └── postgresql.conf  # PostgreSQL配置文件
│   ├── redis/               # Redis配置
│   │   └── redis.conf       # Redis配置文件
│   └── monitoring/          # 监控相关配置
│       ├── prometheus.yml   # Prometheus配置
│       └── grafana/         # Grafana配置
├── tests/                   # 集成测试
├── deployment/              # 部署配置
└── 项目文档/                    # 项目文档
```

### 1.2 设计原则

**前后端分离**
- 前端Vue.js与后端FastAPI完全独立开发和部署
- 通过RESTful API进行数据交互
- 支持独立的技术栈升级和维护

**分层架构**
- 遵循FastAPI最佳实践，采用API、Core、Models、Schemas、CRUD分层
- 每层职责明确，便于维护和测试
- 支持依赖注入和控制反转

**领域驱动设计**
- 按业务领域（AI交易员、交易引擎、监督等）组织代码模块
- 每个业务模块内部高内聚，模块间低耦合
- 清晰的业务边界和接口定义

**版本控制**
- API采用版本化设计（v1、v2），便于向后兼容
- 支持多版本并存和平滑升级
- 版本间隔离，避免相互影响

**配置管理**
- core目录统一管理应用配置
- infrastructure目录管理基础设施配置
- 环境变量与代码分离，支持多环境部署

## 2. FastAPI分层架构设计原理

### 2.1 分层架构概述

FastAPI分层架构遵循经典的三层架构模式，结合现代微服务设计理念：

```
┌─────────────────┐
│   API Layer    │  ← 路由层：处理HTTP请求和响应
├─────────────────┤
│ Business Layer │  ← 业务层：核心业务逻辑处理
├─────────────────┤
│   Data Layer   │  ← 数据层：数据访问和持久化
└─────────────────┘
```

### 2.2 各层职责

**API层（api/）**
- HTTP路由定义和请求处理
- 请求参数验证和响应格式化
- 认证授权和权限控制
- API文档生成和版本管理

**业务层（ai-traders/、trading-engine/、supervisor/）**
- 核心业务逻辑实现
- 业务规则验证和处理
- 外部服务集成（QMT、AI模型等）
- 业务流程编排和协调

**数据层（models/、schemas/、crud/）**
- 数据模型定义和映射
- 数据库操作和事务管理
- 数据验证和序列化
- 缓存策略和优化

**核心层（core/）**
- 应用配置和环境管理
- 安全策略和加密服务
- 数据库连接和会话管理
- 全局异常处理和日志

**共享层（shared/）**
- 通用工具函数和帮助类
- 全局常量和枚举定义
- 自定义验证器和装饰器
- 跨模块共享组件

### 2.3 依赖注入原理

FastAPI的依赖注入系统提供了强大的控制反转能力：

```python
# 依赖定义
def get_db() -> Session:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# 依赖使用
@router.get("/traders/")
def get_traders(db: Session = Depends(get_db)):
    return crud.trader.get_multi(db)
```

**优势**：
- 提高代码可测试性
- 降低模块间耦合
- 支持配置和环境切换
- 便于单元测试和模拟

## 3. 各模块职责和边界定义

### 3.1 前端模块（frontend/）

**职责**：
- 用户界面展示和交互
- 实时数据可视化
- 用户操作和输入处理
- 前端路由和状态管理

**边界**：
- 不直接访问数据库
- 通过API与后端通信
- 不包含业务逻辑处理
- 专注于用户体验优化

### 3.2 API路由层（backend/app/api/）

**职责**：
- HTTP请求路由和分发
- 请求参数验证和解析
- 响应数据格式化
- API文档自动生成

**边界**：
- 不包含业务逻辑
- 不直接操作数据库
- 通过依赖注入调用业务层
- 专注于HTTP协议处理

### 3.3 AI交易员模块（backend/app/ai-traders/）

**职责**：
- AI交易员生命周期管理
- 交易决策生成和执行
- 个人档案和学习记录维护
- 多模型协调和冲突处理

**边界**：
- 独立的资金管理和风险控制
- 不直接访问其他交易员数据
- 通过标准接口与交易引擎通信
- 专注于AI决策逻辑

### 3.4 交易执行引擎（backend/app/trading-engine/）

**职责**：
- 交易指令执行和确认
- QMT接口集成和通信
- 订单状态跟踪和管理
- 交易记录和审计日志

**边界**：
- 不包含交易决策逻辑
- 专注于交易执行可靠性
- 提供统一的交易接口
- 确保交易数据一致性

### 3.5 AI交易总监（backend/app/supervisor/）

**职责**：
- 全局风险监控和控制
- 多AI交易员协调管理
- 异常情况处理和报警
- 系统性能监控和优化

**边界**：
- 不直接执行交易操作
- 专注于监督和协调功能
- 提供全局视角和决策支持
- 确保系统稳定运行

### 3.6 数据层模块

**Models（backend/app/models/）**：
- 数据库表结构定义
- 关系映射和约束设置
- 数据完整性保证

**Schemas（backend/app/schemas/）**：
- API输入输出数据验证
- 数据序列化和反序列化
- 类型安全和格式检查

**CRUD（backend/app/crud/）**：
- 数据库操作封装
- 查询优化和缓存策略
- 事务管理和错误处理

## 4. 模块间依赖关系

### 4.1 依赖关系图

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Frontend  │───▶│  API Layer  │───▶│   Business  │
└─────────────┘    └─────────────┘    │    Layer    │
                                      └─────────────┘
                                             │
                                             ▼
                   ┌─────────────┐    ┌─────────────┐
                   │    Core     │◀───│ Data Layer  │
                   │   Layer     │    └─────────────┘
                   └─────────────┘
                          │
                          ▼
                   ┌─────────────┐
                   │   Shared    │
                   │ Components  │
                   └─────────────┘
```

### 4.2 依赖规则

**向下依赖**：
- API层依赖业务层和核心层
- 业务层依赖数据层和共享层
- 数据层依赖核心层和共享层

**禁止循环依赖**：
- 任何模块不得形成循环依赖
- 通过接口抽象解决依赖问题
- 使用依赖注入实现控制反转

**共享组件使用**：
- 所有层都可以使用共享组件
- 共享组件不依赖具体业务模块
- 保持共享组件的通用性和稳定性

## 5. API版本控制策略

### 5.1 版本控制方案

**URL路径版本控制**：
```
/api/v1/traders/          # 版本1 API
/api/v2/traders/          # 版本2 API
```

**版本兼容性策略**：
- 向后兼容：新版本保持对旧版本的兼容
- 渐进式升级：支持多版本并存
- 废弃通知：提前通知版本废弃计划

### 5.2 版本管理规范

**版本号规则**：
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

**版本生命周期**：
- 开发阶段：内部测试和验证
- 发布阶段：正式对外提供服务
- 维护阶段：bug修复和安全更新
- 废弃阶段：停止新功能开发
- 下线阶段：完全停止服务

### 5.3 版本迁移策略

**平滑迁移**：
- 提供版本迁移指南
- 支持数据格式转换
- 保证服务连续性

**监控和告警**：
- 版本使用情况统计
- 性能指标监控
- 异常情况报警

---

*本文档为海天AI纳斯达克交易系统项目结构设计的核心指导文档，确保项目架构的科学性、可扩展性和可维护性。*
