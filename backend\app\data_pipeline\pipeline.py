"""
海天AI纳斯达克交易系统 - 数据管道主控制器
基于: 项目手册4.3节数据处理管道设计
创建日期: 2025年8月1日
技术栈: FastAPI 0.116.1 + Python 3.13.2
功能: 数据处理管道主控制器，协调各个模块的工作
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from contextlib import asynccontextmanager

from .collector import DataCollector, MockDataCollector, QMTDataCollector
from .processor import DataProcessor, MarketDataProcessor, TickDataProcessor
from .indicators import (
    TechnicalIndicatorCalculator, RSICalculator, MACDCalculator, 
    MACalculator, BollingerBandsCalculator, VolumeIndicatorCalculator
)
from .distributor import DataDistributor, WebSocketDistributor, AITraderDistributor
from .storage import DataStorage, DatabaseStorage, CacheStorage

from app.schemas.market import MarketData, TechnicalIndicator
from app.core.exceptions import BusinessLogicException
from app.core.settings import TradingSettings

logger = logging.getLogger(__name__)


class DataPipeline:
    """数据处理管道基类"""
    
    def __init__(self):
        self.is_running = False
        self.start_time = None
        self.processed_count = 0
        self.error_count = 0
        self.pipeline_tasks = []
    
    async def start(self):
        """启动数据管道"""
        if self.is_running:
            logger.warning("数据管道已在运行中")
            return
        
        try:
            self.is_running = True
            self.start_time = datetime.now()
            logger.info("数据处理管道启动")
            
            await self._initialize_components()
            await self._start_pipeline_tasks()
            
        except Exception as e:
            logger.error(f"数据管道启动失败: {e}")
            await self.stop()
            raise BusinessLogicException(f"数据管道启动失败: {e}")
    
    async def stop(self):
        """停止数据管道"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 停止所有任务
        for task in self.pipeline_tasks:
            if not task.done():
                task.cancel()
        
        # 等待任务完成
        if self.pipeline_tasks:
            await asyncio.gather(*self.pipeline_tasks, return_exceptions=True)
        
        await self._cleanup_components()
        
        logger.info("数据处理管道已停止")
    
    async def _initialize_components(self):
        """初始化组件"""
        pass
    
    async def _start_pipeline_tasks(self):
        """启动管道任务"""
        pass
    
    async def _cleanup_components(self):
        """清理组件"""
        pass
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取管道统计信息"""
        runtime = datetime.now() - self.start_time if self.start_time else timedelta(0)
        
        return {
            "is_running": self.is_running,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "runtime_seconds": runtime.total_seconds(),
            "processed_count": self.processed_count,
            "error_count": self.error_count,
            "error_rate": self.error_count / max(self.processed_count, 1),
            "active_tasks": len([task for task in self.pipeline_tasks if not task.done()])
        }


class RealTimeDataPipeline(DataPipeline):
    """实时数据处理管道"""
    
    def __init__(self, trading_settings: TradingSettings):
        super().__init__()
        self.trading_settings = trading_settings
        
        # 组件初始化
        self.data_collector: Optional[DataCollector] = None
        self.market_data_processor: Optional[MarketDataProcessor] = None
        self.tick_data_processor: Optional[TickDataProcessor] = None
        self.technical_indicators: Dict[str, TechnicalIndicatorCalculator] = {}
        self.websocket_distributor: Optional[WebSocketDistributor] = None
        self.ai_trader_distributor: Optional[AITraderDistributor] = None
        self.database_storage: Optional[DatabaseStorage] = None
        self.cache_storage: Optional[CacheStorage] = None
        
        # 配置参数
        self.data_collection_interval = 1  # 1秒采集间隔
        self.indicator_calculation_interval = 5  # 5秒计算技术指标
        self.storage_flush_interval = 30  # 30秒刷新存储
    
    async def _initialize_components(self):
        """初始化所有组件"""
        try:
            # 初始化数据采集器
            if hasattr(self.trading_settings, 'qmt_enabled') and self.trading_settings.qmt_enabled:
                # TODO: 使用真实QMT采集器
                self.data_collector = MockDataCollector()  # 暂时使用模拟采集器
            else:
                self.data_collector = MockDataCollector()
            
            # 初始化数据处理器
            self.market_data_processor = MarketDataProcessor()
            self.tick_data_processor = TickDataProcessor()
            
            # 初始化技术指标计算器
            self.technical_indicators = {
                "RSI": RSICalculator(period=14),
                "MACD": MACDCalculator(fast_period=12, slow_period=26, signal_period=9),
                "MA_5": MACalculator(period=5, ma_type="SMA"),
                "MA_20": MACalculator(period=20, ma_type="SMA"),
                "EMA_12": MACalculator(period=12, ma_type="EMA"),
                "BOLLINGER": BollingerBandsCalculator(period=20, std_dev=2.0),
                "VOLUME": VolumeIndicatorCalculator(period=20)
            }
            
            # 初始化数据分发器
            self.websocket_distributor = WebSocketDistributor()
            self.ai_trader_distributor = AITraderDistributor()
            
            # 初始化数据存储
            self.database_storage = DatabaseStorage()
            self.cache_storage = CacheStorage()
            
            logger.info("数据管道组件初始化完成")
            
        except Exception as e:
            logger.error(f"组件初始化失败: {e}")
            raise
    
    async def _start_pipeline_tasks(self):
        """启动管道任务"""
        # 启动数据采集器
        await self.data_collector.start()
        
        # 订阅数据采集器的数据
        self.data_collector.subscribe(self._handle_raw_data)
        
        # 启动定时任务
        self.pipeline_tasks = [
            asyncio.create_task(self._data_processing_loop()),
            asyncio.create_task(self._indicator_calculation_loop()),
            asyncio.create_task(self._storage_flush_loop()),
            asyncio.create_task(self._health_check_loop())
        ]
        
        logger.info("数据管道任务启动完成")
    
    async def _cleanup_components(self):
        """清理组件"""
        if self.data_collector:
            await self.data_collector.stop()
        
        if self.database_storage:
            await self.database_storage.force_flush()
        
        logger.info("数据管道组件清理完成")
    
    async def _handle_raw_data(self, raw_data: Any):
        """处理原始数据"""
        try:
            if isinstance(raw_data, MarketData):
                # 处理市场数据
                processed_data = await self.market_data_processor.process(raw_data)
                if processed_data:
                    await self._distribute_market_data(processed_data)
                    await self._store_market_data(processed_data)
                    await self._calculate_indicators(processed_data)
                    
                    self.processed_count += 1
            
        except Exception as e:
            logger.error(f"处理原始数据失败: {e}")
            self.error_count += 1
    
    async def _distribute_market_data(self, market_data: MarketData):
        """分发市场数据"""
        try:
            # 分发给WebSocket客户端
            if self.websocket_distributor:
                await self.websocket_distributor.distribute(market_data)
            
            # 分发给AI交易员
            if self.ai_trader_distributor:
                await self.ai_trader_distributor.distribute(market_data)
                
        except Exception as e:
            logger.error(f"分发市场数据失败: {e}")
    
    async def _store_market_data(self, market_data: MarketData):
        """存储市场数据"""
        try:
            # 存储到数据库
            if self.database_storage:
                await self.database_storage.store_market_data(market_data)
            
            # 存储到缓存
            if self.cache_storage:
                await self.cache_storage.store_market_data(market_data)
                
        except Exception as e:
            logger.error(f"存储市场数据失败: {e}")
    
    async def _calculate_indicators(self, market_data: MarketData):
        """计算技术指标"""
        try:
            for indicator_name, calculator in self.technical_indicators.items():
                indicator = await calculator.calculate(market_data)
                if indicator:
                    await self._distribute_indicator(indicator)
                    await self._store_indicator(indicator)
                    
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
    
    async def _distribute_indicator(self, indicator: TechnicalIndicator):
        """分发技术指标"""
        try:
            # 分发给WebSocket客户端
            if self.websocket_distributor:
                await self.websocket_distributor.distribute(indicator)
            
            # 分发给AI交易员
            if self.ai_trader_distributor:
                await self.ai_trader_distributor.distribute(indicator)
                
        except Exception as e:
            logger.error(f"分发技术指标失败: {e}")
    
    async def _store_indicator(self, indicator: TechnicalIndicator):
        """存储技术指标"""
        try:
            # 存储到数据库
            if self.database_storage:
                await self.database_storage.store_technical_indicator(indicator)
            
            # 存储到缓存
            if self.cache_storage:
                await self.cache_storage.store_technical_indicator(indicator)
                
        except Exception as e:
            logger.error(f"存储技术指标失败: {e}")
    
    async def _data_processing_loop(self):
        """数据处理循环"""
        while self.is_running:
            try:
                # 这里可以添加额外的数据处理逻辑
                await asyncio.sleep(self.data_collection_interval)
                
            except Exception as e:
                logger.error(f"数据处理循环错误: {e}")
                await asyncio.sleep(5)
    
    async def _indicator_calculation_loop(self):
        """技术指标计算循环"""
        while self.is_running:
            try:
                # 定期触发技术指标计算
                await asyncio.sleep(self.indicator_calculation_interval)
                
            except Exception as e:
                logger.error(f"技术指标计算循环错误: {e}")
                await asyncio.sleep(5)
    
    async def _storage_flush_loop(self):
        """存储刷新循环"""
        while self.is_running:
            try:
                # 定期刷新存储
                if self.database_storage:
                    await self.database_storage.force_flush()
                
                await asyncio.sleep(self.storage_flush_interval)
                
            except Exception as e:
                logger.error(f"存储刷新循环错误: {e}")
                await asyncio.sleep(10)
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while self.is_running:
            try:
                # 检查各组件健康状态
                stats = self.get_statistics()
                logger.debug(f"数据管道状态: {stats}")
                
                await asyncio.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"健康检查循环错误: {e}")
                await asyncio.sleep(60)
    
    async def add_websocket_connection(self, connection_id: str, websocket, metadata: Dict[str, Any] = None):
        """添加WebSocket连接"""
        if self.websocket_distributor:
            await self.websocket_distributor.add_websocket_connection(connection_id, websocket, metadata)
    
    async def remove_websocket_connection(self, connection_id: str):
        """移除WebSocket连接"""
        if self.websocket_distributor:
            await self.websocket_distributor.remove_websocket_connection(connection_id)
    
    async def subscribe_ai_trader(self, trader_id: str, callback):
        """AI交易员订阅数据"""
        if self.ai_trader_distributor:
            await self.ai_trader_distributor.subscribe(trader_id, callback)
    
    async def unsubscribe_ai_trader(self, trader_id: str):
        """AI交易员取消订阅"""
        if self.ai_trader_distributor:
            await self.ai_trader_distributor.unsubscribe(trader_id)


# 全局数据管道实例
_global_pipeline: Optional[RealTimeDataPipeline] = None


@asynccontextmanager
async def get_data_pipeline(trading_settings: TradingSettings):
    """获取数据管道实例（上下文管理器）"""
    global _global_pipeline
    
    if _global_pipeline is None:
        _global_pipeline = RealTimeDataPipeline(trading_settings)
        await _global_pipeline.start()
    
    try:
        yield _global_pipeline
    finally:
        # 在应用关闭时清理
        pass


async def get_global_pipeline() -> Optional[RealTimeDataPipeline]:
    """获取全局数据管道实例，如果不存在则创建"""
    global _global_pipeline

    if _global_pipeline is None:
        # 使用默认配置创建管道实例
        from app.core.settings import settings
        _global_pipeline = RealTimeDataPipeline(settings.trading)
        await _global_pipeline.start()

    return _global_pipeline


async def shutdown_global_pipeline():
    """关闭全局数据管道"""
    global _global_pipeline
    if _global_pipeline:
        await _global_pipeline.stop()
        _global_pipeline = None
