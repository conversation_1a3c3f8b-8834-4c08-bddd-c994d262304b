# 海天AI纳斯达克交易系统 - 模块化设计规范

## 文档信息
- **创建日期**: 2025年7月13日
- **最后更新**: 2025年7月13日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: FastAPI项目模块化设计和代码组织规范
- **技术基础**: FastAPI 0.116.1 + SQLModel + Pydantic

## 版本更新记录
| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 1.0.0 | 2025-07-13 | 初始版本，完整模块化设计规范 | 海天AI开发团队 |

## 目录
1. [FastAPI项目代码组织规范](#1-fastapi项目代码组织规范)
2. [分层架构设计标准](#2-分层架构设计标准)
3. [模块接口设计标准](#3-模块接口设计标准)
4. [依赖注入使用规范](#4-依赖注入使用规范)
5. [共享组件管理规则](#5-共享组件管理规则)
6. [命名约定和代码风格](#6-命名约定和代码风格)

## 1. FastAPI项目代码组织规范

### 1.1 项目结构组织原则

**单一职责原则**
- 每个模块只负责一个明确的业务领域
- 模块内部高内聚，模块间低耦合
- 避免模块功能重叠和职责混乱

**分层架构原则**
- 严格按照API、业务、数据三层架构组织
- 上层可以调用下层，下层不能调用上层
- 同层之间通过明确的接口通信

**领域驱动原则**
- 按业务领域划分模块边界
- 每个领域模块包含完整的业务逻辑
- 领域模型与技术实现分离

### 1.2 模块划分标准

**核心业务模块**
```
ai-traders/          # AI交易员领域
├── service.py       # 业务逻辑服务
├── models.py        # 领域数据模型
├── schemas.py       # API数据模型
├── dependencies.py  # 依赖注入定义
└── utils.py         # 领域工具函数
```

**支撑服务模块**
```
trading-engine/      # 交易执行引擎
supervisor/          # AI交易总监
shared/              # 共享组件
core/                # 核心配置
```

**数据访问模块**
```
models/              # 数据库模型
schemas/             # API数据模型
crud/                # 数据访问对象
```

### 1.3 文件命名规范

**模块文件命名**
- `service.py` - 业务逻辑服务
- `models.py` - 数据模型定义
- `schemas.py` - API数据验证模型
- `dependencies.py` - 依赖注入定义
- `utils.py` - 工具函数集合
- `exceptions.py` - 自定义异常
- `constants.py` - 模块常量

**API文件命名**
- `auth.py` - 认证相关端点
- `traders.py` - 交易员管理端点
- `trading.py` - 交易操作端点
- `monitoring.py` - 监控相关端点

## 2. 分层架构设计标准

### 2.1 API层设计标准

**路由组织结构**
```python
# backend/app/api/v1/endpoints/traders.py
from fastapi import APIRouter, Depends
from app.ai_traders.service import AITraderService
from app.ai_traders.schemas import AITraderCreate, AITraderResponse

router = APIRouter()

@router.post("/", response_model=AITraderResponse)
def create_trader(
    trader_data: AITraderCreate,
    service: AITraderService = Depends()
):
    """创建AI交易员"""
    return service.create_trader(trader_data)
```

**API层职责**
- HTTP请求路由和参数解析
- 请求数据验证和响应格式化
- 认证授权和权限检查
- 异常处理和错误响应

**API层禁止事项**
- 不包含业务逻辑处理
- 不直接操作数据库
- 不处理复杂的数据转换
- 不包含算法和计算逻辑

### 2.2 业务层设计标准

**服务类设计模式**
```python
# backend/app/ai_traders/service.py
from typing import List, Optional
from app.ai_traders.schemas import AITraderCreate, AITraderUpdate
from app.ai_traders.models import AITrader
from app.crud.trader import trader_crud

class AITraderService:
    """AI交易员业务服务"""
    
    def __init__(self, db: Session = Depends(get_db)):
        self.db = db
    
    def create_trader(self, trader_data: AITraderCreate) -> AITrader:
        """创建AI交易员"""
        # 业务逻辑验证
        self._validate_trader_data(trader_data)
        
        # 创建交易员
        trader = trader_crud.create(self.db, obj_in=trader_data)
        
        # 初始化交易员配置
        self._initialize_trader_config(trader)
        
        return trader
    
    def _validate_trader_data(self, data: AITraderCreate) -> None:
        """验证交易员数据"""
        # 业务规则验证逻辑
        pass
```

**业务层设计原则**
- 封装核心业务逻辑和规则
- 协调多个数据访问对象
- 处理业务异常和错误
- 实现事务管理和一致性

### 2.3 数据层设计标准

**模型定义规范**
```python
# backend/app/models/trader.py
from sqlmodel import SQLModel, Field
from typing import Optional
from datetime import datetime

class AITraderBase(SQLModel):
    """AI交易员基础模型"""
    name: str = Field(max_length=100, description="交易员名称")
    model_type: str = Field(max_length=50, description="AI模型类型")
    fund_amount: float = Field(gt=0, description="分配资金")
    is_active: bool = Field(default=True, description="是否激活")

class AITrader(AITraderBase, table=True):
    """AI交易员数据库模型"""
    id: Optional[int] = Field(default=None, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: Optional[datetime] = None
```

**CRUD操作规范**
```python
# backend/app/crud/trader.py
from app.crud.base import CRUDBase
from app.models.trader import AITrader
from app.schemas.trader import AITraderCreate, AITraderUpdate

class CRUDTrader(CRUDBase[AITrader, AITraderCreate, AITraderUpdate]):
    """AI交易员CRUD操作"""
    
    def get_active_traders(self, db: Session) -> List[AITrader]:
        """获取活跃的交易员"""
        return db.query(AITrader).filter(AITrader.is_active == True).all()

trader_crud = CRUDTrader(AITrader)
```

## 3. 模块接口设计标准

### 3.1 接口定义规范

**服务接口抽象**
```python
# backend/app/ai_traders/interfaces.py
from abc import ABC, abstractmethod
from typing import List, Optional

class AITraderServiceInterface(ABC):
    """AI交易员服务接口"""
    
    @abstractmethod
    def create_trader(self, trader_data: AITraderCreate) -> AITrader:
        """创建AI交易员"""
        pass
    
    @abstractmethod
    def get_trader_decisions(self, trader_id: int) -> List[TradingDecision]:
        """获取交易员决策"""
        pass
```

**接口实现规范**
```python
# backend/app/ai_traders/service.py
class AITraderService(AITraderServiceInterface):
    """AI交易员服务实现"""
    
    def create_trader(self, trader_data: AITraderCreate) -> AITrader:
        # 具体实现
        pass
```

### 3.2 模块间通信规范

**事件驱动通信**
```python
# backend/app/shared/events.py
from dataclasses import dataclass
from typing import Any

@dataclass
class TradingDecisionEvent:
    """交易决策事件"""
    trader_id: int
    decision_type: str
    symbol: str
    quantity: int
    price: float
    timestamp: datetime

class EventBus:
    """事件总线"""
    
    def publish(self, event: Any) -> None:
        """发布事件"""
        pass
    
    def subscribe(self, event_type: type, handler: callable) -> None:
        """订阅事件"""
        pass
```

**依赖注入通信**
```python
# 通过依赖注入实现模块间通信
def get_trading_engine() -> TradingEngineService:
    return TradingEngineService()

@router.post("/execute-trade")
def execute_trade(
    trade_data: TradeRequest,
    engine: TradingEngineService = Depends(get_trading_engine)
):
    return engine.execute_trade(trade_data)
```

## 4. 依赖注入使用规范

### 4.1 依赖定义规范

**数据库依赖**
```python
# backend/app/api/deps.py
from sqlmodel import Session
from app.core.database import SessionLocal

def get_db() -> Session:
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
```

**服务依赖**
```python
# backend/app/ai_traders/dependencies.py
from app.ai_traders.service import AITraderService

def get_trader_service(
    db: Session = Depends(get_db)
) -> AITraderService:
    """获取AI交易员服务"""
    return AITraderService(db)
```

**认证依赖**
```python
# backend/app/core/security.py
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer

security = HTTPBearer()

def get_current_user(
    token: str = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前用户"""
    # 验证token并返回用户
    pass
```

### 4.2 依赖使用规范

**路由中使用依赖**
```python
@router.get("/traders/", response_model=List[AITraderResponse])
def get_traders(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    service: AITraderService = Depends(get_trader_service)
):
    """获取交易员列表"""
    return service.get_traders(skip=skip, limit=limit)
```

**嵌套依赖使用**
```python
def get_trader_with_permissions(
    trader_id: int,
    current_user: User = Depends(get_current_user),
    service: AITraderService = Depends(get_trader_service)
) -> AITrader:
    """获取有权限的交易员"""
    trader = service.get_trader(trader_id)
    if not service.check_user_permission(current_user, trader):
        raise HTTPException(status_code=403, detail="权限不足")
    return trader
```

## 5. 共享组件管理规则

### 5.1 共享组件分类

**工具函数类**
```python
# backend/app/shared/utils.py
from typing import Any, Dict
import json

def serialize_datetime(obj: Any) -> str:
    """序列化日期时间"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

def format_currency(amount: float, currency: str = "USD") -> str:
    """格式化货币"""
    return f"{currency} {amount:,.2f}"
```

**常量定义类**
```python
# backend/app/shared/constants.py
from enum import Enum

class TradingAction(str, Enum):
    """交易动作枚举"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"

class AIModelType(str, Enum):
    """AI模型类型枚举"""
    GPT4 = "gpt-4"
    CLAUDE = "claude"
    GEMINI = "gemini"

# 全局常量
MAX_TRADERS_COUNT = 10
DEFAULT_FUND_AMOUNT = 10000.0
TRADING_HOURS_START = "09:30"
TRADING_HOURS_END = "16:00"
```

**验证器类**
```python
# backend/app/shared/validators.py
from pydantic import validator
import re

def validate_stock_symbol(symbol: str) -> str:
    """验证股票代码"""
    if not re.match(r'^[A-Z]{1,5}$', symbol):
        raise ValueError('股票代码格式无效')
    return symbol.upper()

def validate_trading_amount(amount: float) -> float:
    """验证交易金额"""
    if amount <= 0:
        raise ValueError('交易金额必须大于0')
    if amount > 1000000:
        raise ValueError('交易金额不能超过100万')
    return amount
```

### 5.2 共享组件使用规范

**导入规范**
```python
# 推荐的导入方式
from app.shared.constants import TradingAction, AIModelType
from app.shared.utils import serialize_datetime, format_currency
from app.shared.validators import validate_stock_symbol

# 避免的导入方式
from app.shared import *  # 避免通配符导入
```

**版本兼容性**
- 共享组件接口保持向后兼容
- 新增功能通过可选参数实现
- 废弃功能提供迁移指导
- 重大变更需要版本号升级

## 6. 命名约定和代码风格

### 6.1 命名约定规范

**文件和目录命名**
- 使用小写字母和下划线：`ai_traders.py`
- 目录名使用连字符：`ai-traders/`
- 测试文件前缀：`test_ai_traders.py`
- 配置文件后缀：`config.py`

**类命名**
- 使用帕斯卡命名法：`AITraderService`
- 模型类后缀：`AITrader`、`TradingRecord`
- 服务类后缀：`AITraderService`
- 异常类后缀：`AITraderException`

**函数和变量命名**
- 使用蛇形命名法：`create_trader`、`trader_id`
- 布尔变量前缀：`is_active`、`has_permission`
- 私有方法前缀：`_validate_data`
- 常量全大写：`MAX_TRADERS_COUNT`

**API端点命名**
- 使用RESTful风格：`GET /api/v1/traders/`
- 资源名使用复数：`/traders/`、`/trades/`
- 动作使用HTTP方法：`POST`、`PUT`、`DELETE`
- 查询参数使用蛇形：`?skip=0&limit=100`

### 6.2 代码风格规范

**Python代码风格**
- 遵循PEP 8标准
- 使用Black格式化工具
- 行长度限制为88字符
- 使用类型注解

**文档字符串规范**
```python
def create_trader(self, trader_data: AITraderCreate) -> AITrader:
    """
    创建AI交易员
    
    Args:
        trader_data: 交易员创建数据
        
    Returns:
        AITrader: 创建的交易员对象
        
    Raises:
        ValidationError: 数据验证失败
        DatabaseError: 数据库操作失败
    """
    pass
```

**注释规范**
```python
# 业务逻辑注释
def calculate_position_size(self, fund_amount: float, risk_level: float) -> int:
    # 根据资金量和风险等级计算仓位大小
    # 使用凯利公式：f = (bp - q) / b
    # 其中 f=仓位比例, b=赔率, p=胜率, q=败率
    
    base_position = fund_amount * 0.1  # 基础仓位10%
    risk_adjustment = base_position * risk_level  # 风险调整
    
    return int(base_position + risk_adjustment)
```

### 6.3 代码质量标准

**代码复杂度控制**
- 函数行数不超过50行
- 圈复杂度不超过10
- 嵌套层级不超过4层
- 参数个数不超过5个

**测试覆盖率要求**
- 单元测试覆盖率 ≥ 80%
- 核心业务逻辑覆盖率 ≥ 95%
- 集成测试覆盖主要流程
- 端到端测试覆盖关键场景

**性能要求**
- API响应时间 < 200ms
- 数据库查询优化
- 内存使用合理
- 并发处理能力

---

*本规范确保项目代码的一致性、可维护性和可扩展性，为团队协作开发提供统一标准。*
