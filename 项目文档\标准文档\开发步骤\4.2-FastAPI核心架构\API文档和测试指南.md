# 海天AI纳斯达克交易系统 - API文档和测试指南

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025年7月31日
- **技术栈**: FastAPI 0.116.1 + Python 3.13.2
- **开发步骤**: 4.2 FastAPI核心架构

## 1. API文档概述

### 1.1 API基础信息
- **基础URL**: `http://localhost:8001/api/v1`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8
- **API版本**: v1.0

### 1.2 通用响应格式
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "meta": {
    "timestamp": "2025-07-31T16:00:00Z",
    "request_id": "req-uuid",
    "version": "v1.0"
  }
}
```

### 1.3 错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "field": "quantity",
      "issue": "必须大于0"
    },
    "timestamp": "2025-07-31T16:00:00Z"
  },
  "data": null,
  "meta": {
    "status_code": 400,
    "request_id": "req-uuid"
  }
}
```

## 2. 认证API文档

### 2.1 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password123"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 1800
  }
}
```

### 2.2 刷新令牌
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 2.3 获取用户信息
```http
GET /api/v1/auth/me
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "is_active": true,
    "scopes": ["read", "write", "admin"],
    "created_at": "2025-07-31T10:00:00Z"
  }
}
```

## 3. 交易员管理API文档

### 3.1 获取交易员列表
```http
GET /api/v1/traders?skip=0&limit=10&is_active=true
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "保守型AI交易员",
      "ai_model": "conservative_v1",
      "risk_level": "low",
      "max_position_size": 10000,
      "stop_loss_pct": 2.0,
      "take_profit_pct": 5.0,
      "is_active": true,
      "created_at": "2025-07-31T10:00:00Z"
    }
  ],
  "meta": {
    "total": 5,
    "skip": 0,
    "limit": 10
  }
}
```

### 3.2 创建交易员
```http
POST /api/v1/traders
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "新AI交易员",
  "ai_model": "aggressive_v1",
  "risk_level": "high",
  "max_position_size": 20000,
  "stop_loss_pct": 3.0,
  "take_profit_pct": 8.0
}
```

### 3.3 启动/停止交易员
```http
POST /api/v1/traders/1/start
Authorization: Bearer <token>

POST /api/v1/traders/1/stop
Authorization: Bearer <token>
```

## 4. 交易执行API文档

### 4.1 获取持仓信息
```http
GET /api/v1/trading/positions?trader_id=1
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "symbol": "159509",
      "quantity": 1000,
      "avg_cost": 1.25,
      "current_price": 1.32,
      "market_value": 1320.0,
      "unrealized_pnl": 70.0,
      "unrealized_pnl_pct": 5.6,
      "trader_id": 1,
      "last_updated": "2025-07-31T15:30:00Z"
    }
  ]
}
```

### 4.2 手动下单
```http
POST /api/v1/trading/orders
Authorization: Bearer <token>
Content-Type: application/json

{
  "symbol": "159509",
  "side": "buy",
  "order_type": "market",
  "quantity": 1000,
  "trader_id": 1
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 9999,
    "symbol": "159509",
    "side": "buy",
    "order_type": "market",
    "quantity": 1000,
    "status": "pending",
    "trader_id": 1,
    "created_at": "2025-07-31T16:00:00Z"
  }
}
```

### 4.3 获取市场数据
```http
GET /api/v1/trading/market-data?symbol=159509
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "symbol": "159509",
    "current_price": 1.32,
    "open_price": 1.30,
    "high_price": 1.35,
    "low_price": 1.28,
    "volume": 1250000,
    "change": 0.02,
    "change_pct": 1.54,
    "timestamp": "2025-07-31T16:00:00Z"
  }
}
```

## 5. 监控统计API文档

### 5.1 系统状态
```http
GET /api/v1/monitoring/system-status
Authorization: Bearer <token>
```

### 5.2 性能指标
```http
GET /api/v1/monitoring/performance-metrics
Authorization: Bearer <token>
```

### 5.3 交易统计
```http
GET /api/v1/monitoring/trading-statistics?date_filter=2025-07-31
Authorization: Bearer <token>
```

## 6. 测试指南

### 6.1 环境准备

#### 6.1.1 安装依赖
```bash
# 进入后端目录
cd backend

# 激活conda环境
conda activate AI_Nasdaq_Trading

# 安装测试依赖
pip install pytest pytest-asyncio httpx
```

#### 6.1.2 环境变量设置
```bash
# 设置测试环境变量
export ENVIRONMENT=testing
export DATABASE_URL=postgresql://test:test@localhost:5432/test_ai_trading
export SECRET_KEY=test-secret-key
```

### 6.2 单元测试

#### 6.2.1 创建测试文件结构
```
backend/tests/
├── __init__.py
├── conftest.py              # 测试配置
├── test_auth.py            # 认证测试
├── test_traders.py         # 交易员测试
├── test_trading.py         # 交易测试
└── test_monitoring.py      # 监控测试
```

#### 6.2.2 测试配置 (`conftest.py`)
```python
import pytest
from fastapi.testclient import TestClient
from app.main import app

@pytest.fixture
def client():
    return TestClient(app)

@pytest.fixture
def auth_headers(client):
    # 获取测试用户令牌
    response = client.post("/api/v1/auth/login", json={
        "username": "test_user",
        "password": "test_password"
    })
    token = response.json()["data"]["access_token"]
    return {"Authorization": f"Bearer {token}"}
```

#### 6.2.3 认证测试示例 (`test_auth.py`)
```python
def test_login_success(client):
    response = client.post("/api/v1/auth/login", json={
        "username": "admin",
        "password": "password123"
    })
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "access_token" in data["data"]
    assert "refresh_token" in data["data"]

def test_login_invalid_credentials(client):
    response = client.post("/api/v1/auth/login", json={
        "username": "admin",
        "password": "wrong_password"
    })
    assert response.status_code == 401
    data = response.json()
    assert data["success"] is False

def test_get_current_user(client, auth_headers):
    response = client.get("/api/v1/auth/me", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "username" in data["data"]
```

#### 6.2.4 交易员测试示例 (`test_traders.py`)
```python
def test_get_traders(client, auth_headers):
    response = client.get("/api/v1/traders", headers=auth_headers)
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert isinstance(data["data"], list)

def test_create_trader(client, auth_headers):
    trader_data = {
        "name": "测试交易员",
        "ai_model": "test_model",
        "risk_level": "medium",
        "max_position_size": 15000,
        "stop_loss_pct": 2.5,
        "take_profit_pct": 6.0
    }
    response = client.post("/api/v1/traders", 
                          json=trader_data, 
                          headers=auth_headers)
    assert response.status_code == 201
    data = response.json()
    assert data["success"] is True
    assert data["data"]["name"] == trader_data["name"]
```

### 6.3 集成测试

#### 6.3.1 API集成测试
```python
def test_trading_workflow(client, auth_headers):
    # 1. 创建交易员
    trader_response = client.post("/api/v1/traders", 
                                 json={"name": "集成测试交易员", ...},
                                 headers=auth_headers)
    trader_id = trader_response.json()["data"]["id"]
    
    # 2. 启动交易员
    start_response = client.post(f"/api/v1/traders/{trader_id}/start",
                                headers=auth_headers)
    assert start_response.status_code == 200
    
    # 3. 下单
    order_response = client.post("/api/v1/trading/orders",
                                json={"symbol": "159509", "side": "buy", ...},
                                headers=auth_headers)
    assert order_response.status_code == 201
    
    # 4. 检查持仓
    positions_response = client.get("/api/v1/trading/positions",
                                   headers=auth_headers)
    assert positions_response.status_code == 200
```

### 6.4 性能测试

#### 6.4.1 负载测试
```python
import asyncio
import aiohttp
import time

async def load_test():
    async with aiohttp.ClientSession() as session:
        tasks = []
        for i in range(100):  # 100个并发请求
            task = session.get("http://localhost:8001/api/v1/monitoring/system-status")
            tasks.append(task)
        
        start_time = time.time()
        responses = await asyncio.gather(*tasks)
        end_time = time.time()
        
        print(f"100个请求耗时: {end_time - start_time:.2f}秒")
        print(f"平均响应时间: {(end_time - start_time) / 100:.4f}秒")

# 运行负载测试
asyncio.run(load_test())
```

### 6.5 测试执行

#### 6.5.1 运行所有测试
```bash
# 运行所有测试
pytest tests/ -v

# 运行特定测试文件
pytest tests/test_auth.py -v

# 运行特定测试函数
pytest tests/test_auth.py::test_login_success -v

# 生成覆盖率报告
pytest tests/ --cov=app --cov-report=html
```

#### 6.5.2 测试报告
```bash
# 生成详细测试报告
pytest tests/ --html=reports/report.html --self-contained-html

# 生成JUnit格式报告
pytest tests/ --junitxml=reports/junit.xml
```

## 7. API调试工具

### 7.1 Swagger UI
- 访问地址: `http://localhost:8001/docs`
- 功能: 交互式API文档和测试界面
- 支持: 在线测试所有API端点

### 7.2 ReDoc
- 访问地址: `http://localhost:8001/redoc`
- 功能: 美观的API文档展示
- 特点: 更适合文档阅读

### 7.3 Postman集合
```json
{
  "info": {
    "name": "海天AI纳斯达克交易系统API",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "认证",
      "item": [
        {
          "name": "用户登录",
          "request": {
            "method": "POST",
            "header": [{"key": "Content-Type", "value": "application/json"}],
            "body": {
              "mode": "raw",
              "raw": "{\"username\":\"admin\",\"password\":\"password123\"}"
            },
            "url": {
              "raw": "{{base_url}}/api/v1/auth/login",
              "host": ["{{base_url}}"],
              "path": ["api", "v1", "auth", "login"]
            }
          }
        }
      ]
    }
  ],
  "variable": [
    {
      "key": "base_url",
      "value": "http://localhost:8001"
    }
  ]
}
```

## 8. 监控和日志

### 8.1 API监控
- 请求响应时间监控
- 错误率统计
- 并发请求数监控
- 慢查询告警

### 8.2 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 查看访问日志
tail -f logs/access.log
```

## 9. 故障排查

### 9.1 常见问题
1. **认证失败**: 检查JWT令牌是否过期
2. **权限不足**: 确认用户具有所需权限范围
3. **参数验证失败**: 检查请求参数格式和类型
4. **数据库连接失败**: 检查数据库服务状态

### 9.2 调试技巧
- 使用请求ID追踪问题
- 查看详细错误信息
- 检查中间件日志
- 验证配置参数

## 10. 总结

本测试指南提供了：
- ✅ 完整的API文档和示例
- ✅ 系统化的测试策略
- ✅ 单元测试和集成测试框架
- ✅ 性能测试方法
- ✅ 调试工具和监控方案
- ✅ 故障排查指南

确保API的质量和可靠性，为系统的稳定运行提供保障。
