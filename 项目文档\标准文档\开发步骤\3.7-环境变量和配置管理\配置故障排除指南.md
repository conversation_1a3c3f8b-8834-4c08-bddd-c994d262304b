# 配置故障排除指南

## 文档信息
- **创建日期**: 2025年7月13日
- **最后更新**: 2025年7月13日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: AI交易系统配置问题诊断和解决
- **技术基础**: 配置管理系统故障排除

*本指南提供AI交易系统配置相关问题的诊断方法和解决方案。*

---

## 常见配置问题

### 1. 环境变量问题

#### 问题：环境变量未生效
**症状**：
- 应用使用默认值而非环境变量值
- 配置验证显示变量缺失
- 功能异常或连接失败

**诊断步骤**：
```bash
# 1. 检查环境变量是否设置
echo $APP_DB_PASSWORD
echo $SECRET_KEY

# 2. 检查.env文件位置和内容
cat infrastructure/.env

# 3. 验证环境变量加载
python scripts/config_manager.py check-env
```

**解决方案**：
```bash
# 1. 确认.env文件位置正确
ls -la infrastructure/.env

# 2. 检查文件权限
chmod 600 infrastructure/.env

# 3. 重新加载环境变量
source infrastructure/.env

# 4. 重启应用服务
docker-compose restart backend
```

#### 问题：环境变量格式错误
**症状**：
- 配置验证失败
- 类型转换错误
- 应用启动异常

**诊断方法**：
```python
# 检查环境变量类型
import os
print(f"PORT: {os.getenv('PORT')} (type: {type(os.getenv('PORT'))})")
print(f"DEBUG: {os.getenv('DEBUG')} (type: {type(os.getenv('DEBUG'))})")
```

**解决方案**：
```bash
# 正确的环境变量格式
PORT=8000                    # 数字
DEBUG=true                   # 布尔值
CORS_ORIGINS=["http://localhost:3000"]  # JSON数组
```

### 2. 数据库连接问题

#### 问题：数据库连接失败
**症状**：
- "connection refused" 错误
- "authentication failed" 错误
- 应用无法启动

**诊断步骤**：
```bash
# 1. 检查数据库服务状态
docker-compose ps db

# 2. 测试数据库连接
python scripts/config_manager.py test-connections --service database

# 3. 检查数据库日志
docker-compose logs db

# 4. 手动连接测试
psql -h localhost -p 5432 -U postgres -d ai_trading
```

**解决方案**：
```bash
# 1. 启动数据库服务
docker-compose up -d db

# 2. 检查数据库配置
cat infrastructure/.env | grep POSTGRES

# 3. 重置数据库密码
docker-compose exec db psql -U postgres -c "ALTER USER postgres PASSWORD 'new_password';"

# 4. 更新环境变量
echo "APP_DB_PASSWORD=new_password" >> infrastructure/.env
```

#### 问题：数据库权限不足
**症状**：
- "permission denied" 错误
- 无法创建表或执行查询
- 连接成功但操作失败

**解决方案**：
```sql
-- 创建应用用户并授权
CREATE USER ai_trading_app WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE ai_trading TO ai_trading_app;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ai_trading_app;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ai_trading_app;
```

### 3. AI模型配置问题

#### 问题：AI模型API密钥无效
**症状**：
- 401 Unauthorized 错误
- API调用失败
- AI功能无法使用

**诊断步骤**：
```bash
# 1. 检查API密钥配置
python scripts/config_manager.py check-env | grep API_KEY

# 2. 测试API连接
curl -H "Authorization: Bearer $AI_MODEL_API_KEY" \
     https://api.openai.com/v1/models
```

**解决方案**：
```bash
# 1. 获取新的API密钥
# 访问 https://platform.openai.com/api-keys

# 2. 更新环境变量
echo "AI_MODEL_API_KEY=sk-new-api-key-here" >> infrastructure/.env

# 3. 重启服务
docker-compose restart backend
```

#### 问题：API配额超限
**症状**：
- 429 Too Many Requests 错误
- API调用被限制
- 服务间歇性失败

**解决方案**：
```python
# 1. 检查API使用情况
# 登录API提供商控制台查看使用量

# 2. 调整并发限制
AI_MAX_CONCURRENT=5  # 降低并发数
AI_RETRY_DELAY=2.0   # 增加重试延迟

# 3. 实施请求缓存
AI_CACHE_ENABLED=true
AI_CACHE_TTL=300
```

### 4. QMT接口问题

#### 问题：QMT连接失败
**症状**：
- 无法连接到QMT服务
- 交易功能不可用
- 认证失败

**诊断步骤**：
```bash
# 1. 检查QMT服务状态
telnet localhost 58610

# 2. 验证QMT配置
python scripts/config_manager.py check-env | grep QMT

# 3. 检查网络连接
ping qmt-server-address
```

**解决方案**：
```bash
# 1. 确认QMT服务运行
# 启动QMT客户端软件

# 2. 检查防火墙设置
# 确保端口58610开放

# 3. 更新QMT配置
QMT_HOST=correct-host-address
QMT_PORT=58610
QMT_USERNAME=your-username
QMT_PASSWORD=your-password
```

### 5. Redis连接问题

#### 问题：Redis连接失败
**症状**：
- 缓存功能不可用
- 会话管理失败
- 性能下降

**诊断步骤**：
```bash
# 1. 检查Redis服务
docker-compose ps redis

# 2. 测试Redis连接
python scripts/config_manager.py test-connections --service redis

# 3. 手动连接测试
redis-cli -h localhost -p 6379 ping
```

**解决方案**：
```bash
# 1. 启动Redis服务
docker-compose up -d redis

# 2. 检查Redis配置
docker-compose logs redis

# 3. 清理Redis数据（如果需要）
redis-cli FLUSHALL
```

## 配置文件问题

### 1. YAML文件语法错误

#### 问题：YAML解析失败
**症状**：
- 配置加载失败
- 语法错误提示
- 应用启动异常

**诊断方法**：
```bash
# 1. 验证YAML语法
python -c "import yaml; yaml.safe_load(open('config/default.yaml'))"

# 2. 使用在线YAML验证器
# 复制文件内容到 https://yamlchecker.com/
```

**解决方案**：
```bash
# 1. 检查缩进（使用空格，不使用Tab）
# 2. 检查引号匹配
# 3. 检查特殊字符转义
# 4. 验证数据类型格式
```

### 2. 配置文件权限问题

#### 问题：无法读取配置文件
**症状**：
- "Permission denied" 错误
- 配置加载失败
- 文件访问异常

**解决方案**：
```bash
# 1. 检查文件权限
ls -la config/
ls -la infrastructure/.env

# 2. 设置正确权限
chmod 644 config/*.yaml
chmod 600 infrastructure/.env

# 3. 检查文件所有者
chown app:app config/*.yaml
chown app:app infrastructure/.env
```

## 系统级问题

### 1. 内存不足

#### 问题：配置加载时内存不足
**症状**：
- 应用启动缓慢
- 内存使用率过高
- 系统响应迟缓

**解决方案**：
```bash
# 1. 检查内存使用
free -h
docker stats

# 2. 调整容器内存限制
# 在docker-compose.yml中增加内存限制
services:
  backend:
    mem_limit: 1g
    mem_reservation: 512m

# 3. 优化配置加载
# 延迟加载非关键配置
# 减少配置文件大小
```

### 2. 网络连接问题

#### 问题：外部服务连接超时
**症状**：
- API调用超时
- 数据库连接超时
- 服务间通信失败

**诊断步骤**：
```bash
# 1. 检查网络连接
ping api.openai.com
ping database-host

# 2. 检查DNS解析
nslookup api.openai.com

# 3. 检查防火墙规则
iptables -L
```

**解决方案**：
```bash
# 1. 调整超时设置
AI_TIMEOUT=120
DB_POOL_TIMEOUT=60

# 2. 配置代理（如果需要）
HTTP_PROXY=http://proxy-server:8080
HTTPS_PROXY=http://proxy-server:8080

# 3. 检查网络配置
# 确保容器网络配置正确
```

## 故障排除工具

### 1. 配置诊断脚本

```bash
#!/bin/bash
# config_diagnosis.sh

echo "=== 配置诊断开始 ==="

# 检查环境变量
echo "1. 检查环境变量..."
python scripts/config_manager.py check-env

# 验证配置
echo "2. 验证配置..."
python scripts/config_manager.py validate

# 测试连接
echo "3. 测试连接..."
python scripts/config_manager.py test-connections

# 生成报告
echo "4. 生成诊断报告..."
python scripts/config_manager.py report --output diagnosis_report.md

echo "=== 配置诊断完成 ==="
```

### 2. 日志分析工具

```bash
# 查看配置相关日志
grep -i "config" logs/app.log | tail -50

# 查看错误日志
grep -i "error" logs/app.log | grep -i "config"

# 实时监控日志
tail -f logs/app.log | grep -i "config"
```

### 3. 配置备份和恢复

```bash
# 备份当前配置
mkdir -p backups/$(date +%Y%m%d_%H%M%S)
cp infrastructure/.env backups/$(date +%Y%m%d_%H%M%S)/
cp -r config/ backups/$(date +%Y%m%d_%H%M%S)/

# 恢复配置
cp backups/20250713_120000/.env infrastructure/
cp -r backups/20250713_120000/config/ ./
```

## 预防措施

### 1. 配置监控

```python
# 配置变更监控
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class ConfigMonitor(FileSystemEventHandler):
    def on_modified(self, event):
        if '.env' in event.src_path or '.yaml' in event.src_path:
            print(f"配置文件变更: {event.src_path}")
            # 触发配置验证
            run_config_validation()
```

### 2. 定期检查

```bash
# 添加到crontab
# 每小时检查配置状态
0 * * * * /path/to/config_diagnosis.sh

# 每日生成配置报告
0 9 * * * python /path/to/scripts/config_manager.py report --output daily_config_report.md
```

### 3. 告警机制

```python
def send_config_alert(issue_type, message):
    """发送配置告警"""
    # 发送邮件、短信或其他通知
    # 记录到监控系统
    # 触发自动修复流程（如果可能）
    pass
```

## 紧急恢复流程

### 1. 快速恢复步骤

```bash
# 1. 停止服务
docker-compose down

# 2. 恢复最后已知良好配置
cp config_backup/.env infrastructure/
cp -r config_backup/config/ ./

# 3. 验证配置
python scripts/config_manager.py validate

# 4. 重启服务
docker-compose up -d

# 5. 验证系统状态
python scripts/config_manager.py test-connections
```

### 2. 数据恢复

```bash
# 如果配置错误导致数据问题
# 1. 停止所有服务
docker-compose down

# 2. 恢复数据库备份
docker-compose exec db pg_restore -U postgres -d ai_trading /backup/latest.sql

# 3. 恢复配置
# 按照上述步骤恢复配置

# 4. 重启并验证
docker-compose up -d
```

---

## 联系支持

如果以上方法无法解决问题，请联系技术支持：

- **技术支持邮箱**: <EMAIL>
- **紧急联系电话**: +86-xxx-xxxx-xxxx
- **在线支持**: https://support.company.com

提供以下信息以便快速诊断：
1. 错误信息和日志
2. 配置文件内容（去除敏感信息）
3. 系统环境信息
4. 问题复现步骤

---

## 相关文档

- [配置管理完整指南](./配置管理完整指南.md)
- [环境变量配置检查清单](./环境变量配置检查清单.md)
- [配置验证方法和标准](./配置验证方法和标准.md)
