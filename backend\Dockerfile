# =============================================================================
# 海天AI纳斯达克交易系统 - 后端服务容器配置
# 基于: 项目手册4.1节MVP版本技术栈配置
# 创建日期: 2025年7月13日
# 最后更新: 2025年7月30日 (前端安全漏洞修复后版本同步)
# 后端技术栈: FastAPI 0.116.1 + Python 3.13.2 + PostgreSQL 17.5
# 前端技术栈: Vue.js 3.5.x + TypeScript 5.7.x + Vite 6.0.x + Vitest 3.2.x
# 构建策略: 单阶段构建，简化部署流程
# =============================================================================

FROM python:3.13.2-slim

# 设置环境变量
ARG DEBIAN_FRONTEND=noninteractive
ARG PIP_NO_CACHE_DIR=1
ARG PIP_DISABLE_PIP_VERSION_CHECK=1
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app

# 创建非root用户（安全最佳实践）
RUN groupadd --gid 1000 appgroup \
    && useradd --uid 1000 --gid appgroup --shell /bin/bash --create-home appuser

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    # 编译工具
    gcc \
    g++ \
    make \
    # 系统库
    libpq-dev \
    libffi-dev \
    libssl-dev \
    libpq5 \
    libffi8 \
    libssl3 \
    # 网络工具（健康检查需要）
    curl \
    # 进程管理工具
    procps \
    # 清理缓存
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 升级pip和安装构建工具
RUN pip install --upgrade pip setuptools wheel

# 复制依赖文件并安装Python依赖
COPY requirements.txt pyproject.toml ./
RUN pip install --no-warn-script-location -r requirements.txt

# 创建应用目录结构
RUN mkdir -p /app/data/ai_traders \
    && mkdir -p /app/data/logs \
    && mkdir -p /app/uploads \
    && mkdir -p /app/tests

# 复制应用代码
COPY --chown=appuser:appgroup ./app /app/app
COPY --chown=appuser:appgroup ./tests /app/tests
COPY --chown=appuser:appgroup ./alembic /app/alembic
COPY --chown=appuser:appgroup ./requirements.txt /app/
COPY --chown=appuser:appgroup ./pyproject.toml /app/

# 设置目录权限
RUN chown -R appuser:appgroup /app \
    && chmod -R 755 /app \
    && chmod -R 777 /app/data \
    && chmod -R 777 /app/uploads

# 切换到非root用户
USER appuser

# 健康检查配置
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# 暴露端口
EXPOSE 8001

# 设置默认启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"]

# =============================================================================
# 镜像元数据
# =============================================================================
LABEL maintainer="海天AI量化交易开发团队"
LABEL version="1.0.0"
LABEL description="海天AI纳斯达克交易系统后端服务"
LABEL tech.stack="FastAPI 0.116.1 + Python 3.13.2 + PostgreSQL 17.5"
LABEL frontend.stack="Vue.js 3.5.x + TypeScript 5.7.x + Vite 6.0.x + Vitest 3.2.x"
LABEL build.date="2025-07-13"
LABEL last.update="2025-07-30"
