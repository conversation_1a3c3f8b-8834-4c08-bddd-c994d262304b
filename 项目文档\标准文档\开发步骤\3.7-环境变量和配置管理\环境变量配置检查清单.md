# 环境变量配置检查清单

## 文档信息
- **创建日期**: 2025年7月13日
- **最后更新**: 2025年7月13日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: AI交易系统环境变量配置验证
- **技术基础**: 环境变量管理和配置验证

*本清单用于确保AI交易系统的环境变量配置正确完整。*

---

## 必需环境变量检查

### 数据库配置
- [ ] `POSTGRES_HOST` - PostgreSQL服务器地址
- [ ] `POSTGRES_PORT` - PostgreSQL端口号（默认5432）
- [ ] `POSTGRES_DB` - 数据库名称
- [ ] `POSTGRES_USER` - PostgreSQL管理员用户
- [ ] `POSTGRES_PASSWORD` - PostgreSQL管理员密码
- [ ] `APP_DB_USER` - 应用数据库用户
- [ ] `APP_DB_PASSWORD` - 应用数据库密码

### 安全配置
- [ ] `SECRET_KEY` - JWT签名密钥（至少32字符）
- [ ] `JWT_ALGORITHM` - JWT算法（默认HS256）
- [ ] `ACCESS_TOKEN_EXPIRE_MINUTES` - 访问令牌过期时间

### 交易系统配置
- [ ] `AI_TRADER_COUNT` - AI交易员数量
- [ ] `MAX_USABLE_FUNDS` - 最大可用资金
- [ ] `SINGLE_TRANSACTION_LIMIT` - 单笔交易限额
- [ ] `RECOMMENDED_POSITION_RATE` - 推荐仓位比例

## 可选环境变量检查

### AI模型配置
- [ ] `AI_MODEL_API_KEY` - OpenAI API密钥
- [ ] `AI_MODEL_BASE_URL` - OpenAI API基础URL
- [ ] `AI_MODEL_NAME` - 使用的AI模型名称
- [ ] `ANTHROPIC_API_KEY` - Anthropic API密钥
- [ ] `GOOGLE_API_KEY` - Google API密钥

### QMT交易接口
- [ ] `QMT_HOST` - QMT服务器地址
- [ ] `QMT_PORT` - QMT端口号
- [ ] `QMT_USERNAME` - QMT用户名
- [ ] `QMT_PASSWORD` - QMT密码
- [ ] `QMT_ACCOUNT_ID` - QMT账户ID

### Redis缓存
- [ ] `REDIS_HOST` - Redis服务器地址
- [ ] `REDIS_PORT` - Redis端口号
- [ ] `REDIS_PASSWORD` - Redis密码
- [ ] `REDIS_DB` - Redis数据库编号

### 系统配置
- [ ] `ENVIRONMENT` - 运行环境标识
- [ ] `LOG_LEVEL` - 日志级别
- [ ] `DEBUG` - 调试模式开关
- [ ] `CORS_ORIGINS` - 跨域请求源

## 配置验证步骤

### 1. 环境变量存在性检查
```bash
# 使用配置管理工具检查
python scripts/config_manager.py check-env

# 手动检查关键变量
echo $APP_DB_PASSWORD
echo $SECRET_KEY
echo $AI_MODEL_API_KEY
```

### 2. 配置格式验证
```bash
# 验证所有配置
python scripts/config_manager.py validate

# 生成配置报告
python scripts/config_manager.py report
```

### 3. 连接测试
```bash
# 测试数据库连接
python scripts/config_manager.py test-connections --service database

# 测试Redis连接
python scripts/config_manager.py test-connections --service redis

# 测试所有连接
python scripts/config_manager.py test-connections --service all
```

## 安全检查要点

### 密钥强度验证
- [ ] SECRET_KEY长度至少32字符
- [ ] 密码包含大小写字母、数字、特殊字符
- [ ] API密钥格式正确
- [ ] 敏感信息未硬编码在代码中

### 权限检查
- [ ] .env文件权限设置为600
- [ ] 配置文件目录权限正确
- [ ] 敏感文件不在版本控制中

### 网络安全
- [ ] 数据库连接使用SSL
- [ ] API请求使用HTTPS
- [ ] CORS配置正确

## 常见问题排查

### 环境变量未生效
- [ ] 检查.env文件位置是否正确
- [ ] 确认环境变量名称拼写正确
- [ ] 验证应用是否重新启动
- [ ] 检查环境变量优先级

### 配置加载失败
- [ ] 检查配置文件语法
- [ ] 验证文件编码格式
- [ ] 确认文件路径正确
- [ ] 检查文件权限

### 连接测试失败
- [ ] 验证服务器地址和端口
- [ ] 检查网络连接
- [ ] 确认认证信息正确
- [ ] 检查防火墙设置

## 部署前最终检查

### 生产环境准备
- [ ] 所有必需环境变量已配置
- [ ] 密钥强度符合安全要求
- [ ] 数据库连接测试通过
- [ ] AI模型API测试通过
- [ ] QMT接口连接测试通过
- [ ] 配置验证全部通过

### 备份和恢复
- [ ] 配置文件已备份
- [ ] 环境变量已记录
- [ ] 恢复流程已测试
- [ ] 回滚方案已准备

### 监控和告警
- [ ] 配置变更监控已启用
- [ ] 连接失败告警已配置
- [ ] 性能监控已设置
- [ ] 日志记录已启用

## 检查清单完成确认

- [ ] 所有必需环境变量已配置并验证
- [ ] 可选环境变量根据需要已配置
- [ ] 配置验证测试全部通过
- [ ] 安全检查全部完成
- [ ] 常见问题排查已执行
- [ ] 部署前检查全部完成

**检查人员**: ________________  
**检查日期**: ________________  
**审核人员**: ________________  
**审核日期**: ________________

---

## 相关文档

- [配置管理完整指南](./配置管理完整指南.md)
- [配置验证方法和标准](./配置验证方法和标准.md)
- [配置故障排除指南](./配置故障排除指南.md)
