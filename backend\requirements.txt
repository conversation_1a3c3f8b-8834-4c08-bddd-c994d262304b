# =============================================================================
# 海天AI纳斯达克交易系统 - Python依赖包
# 基于: 项目手册4.1节MVP版本技术栈配置
# 创建日期: 2025年7月13日
# 最后更新: 2025年7月30日 (前端安全漏洞修复后版本同步)
# 后端技术栈: FastAPI 0.116.1 + Python 3.13.2 + PostgreSQL 17.5
# 前端技术栈: Vue.js 3.5.x + TypeScript 5.7.x + Vite 6.0.x + Vitest 3.2.x
# =============================================================================

# =============================================================================
# Web框架和API
# =============================================================================
fastapi==0.116.1
uvicorn[standard]==0.32.1
pydantic==2.10.4
pydantic-settings==2.7.0

# =============================================================================
# 数据库相关
# =============================================================================
asyncpg==0.30.0
sqlalchemy[asyncio]==2.0.36
alembic==1.14.0
psycopg2-binary==2.9.10

# =============================================================================
# 认证和安全
# =============================================================================
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.17

# =============================================================================
# HTTP客户端和工具
# =============================================================================
httpx==0.28.1
requests==2.32.3
aiofiles==24.1.0

# =============================================================================
# 数据处理和分析
# =============================================================================
pandas==2.2.3
numpy==2.2.1

# =============================================================================
# 配置和环境
# =============================================================================
python-dotenv==1.0.1
pyyaml==6.0.2

# =============================================================================
# 日志和监控
# =============================================================================
loguru==0.7.3

# =============================================================================
# 时间处理
# =============================================================================
python-dateutil==2.9.0.post0

# =============================================================================
# 开发和测试工具
# =============================================================================
pytest==8.3.4
pytest-asyncio==0.24.0
pytest-cov==6.0.0
