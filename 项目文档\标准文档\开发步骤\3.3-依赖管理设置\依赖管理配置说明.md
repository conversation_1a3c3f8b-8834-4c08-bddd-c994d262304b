# 依赖管理配置说明

## 文档信息
- **创建日期**: 2025年7月13日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: 海天AI纳斯达克交易系统依赖管理配置
- **技术基础**: Poetry + npm + Vue.js 3.5.x + FastAPI 0.116.1 + Python 3.13.2

## 版本更新记录
- v1.0: 初始版本，完成Python和前端依赖管理配置说明

## 目录
1. [Python项目依赖配置说明](#1-python项目依赖配置说明)
2. [前端项目依赖配置说明](#2-前端项目依赖配置说明)
3. [依赖锁定文件管理方法](#3-依赖锁定文件管理方法)
4. [配置文件模板和示例](#4-配置文件模板和示例)

---

## 1. Python项目依赖配置说明

### 1.1 Poetry配置概述

**配置文件**: `backend/pyproject.toml`

Poetry是现代Python项目的依赖管理工具，提供依赖解析、虚拟环境管理和包发布功能。

**核心优势**:
- 自动依赖解析和冲突检测
- 语义化版本管理
- 虚拟环境自动创建和管理
- 锁定文件确保环境一致性

### 1.2 依赖分类说明

**生产依赖** (`[tool.poetry.dependencies]`):
```toml
# Web框架和服务器
fastapi = "0.116.1"              # 固定版本，确保稳定性
uvicorn = {extras = ["standard"], version = "^0.32.1"}

# 数据库和ORM
sqlalchemy = "^2.0.36"           # 兼容版本，允许小版本更新
alembic = "^1.14.0"
asyncpg = "^0.29.0"

# AI模型API客户端
openai = "^1.58.1"
anthropic = "^0.40.0"
```

**开发依赖** (`[tool.poetry.group.dev.dependencies]`):
```toml
# 测试框架
pytest = "^8.3.3"
pytest-asyncio = "^0.24.0"

# 代码质量工具
black = "^24.10.0"
isort = "^5.13.2"
flake8 = "^7.1.1"
```

### 1.3 版本约束说明

| 约束符号 | 含义 | 示例 | 说明 |
|---------|------|------|------|
| `=` | 精确版本 | `fastapi = "0.116.1"` | 关键组件固定版本 |
| `^` | 兼容版本 | `sqlalchemy = "^2.0.36"` | 允许小版本和补丁版本更新 |
| `~` | 近似版本 | `pytest = "~8.3.0"` | 仅允许补丁版本更新 |
| `>=` | 最小版本 | `python = "^3.13.2"` | 最低版本要求 |

### 1.4 工具配置集成

**代码格式化配置**:
```toml
[tool.black]
line-length = 88
target-version = ['py313']

[tool.isort]
profile = "black"
multi_line_output = 3
```

**测试配置**:
```toml
[tool.pytest.ini_options]
testpaths = ["tests"]
addopts = ["--cov=app", "--cov-report=html"]
```

## 2. 前端项目依赖配置说明

### 2.1 npm配置概述

**配置文件**: `frontend/package.json`

npm是Node.js生态系统的标准包管理器，支持语义化版本控制和依赖树管理。

**核心特性**:
- 扁平化依赖安装
- 语义化版本控制
- 脚本自动化执行
- 开发和生产依赖分离

### 2.2 依赖分类说明

**生产依赖** (`dependencies`):
```json
{
  "vue": "^3.5.13",              // Vue.js核心框架
  "vue-router": "^4.5.0",        // 路由管理
  "pinia": "^2.3.0",             // 状态管理
  "element-plus": "^2.9.1",      // UI组件库
  "axios": "^1.7.9",             // HTTP客户端
  "echarts": "^5.6.0"            // 图表库
}
```

**开发依赖** (`devDependencies`):
```json
{
  "typescript": "^5.7.2",        // TypeScript编译器
  "vite": "^6.0.5",              // 构建工具
  "eslint": "^9.17.0",           // 代码检查
  "prettier": "^3.4.2",          // 代码格式化
  "vitest": "^2.1.8"             // 测试框架
}
```

### 2.3 脚本配置说明

**开发脚本**:
```json
{
  "dev": "vite",                 // 开发服务器
  "build": "vue-tsc && vite build", // 生产构建
  "preview": "vite preview",     // 预览构建结果
  "type-check": "vue-tsc --noEmit" // 类型检查
}
```

**质量控制脚本**:
```json
{
  "lint": "eslint . --ext .vue,.ts --fix", // 代码检查和修复
  "format": "prettier --write src/",       // 代码格式化
  "test": "vitest",                        // 运行测试
  "test:coverage": "vitest --coverage"     // 测试覆盖率
}
```

### 2.4 引擎和环境要求

```json
{
  "engines": {
    "node": ">=18.0.0",          // Node.js最低版本
    "npm": ">=8.0.0"             // npm最低版本
  },
  "browserslist": [
    "> 1%",                      // 市场份额大于1%的浏览器
    "last 2 versions",           // 最新两个版本
    "not dead",                  // 仍在维护的浏览器
    "not ie 11"                  // 排除IE11
  ]
}
```

## 3. 依赖锁定文件管理方法

### 3.1 Python锁定文件 (poetry.lock)

**文件作用**:
- 记录精确的依赖版本和哈希值
- 确保团队成员使用相同的依赖版本
- 提供可重现的构建环境

**管理原则**:
```bash
# 安装依赖时自动生成/更新
poetry install

# 更新锁定文件
poetry update

# 仅更新特定包
poetry update fastapi

# 导出requirements.txt（兼容性）
poetry export -f requirements.txt --output requirements.txt
```

**版本控制**:
- ✅ **提交到版本控制**: poetry.lock必须提交
- ✅ **团队同步**: 确保所有成员使用相同版本
- ❌ **手动编辑**: 不要手动修改锁定文件

### 3.2 前端锁定文件 (package-lock.json)

**文件作用**:
- 锁定依赖树的精确版本
- 记录依赖的完整解析路径
- 确保安装的一致性和安全性

**管理原则**:
```bash
# 安装依赖时自动生成/更新
npm install

# 清理并重新安装
npm ci  # 生产环境推荐

# 更新依赖
npm update

# 审计安全漏洞
npm audit
npm audit fix
```

**最佳实践**:
- ✅ **使用npm ci**: 生产环境使用npm ci而非npm install
- ✅ **定期审计**: 定期运行npm audit检查安全漏洞
- ✅ **版本控制**: package-lock.json必须提交到版本控制

### 3.3 依赖同步策略

**团队协作流程**:
1. **拉取代码后**: 运行依赖安装命令
2. **添加新依赖**: 提交锁定文件变更
3. **依赖冲突**: 协调解决版本冲突
4. **定期更新**: 按计划更新依赖版本

**环境一致性检查**:
```bash
# Python环境检查
poetry check
poetry show --tree

# 前端环境检查
npm ls
npm outdated
```

## 4. 配置文件模板和示例

### 4.1 Python配置模板

**最小化pyproject.toml模板**:
```toml
[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "your-project"
version = "1.0.0"
description = "项目描述"
authors = ["Your Name <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.13.2"
fastapi = "0.116.1"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.3"
black = "^24.10.0"
```

### 4.2 前端配置模板

**最小化package.json模板**:
```json
{
  "name": "your-frontend-project",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vue-tsc && vite build"
  },
  "dependencies": {
    "vue": "^3.5.13",
    "vue-router": "^4.5.0"
  },
  "devDependencies": {
    "typescript": "^5.7.2",
    "vite": "^6.0.5",
    "@vitejs/plugin-vue": "^5.2.1"
  }
}
```

### 4.3 配置验证命令

**Python配置验证**:
```bash
# 验证配置文件语法
poetry check

# 显示依赖树
poetry show --tree

# 验证虚拟环境
poetry env info
```

**前端配置验证**:
```bash
# 验证package.json语法
npm config list

# 检查依赖状态
npm ls

# 验证构建配置
npm run build --dry-run
```

---

## 总结

本文档详细说明了海天AI纳斯达克交易系统的依赖管理配置，包括Python和前端项目的依赖配置、锁定文件管理和配置模板。遵循这些配置标准可以确保项目的依赖管理规范化、环境一致性和团队协作效率。
