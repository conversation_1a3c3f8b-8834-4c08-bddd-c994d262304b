# 海天AI纳斯达克交易系统 - API实现和配置文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025年7月31日
- **技术栈**: FastAPI 0.116.1 + Python 3.13.2
- **开发步骤**: 4.2 FastAPI核心架构

## 1. 核心模块实现

### 1.1 安全模块 (`core/security.py`)

#### 1.1.1 密码处理
```python
# 密码哈希和验证
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool
def get_password_hash(password: str) -> str
```

#### 1.1.2 JWT令牌管理
```python
# 令牌创建和验证
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str
def create_refresh_token(data: dict, expires_delta: Optional[timedelta] = None) -> str
def verify_token(token: str) -> dict
def decode_token(token: str) -> TokenData
```

#### 1.1.3 安全工具函数
```python
# API密钥和密码强度
def generate_api_key() -> str
def validate_api_key(api_key: str) -> bool
def check_password_strength(password: str) -> dict
```

### 1.2 异常处理模块 (`core/exceptions.py`)

#### 1.2.1 自定义异常类
```python
# 异常层次结构
AITradingException          # 基础异常
├── DatabaseException       # 数据库异常
├── AuthenticationException # 认证异常
├── AuthorizationException  # 授权异常
├── ValidationException     # 验证异常
├── BusinessLogicException  # 业务逻辑异常
├── ExternalServiceException # 外部服务异常
│   ├── AIModelException    # AI模型异常
│   └── QMTException        # QMT接口异常
└── TradingException        # 交易异常
    └── RiskControlException # 风险控制异常
```

#### 1.2.2 异常处理器
```python
# 全局异常处理器
async def ai_trading_exception_handler(request: Request, exc: AITradingException)
async def http_exception_handler(request: Request, exc: HTTPException)
async def validation_exception_handler(request: Request, exc: RequestValidationError)
async def general_exception_handler(request: Request, exc: Exception)
```

### 1.3 依赖注入模块 (`api/deps.py`)

#### 1.3.1 认证依赖
```python
# 认证链
def get_current_user_token(credentials: HTTPAuthorizationCredentials) -> str
def get_current_user(token: str) -> TokenData
def get_current_active_user(current_user: TokenData) -> TokenData
def require_scopes(required_scopes: list[str]) -> function
```

#### 1.3.2 数据库依赖
```python
# 数据库会话管理
def get_database_session() -> Generator[Session, None, None]
async def get_async_database_session() -> AsyncSession
```

#### 1.3.3 配置依赖
```python
# 配置对象注入
def get_settings() -> Settings
def get_security_settings() -> SecuritySettings
def get_trading_settings() -> TradingSettings
def get_ai_model_settings() -> AIModelSettings
```

## 2. 中间件实现

### 2.1 请求日志中间件
```python
class RequestLoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成请求ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # 记录请求开始
        start_time = time.time()
        logger.info(f"Request started: {request.method} {request.url.path}")
        
        # 处理请求
        response = await call_next(request)
        
        # 记录请求完成
        process_time = time.time() - start_time
        logger.info(f"Request completed: {request.method} {request.url.path}")
        
        # 添加响应头
        response.headers["X-Request-ID"] = request_id
        response.headers["X-Process-Time"] = f"{process_time:.4f}"
        
        return response
```

### 2.2 性能监控中间件
```python
class PerformanceMonitoringMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        
        # 慢请求告警
        if process_time > 5.0:
            logger.warning(f"Slow request detected: {request.method} {request.url.path}")
        
        return response
```

### 2.3 安全头中间件
```python
class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        return response
```

## 3. API端点实现

### 3.1 认证端点 (`api/v1/endpoints/auth.py`)

#### 3.1.1 用户登录
```python
@router.post("/login", response_model=Token)
async def login(user_credentials: UserLogin, db: Session, security_settings):
    # 验证用户凭据
    # 创建访问令牌和刷新令牌
    # 返回令牌信息
```

#### 3.1.2 令牌刷新
```python
@router.post("/refresh", response_model=Token)
async def refresh_token(token_data: TokenRefresh, security_settings):
    # 验证刷新令牌
    # 创建新的访问令牌
    # 返回新令牌
```

### 3.2 交易员管理端点 (`api/v1/endpoints/traders.py`)

#### 3.2.1 创建交易员
```python
@router.post("/", response_model=TraderResponse)
async def create_trader(
    trader_data: TraderCreate,
    current_user: TokenData = Depends(require_scopes(["write"])),
    db: Session,
    trading_settings
):
    # 验证AI模型支持
    # 验证风险等级
    # 验证持仓限制
    # 创建交易员记录
```

#### 3.2.2 更新交易员
```python
@router.put("/{trader_id}", response_model=TraderResponse)
async def update_trader(
    trader_id: int,
    trader_data: TraderUpdate,
    current_user: TokenData = Depends(require_scopes(["write"])),
    db: Session
):
    # 验证交易员存在
    # 验证权限
    # 更新配置
```

### 3.3 交易执行端点 (`api/v1/endpoints/trading.py`)

#### 3.3.1 手动下单
```python
@router.post("/orders", response_model=OrderResponse)
async def create_order(
    order_data: OrderCreate,
    current_user: TokenData = Depends(require_scopes(["write", "trading"])),
    db: Session,
    trading_settings
):
    # 验证交易标的
    # 验证订单参数
    # 验证交易权限
    # 提交订单
```

#### 3.3.2 获取持仓
```python
@router.get("/positions", response_model=List[PositionResponse])
async def get_positions(
    trader_id: Optional[int],
    current_user: TokenData,
    db: Session
):
    # 查询持仓数据
    # 应用筛选条件
    # 返回持仓列表
```

### 3.4 监控统计端点 (`api/v1/endpoints/monitoring.py`)

#### 3.4.1 系统状态
```python
@router.get("/system-status", response_model=SystemStatusResponse)
async def get_system_status(current_user: TokenData, db: Session):
    # 检查各组件状态
    # 收集系统指标
    # 返回状态信息
```

#### 3.4.2 性能指标
```python
@router.get("/performance-metrics", response_model=PerformanceMetricsResponse)
async def get_performance_metrics(
    current_user: TokenData = Depends(require_scopes(["read", "monitoring"])),
    db: Session
):
    # 收集性能指标
    # 计算统计数据
    # 返回指标信息
```

## 4. 配置管理

### 4.1 环境变量配置
```bash
# 基础配置
ENVIRONMENT=development
DEBUG=true
BACKEND_PORT=8001

# 数据库配置
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=ai_trading
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# 安全配置
SECRET_KEY=your-secret-key-here
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
```

### 4.2 配置文件支持
```yaml
# config/security.yaml
jwt:
  algorithm: HS256
  access_token_expire_minutes: 30
  refresh_token_expire_days: 7

cors:
  origins:
    - "http://localhost:3000"
    - "http://localhost:3001"
  
api:
  rate_limit: 100
  rate_limit_window: 60
```

### 4.3 配置优先级
1. 环境变量（最高优先级）
2. 配置文件
3. 默认值（最低优先级）

## 5. 应用启动配置

### 5.1 主应用配置 (`main.py`)
```python
# FastAPI应用实例
app = FastAPI(
    title="海天AI纳斯达克交易系统",
    description="AI驱动的纳斯达克实时量化交易系统API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    openapi_url="/api/v1/openapi.json"
)

# 中间件注册（按执行顺序）
app.add_middleware(SecurityHeadersMiddleware)
app.add_middleware(PerformanceMonitoringMiddleware)
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(CORSMiddleware, ...)

# 异常处理器注册
app.add_exception_handler(AITradingException, ai_trading_exception_handler)
app.add_exception_handler(StarletteHTTPException, http_exception_handler)
app.add_exception_handler(RequestValidationError, validation_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

# API路由注册
app.include_router(api_router, prefix="/api/v1")
```

### 5.2 启动事件配置
```python
@app.on_event("startup")
async def startup_event():
    logger.info("🚀 海天AI纳斯达克交易系统启动中...")
    logger.info(f"📊 环境: {settings.environment}")
    logger.info(f"🔗 数据库: {settings.database.host}:{settings.database.port}")
    logger.info("✅ 系统启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("🛑 海天AI纳斯达克交易系统正在关闭...")
    logger.info("✅ 系统已安全关闭")
```

## 6. 开发环境配置

### 6.1 开发服务器启动
```python
if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.backend_port,
        reload=settings.environment == "development",
        log_level="info",
        access_log=True
    )
```

### 6.2 调试配置
```python
# 开发环境启用调试模式
DEBUG = settings.environment == "development"

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

## 7. 部署配置

### 7.1 生产环境配置
```bash
# 生产环境变量
ENVIRONMENT=production
DEBUG=false
SECRET_KEY=production-secret-key
CORS_ORIGINS=https://yourdomain.com
```

### 7.2 Docker配置
```dockerfile
FROM python:3.13.2-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8001
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"]
```

## 8. 测试配置

### 8.1 测试环境设置
```python
# 测试配置
TEST_DATABASE_URL = "postgresql://test:test@localhost:5432/test_ai_trading"
TEST_SECRET_KEY = "test-secret-key"
```

### 8.2 测试客户端配置
```python
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)
```

## 9. 监控配置

### 9.1 日志配置
```python
# 结构化日志
logger.info("Request completed", extra={
    "request_id": request_id,
    "method": request.method,
    "path": request.url.path,
    "status_code": response.status_code,
    "process_time": f"{process_time:.4f}s"
})
```

### 9.2 性能监控
```python
# 慢请求告警
if process_time > 5.0:
    logger.warning(f"Slow request detected", extra={
        "process_time": f"{process_time:.4f}s",
        "threshold": "5.0s"
    })
```

## 10. 总结

本实现文档涵盖了：
- ✅ 核心模块的详细实现
- ✅ 中间件系统的具体代码
- ✅ API端点的实现逻辑
- ✅ 配置管理的完整方案
- ✅ 应用启动和生命周期管理
- ✅ 开发和部署环境配置
- ✅ 监控和日志配置

为开发团队提供了完整的实现参考和配置指南。
