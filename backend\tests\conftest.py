"""
海天AI纳斯达克交易系统 - 测试配置文件
基于: 项目手册4.1节MVP版本技术栈配置
创建日期: 2025年7月31日
技术栈: FastAPI 0.116.1 + Python 3.13.2 + pytest
功能: 测试环境配置、测试客户端、测试数据、测试依赖
"""

import pytest
import asyncio
from typing import Generator, AsyncGenerator
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import get_db
from app.core.settings import settings
from sqlmodel import SQLModel
from app.core.security import create_access_token, create_refresh_token
from app.api.deps import get_database_session

# =============================================================================
# 测试数据库配置
# =============================================================================

# 使用内存SQLite数据库进行测试
TEST_DATABASE_URL = "sqlite:///./test.db"

# 创建测试数据库引擎
test_engine = create_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

# 创建测试数据库会话
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)

# =============================================================================
# 测试数据库依赖覆盖
# =============================================================================

def override_get_db():
    """覆盖数据库依赖，使用测试数据库"""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

# 覆盖应用的数据库依赖
app.dependency_overrides[get_database_session] = override_get_db

# =============================================================================
# pytest配置
# =============================================================================



# =============================================================================
# 数据库fixtures
# =============================================================================

@pytest.fixture(scope="function")
def db_session() -> Generator[Session, None, None]:
    """创建测试数据库会话"""
    # 创建所有表
    SQLModel.metadata.create_all(bind=test_engine)

    # 创建会话
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        # 清理所有表
        SQLModel.metadata.drop_all(bind=test_engine)

# =============================================================================
# 测试客户端fixtures
# =============================================================================

@pytest.fixture(scope="function")
def client() -> Generator[TestClient, None, None]:
    """创建测试客户端"""
    with TestClient(app) as test_client:
        yield test_client

# =============================================================================
# 认证fixtures
# =============================================================================

@pytest.fixture
def test_user_data():
    """测试用户数据 - 普通用户，无特殊权限"""
    return {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "is_active": True,
        "scopes": []  # 无特殊权限，用于测试权限控制
    }

@pytest.fixture
def admin_user_data():
    """管理员用户数据"""
    return {
        "id": 2,
        "username": "admin_user", 
        "email": "<EMAIL>",
        "is_active": True,
        "scopes": ["read", "write", "admin", "trading", "monitoring", "risk"]
    }

@pytest.fixture
def test_access_token(test_user_data):
    """创建测试访问令牌"""
    return create_access_token(data={
        "sub": test_user_data["username"],
        "user_id": test_user_data["id"],
        "scopes": test_user_data["scopes"]
    })

@pytest.fixture
def admin_access_token(admin_user_data):
    """创建管理员访问令牌"""
    return create_access_token(data={
        "sub": admin_user_data["username"],
        "user_id": admin_user_data["id"],
        "scopes": admin_user_data["scopes"]
    })

@pytest.fixture
def test_refresh_token(test_user_data):
    """创建测试刷新令牌"""
    return create_refresh_token(data={
        "sub": test_user_data["username"],
        "user_id": test_user_data["id"]
    })

@pytest.fixture
def auth_headers(test_access_token):
    """创建认证头"""
    return {"Authorization": f"Bearer {test_access_token}"}

@pytest.fixture
def admin_auth_headers(admin_access_token):
    """创建管理员认证头"""
    return {"Authorization": f"Bearer {admin_access_token}"}

# =============================================================================
# 测试数据fixtures
# =============================================================================

@pytest.fixture
def sample_trader_data():
    """示例交易员数据"""
    return {
        "name": "测试AI交易员",
        "ai_model": "gpt-4",
        "risk_level": "medium",
        "max_position_size": 15000,
        "stop_loss_pct": 0.025,  # 2.5%
        "take_profit_pct": 0.06,  # 6.0%
        "is_active": True
    }

@pytest.fixture
def sample_order_data():
    """示例订单数据"""
    return {
        "symbol": "159509",
        "side": "buy",
        "order_type": "market",
        "quantity": 1000,
        "trader_id": 1
    }

@pytest.fixture
def sample_limit_order_data():
    """示例限价订单数据"""
    return {
        "symbol": "159509",
        "side": "buy", 
        "order_type": "limit",
        "quantity": 1000,
        "price": 1.25,
        "trader_id": 1
    }

# =============================================================================
# Mock数据fixtures
# =============================================================================

@pytest.fixture
def mock_user_database():
    """模拟用户数据库"""
    return {
        "test_user": {
            "id": 1,
            "username": "test_user",
            "email": "<EMAIL>",
            "hashed_password": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # secret
            "is_active": True,
            "scopes": ["read", "write"]
        },
        "admin_user": {
            "id": 2,
            "username": "admin_user",
            "email": "<EMAIL>", 
            "hashed_password": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # secret
            "is_active": True,
            "scopes": ["read", "write", "admin", "trading", "monitoring", "risk"]
        }
    }

@pytest.fixture
def mock_traders_database():
    """模拟交易员数据库"""
    return [
        {
            "id": 1,
            "name": "保守型AI交易员",
            "ai_model": "conservative_v1",
            "risk_level": "low",
            "max_position_size": 10000,
            "stop_loss_pct": 0.02,  # 2.0%
            "take_profit_pct": 0.05,  # 5.0%
            "is_active": True,
            "created_at": "2025-07-31T10:00:00Z",
            "updated_at": "2025-07-31T10:00:00Z"
        },
        {
            "id": 2,
            "name": "激进型AI交易员",
            "ai_model": "aggressive_v1",
            "risk_level": "high",
            "max_position_size": 20000,
            "stop_loss_pct": 0.03,  # 3.0%
            "take_profit_pct": 0.08,  # 8.0%
            "is_active": True,
            "created_at": "2025-07-31T10:00:00Z",
            "updated_at": "2025-07-31T10:00:00Z"
        }
    ]


@pytest.fixture
def mock_request():
    """模拟请求对象"""
    from unittest.mock import Mock
    request = Mock()
    request.url.path = "/test/path"
    request.method = "GET"
    request.headers.get.return_value = "test-request-id"
    return request

# =============================================================================
# 测试环境配置
# =============================================================================

@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch):
    """设置测试环境变量"""
    monkeypatch.setenv("ENVIRONMENT", "testing")
    monkeypatch.setenv("DATABASE_URL", TEST_DATABASE_URL)
    monkeypatch.setenv("SECRET_KEY", "test-secret-key-for-testing-only")
    monkeypatch.setenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30")
    monkeypatch.setenv("REFRESH_TOKEN_EXPIRE_DAYS", "7")

# =============================================================================
# 清理fixtures
# =============================================================================

@pytest.fixture(autouse=True)
def cleanup_after_test():
    """测试后清理"""
    yield
    # 这里可以添加测试后的清理逻辑
    pass

# =============================================================================
# 测试标记配置
# =============================================================================

def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 标记单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 标记集成测试"
    )
    config.addinivalue_line(
        "markers", "slow: 标记慢速测试"
    )
    config.addinivalue_line(
        "markers", "auth: 标记认证相关测试"
    )
    config.addinivalue_line(
        "markers", "trading: 标记交易相关测试"
    )
    config.addinivalue_line(
        "markers", "monitoring: 标记监控相关测试"
    )
