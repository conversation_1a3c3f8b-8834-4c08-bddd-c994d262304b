# 数据库运维文档

## 文档信息
- **创建日期**: 2025年7月16日
- **维护团队**: 海天AI量化交易开发团队
- **适用范围**: 海天AI纳斯达克交易系统数据库运维
- **技术基础**: PostgreSQL 17.5 + 高可用架构
- **运维环境**: 开发环境、测试环境、生产环境

---

## 1. 数据库安装和配置指南

### 1.1 生产环境安装配置

#### 1.1.1 系统要求
```
操作系统: Windows Server 2022 或 Ubuntu 22.04 LTS
CPU: 8核心以上
内存: 32GB以上
存储: SSD 1TB以上（数据）+ SSD 500GB（日志）
网络: 千兆网络
```

#### 1.1.2 PostgreSQL 17.5 生产配置
```sql
-- postgresql.conf 生产环境配置
# 内存配置
shared_buffers = 8GB                     # 25%的系统内存
effective_cache_size = 24GB              # 75%的系统内存
work_mem = 256MB                         # 工作内存
maintenance_work_mem = 2GB               # 维护工作内存
temp_buffers = 32MB                      # 临时缓冲区

# 检查点配置
checkpoint_completion_target = 0.9       # 检查点完成目标
checkpoint_timeout = 15min               # 检查点超时
max_wal_size = 4GB                       # 最大WAL大小
min_wal_size = 1GB                       # 最小WAL大小

# 连接配置
max_connections = 200                    # 最大连接数
superuser_reserved_connections = 3       # 超级用户保留连接

# 日志配置
logging_collector = on                   # 启用日志收集
log_directory = 'log'                    # 日志目录
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d                    # 日志轮转时间
log_rotation_size = 100MB                # 日志轮转大小
log_min_duration_statement = 1000        # 记录慢查询（1秒）
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '

# 性能配置
random_page_cost = 1.1                   # SSD随机页面成本
effective_io_concurrency = 200          # 有效IO并发
max_worker_processes = 8                 # 最大工作进程
max_parallel_workers_per_gather = 4     # 每个查询的并行工作进程
max_parallel_workers = 8                # 最大并行工作进程

# 自动清理配置
autovacuum = on                          # 启用自动清理
autovacuum_max_workers = 3               # 自动清理最大工作进程
autovacuum_naptime = 1min                # 自动清理间隔
```

#### 1.1.3 安全配置
```sql
-- pg_hba.conf 安全配置
# 本地连接
local   all             postgres                                peer
local   all             all                                     md5

# IPv4本地连接
host    all             all             127.0.0.1/32            md5
host    all             all             10.0.0.0/8              md5

# IPv6本地连接
host    all             all             ::1/128                 md5

# SSL连接
hostssl all             all             0.0.0.0/0               md5

# 复制连接
host    replication     replicator      10.0.0.0/8              md5
```

### 1.2 高可用架构配置

#### 1.2.1 主从复制配置
```sql
-- 主服务器配置
# postgresql.conf
wal_level = replica                      # WAL级别
max_wal_senders = 3                      # 最大WAL发送进程
wal_keep_size = 1GB                      # 保留WAL大小
archive_mode = on                        # 启用归档
archive_command = 'cp %p /archive/%f'    # 归档命令

-- 创建复制用户
CREATE USER replicator REPLICATION LOGIN CONNECTION LIMIT 3 PASSWORD 'repl_password_2025!';
```

#### 1.2.2 从服务器配置
```sql
-- recovery.conf（从服务器）
standby_mode = 'on'
primary_conninfo = 'host=primary_server port=5432 user=replicator password=repl_password_2025!'
restore_command = 'cp /archive/%f %p'
archive_cleanup_command = 'pg_archivecleanup /archive %r'
```

## 2. 备份和恢复策略

### 2.1 备份策略

#### 2.1.1 全量备份
```bash
#!/bin/bash
# 全量备份脚本 - full_backup.sh

BACKUP_DIR="/backup/postgresql"
DB_NAME="ai_nasdaq_trading"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="${BACKUP_DIR}/full_backup_${TIMESTAMP}.sql"

# 创建备份目录
mkdir -p ${BACKUP_DIR}

# 执行全量备份
pg_dump -h localhost -U postgres -d ${DB_NAME} \
    --verbose --format=custom --compress=9 \
    --file=${BACKUP_FILE}

# 验证备份文件
if [ $? -eq 0 ]; then
    echo "Full backup completed successfully: ${BACKUP_FILE}"
    
    # 清理7天前的备份
    find ${BACKUP_DIR} -name "full_backup_*.sql" -mtime +7 -delete
else
    echo "Full backup failed!"
    exit 1
fi
```

#### 2.1.2 增量备份
```bash
#!/bin/bash
# WAL归档备份脚本 - wal_backup.sh

WAL_ARCHIVE_DIR="/backup/wal_archive"
REMOTE_BACKUP_DIR="/remote_backup/wal"

# 同步WAL文件到远程备份
rsync -av --delete ${WAL_ARCHIVE_DIR}/ ${REMOTE_BACKUP_DIR}/

# 清理本地旧WAL文件（保留3天）
find ${WAL_ARCHIVE_DIR} -name "*.backup" -mtime +3 -delete
find ${WAL_ARCHIVE_DIR} -name "*" -mtime +3 -delete
```

#### 2.1.3 自动备份调度
```bash
# crontab配置
# 每天凌晨2点执行全量备份
0 2 * * * /scripts/full_backup.sh >> /logs/backup.log 2>&1

# 每小时执行WAL备份
0 * * * * /scripts/wal_backup.sh >> /logs/wal_backup.log 2>&1

# 每周日执行备份验证
0 3 * * 0 /scripts/backup_verify.sh >> /logs/backup_verify.log 2>&1
```

### 2.2 恢复策略

#### 2.2.1 完整恢复
```bash
#!/bin/bash
# 完整恢复脚本 - full_restore.sh

BACKUP_FILE=$1
DB_NAME="ai_nasdaq_trading"
RESTORE_DB_NAME="${DB_NAME}_restore"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    exit 1
fi

# 创建恢复数据库
createdb -h localhost -U postgres ${RESTORE_DB_NAME}

# 恢复数据
pg_restore -h localhost -U postgres -d ${RESTORE_DB_NAME} \
    --verbose --clean --if-exists ${BACKUP_FILE}

if [ $? -eq 0 ]; then
    echo "Database restored successfully to ${RESTORE_DB_NAME}"
else
    echo "Database restore failed!"
    exit 1
fi
```

#### 2.2.2 时间点恢复（PITR）
```bash
#!/bin/bash
# 时间点恢复脚本 - pitr_restore.sh

TARGET_TIME=$1
BACKUP_FILE=$2
WAL_ARCHIVE_DIR="/backup/wal_archive"

if [ -z "$TARGET_TIME" ] || [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <target_time> <backup_file>"
    echo "Example: $0 '2025-07-16 14:30:00' /backup/full_backup_20250716_020000.sql"
    exit 1
fi

# 停止PostgreSQL服务
systemctl stop postgresql

# 清理数据目录
rm -rf /var/lib/postgresql/17/main/*

# 恢复基础备份
pg_basebackup -h backup_server -D /var/lib/postgresql/17/main -U replicator -P -W

# 创建recovery.conf
cat > /var/lib/postgresql/17/main/recovery.conf << EOF
restore_command = 'cp ${WAL_ARCHIVE_DIR}/%f %p'
recovery_target_time = '${TARGET_TIME}'
recovery_target_action = 'promote'
EOF

# 启动PostgreSQL服务
systemctl start postgresql

echo "PITR restore initiated to time: ${TARGET_TIME}"
```

## 3. 性能监控和调优

### 3.1 性能监控指标

#### 3.1.1 关键性能指标
```sql
-- 数据库连接监控
SELECT 
    state,
    COUNT(*) as connection_count
FROM pg_stat_activity 
WHERE datname = 'ai_nasdaq_trading'
GROUP BY state;

-- 慢查询监控
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements 
WHERE mean_time > 1000  -- 超过1秒的查询
ORDER BY mean_time DESC 
LIMIT 10;

-- 表大小监控
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE idx_scan = 0  -- 未使用的索引
ORDER BY schemaname, tablename;
```

#### 3.1.2 系统资源监控
```bash
#!/bin/bash
# 系统监控脚本 - system_monitor.sh

# CPU使用率
echo "=== CPU Usage ==="
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1

# 内存使用率
echo "=== Memory Usage ==="
free -m | awk 'NR==2{printf "Memory Usage: %s/%sMB (%.2f%%)\n", $3,$2,$3*100/$2 }'

# 磁盘使用率
echo "=== Disk Usage ==="
df -h | awk '$NF=="/"{printf "Disk Usage: %d/%dGB (%s)\n", $3,$2,$5}'

# PostgreSQL进程状态
echo "=== PostgreSQL Processes ==="
ps aux | grep postgres | grep -v grep | wc -l

# 数据库连接数
echo "=== Database Connections ==="
psql -h localhost -U postgres -d ai_nasdaq_trading -c "SELECT COUNT(*) FROM pg_stat_activity;"
```

### 3.2 性能调优策略

#### 3.2.1 查询优化
```sql
-- 创建性能分析函数
CREATE OR REPLACE FUNCTION analyze_query_performance(query_text TEXT)
RETURNS TABLE(
    execution_plan TEXT,
    execution_time NUMERIC,
    recommendations TEXT
) AS $$
BEGIN
    -- 执行查询计划分析
    RETURN QUERY
    SELECT 
        'EXPLAIN ANALYZE: ' || query_text as execution_plan,
        0.0 as execution_time,
        'Use EXPLAIN ANALYZE to get detailed execution plan' as recommendations;
END;
$$ LANGUAGE plpgsql;

-- 索引建议查询
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE schemaname = 'public' 
    AND n_distinct > 100  -- 高选择性字段
    AND correlation < 0.1  -- 低相关性
ORDER BY n_distinct DESC;
```

#### 3.2.2 自动调优脚本
```bash
#!/bin/bash
# 自动调优脚本 - auto_tune.sh

DB_NAME="ai_nasdaq_trading"

echo "Starting automatic database tuning..."

# 更新表统计信息
echo "Updating table statistics..."
psql -h localhost -U postgres -d ${DB_NAME} -c "ANALYZE;"

# 重建索引（如果需要）
echo "Checking index health..."
psql -h localhost -U postgres -d ${DB_NAME} -c "
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexname)) as index_size
FROM pg_stat_user_indexes 
WHERE idx_scan < 100  -- 使用频率低的索引
ORDER BY pg_relation_size(indexname) DESC;
"

# 清理无用数据
echo "Running maintenance tasks..."
psql -h localhost -U postgres -d ${DB_NAME} -c "VACUUM ANALYZE;"

echo "Automatic tuning completed."
```

## 4. 故障排除和应急处理

### 4.1 常见故障处理

#### 4.1.1 连接问题
```bash
# 检查PostgreSQL服务状态
systemctl status postgresql

# 检查端口监听
netstat -tlnp | grep 5432

# 检查连接数
psql -h localhost -U postgres -c "SELECT COUNT(*) FROM pg_stat_activity;"

# 终止异常连接
psql -h localhost -U postgres -c "
SELECT pg_terminate_backend(pid) 
FROM pg_stat_activity 
WHERE state = 'idle in transaction' 
    AND state_change < now() - interval '1 hour';
"
```

#### 4.1.2 性能问题
```sql
-- 查找阻塞查询
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.DATABASE IS NOT DISTINCT FROM blocked_locks.DATABASE
    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.GRANTED;
```

#### 4.1.3 空间问题
```bash
#!/bin/bash
# 磁盘空间清理脚本 - cleanup_space.sh

DB_NAME="ai_nasdaq_trading"

echo "Checking disk space..."
df -h

echo "Cleaning up old log files..."
find /var/log/postgresql -name "*.log" -mtime +7 -delete

echo "Running VACUUM on large tables..."
psql -h localhost -U postgres -d ${DB_NAME} -c "
VACUUM FULL market_data;
VACUUM FULL system_logs;
VACUUM FULL audit_logs;
"

echo "Cleaning up old partitions..."
psql -h localhost -U postgres -d ${DB_NAME} -c "
DROP TABLE IF EXISTS market_data_2024_01;
DROP TABLE IF EXISTS system_logs_2024_01;
"

echo "Space cleanup completed."
```

### 4.2 应急响应流程

#### 4.2.1 数据库宕机应急处理
```bash
#!/bin/bash
# 应急恢复脚本 - emergency_recovery.sh

echo "=== Emergency Database Recovery ==="
echo "Step 1: Check PostgreSQL service status"
systemctl status postgresql

echo "Step 2: Attempt to start PostgreSQL"
systemctl start postgresql

if [ $? -ne 0 ]; then
    echo "Step 3: Check PostgreSQL logs"
    tail -n 50 /var/log/postgresql/postgresql-*.log
    
    echo "Step 4: Check data directory permissions"
    ls -la /var/lib/postgresql/17/main/
    
    echo "Step 5: Attempt recovery mode"
    # 在postgresql.conf中添加recovery配置
    echo "recovery_target_action = 'promote'" >> /etc/postgresql/17/main/postgresql.conf
    systemctl start postgresql
fi

echo "Step 6: Verify database connectivity"
psql -h localhost -U postgres -c "SELECT version();"

echo "Step 7: Check critical tables"
psql -h localhost -U postgres -d ai_nasdaq_trading -c "
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
LIMIT 5;
"

echo "Emergency recovery process completed."
```

---

## 总结

本数据库运维文档提供了海天AI纳斯达克交易系统数据库的完整运维指南，包括：

1. **安装配置**：生产环境的安装和优化配置
2. **高可用架构**：主从复制和故障转移配置
3. **备份恢复**：全量备份、增量备份和时间点恢复
4. **性能监控**：关键指标监控和自动调优
5. **故障处理**：常见问题排查和应急响应流程

通过遵循本文档的运维规范，可以确保数据库系统的稳定运行和高可用性。
