# 海天AI纳斯达克交易系统 - Bandit安全扫描配置
# 基于Python 3.13.2 + FastAPI 0.116.1技术栈
# 创建日期: 2025年7月13日

# 扫描的目标
targets:
  - backend/app
  - scripts

# 排除的路径
exclude_dirs:
  - backend/tests
  - backend/alembic
  - .venv
  - venv
  - env
  - .env
  - __pycache__
  - .git
  - node_modules
  - dist
  - build
  - logs
  - data
  - uploads
  - migrations
  - .pytest_cache
  - .mypy_cache
  - htmlcov

# 跳过的测试ID
skips:
  # B101: assert语句的使用（在开发环境中可能需要）
  # - B101
  
  # B601: shell注入（如果确认安全可以跳过）
  # - B601
  
  # B602: subprocess调用（如果确认安全可以跳过）
  # - B602

# 测试配置
tests:
  # 高严重性测试
  - B102  # exec的使用
  - B103  # 设置不安全的文件权限
  - B104  # 硬编码的绑定到所有接口
  - B105  # 硬编码的密码字符串
  - B106  # 硬编码的密码函数参数
  - B107  # 硬编码的密码默认参数
  - B108  # 临时文件的不安全使用
  - B110  # try/except: pass
  - B112  # try/except: continue
  - B201  # Flask调试模式
  - B301  # pickle的使用
  - B302  # marshal的使用
  - B303  # MD5哈希的使用
  - B304  # 不安全的密码学哈希
  - B305  # 不安全的密码学加密
  - B306  # mktemp的使用
  - B307  # eval的使用
  - B308  # mark_safe的使用
  - B309  # HTTPSConnection的使用
  - B310  # urllib.urlopen的使用
  - B311  # 随机数生成器的使用
  - B312  # telnetlib的使用
  - B313  # XML解析器的使用
  - B314  # XML解析器的使用
  - B315  # XML解析器的使用
  - B316  # XML解析器的使用
  - B317  # XML解析器的使用
  - B318  # XML解析器的使用
  - B319  # XML解析器的使用
  - B320  # XML解析器的使用
  - B321  # FTP的使用
  - B322  # input的使用
  - B323  # 不安全的随机数生成
  - B324  # hashlib.new的使用
  - B325  # tempfile.mktemp的使用
  - B401  # import telnetlib
  - B402  # import ftplib
  - B403  # import pickle
  - B404  # import subprocess
  - B405  # import xml
  - B406  # import xml
  - B407  # import xml
  - B408  # import xml
  - B409  # import xml
  - B410  # import xml
  - B411  # import xml
  - B412  # import telnetlib
  - B413  # import pycrypto
  - B501  # 请求验证
  - B502  # SSL证书验证
  - B503  # SSL版本
  - B504  # SSL默认上下文
  - B505  # 弱密码学密钥
  - B506  # YAML加载
  - B507  # SSH主机密钥验证
  - B601  # shell注入
  - B602  # subprocess shell注入
  - B603  # subprocess调用
  - B604  # 任何其他函数调用
  - B605  # 启动进程
  - B606  # 启动进程
  - B607  # 启动进程
  - B608  # SQL注入
  - B609  # Linux命令通配符注入
  - B610  # Django SQL注入
  - B611  # Django SQL注入
  - B701  # jinja2自动转义
  - B702  # Tornado自动转义
  - B703  # Django自动转义

# 严重性级别
severity:
  # 低严重性
  low:
    - B110  # try/except: pass
    - B112  # try/except: continue
    - B311  # 随机数生成器
    - B323  # 不安全的随机数生成
    - B324  # hashlib.new
    
  # 中等严重性  
  medium:
    - B101  # assert语句
    - B103  # 文件权限
    - B104  # 绑定到所有接口
    - B108  # 临时文件
    - B201  # Flask调试
    - B303  # MD5哈希
    - B304  # 不安全哈希
    - B305  # 不安全加密
    - B309  # HTTPSConnection
    - B310  # urllib.urlopen
    - B312  # telnetlib
    - B321  # FTP
    - B322  # input
    - B325  # tempfile.mktemp
    - B501  # 请求验证
    - B502  # SSL证书验证
    - B503  # SSL版本
    - B504  # SSL上下文
    - B506  # YAML加载
    - B507  # SSH验证
    - B603  # subprocess
    - B604  # 函数调用
    - B605  # 启动进程
    - B606  # 启动进程
    - B607  # 启动进程
    - B609  # 通配符注入
    
  # 高严重性
  high:
    - B102  # exec
    - B105  # 硬编码密码
    - B106  # 硬编码密码参数
    - B107  # 硬编码密码默认值
    - B301  # pickle
    - B302  # marshal
    - B306  # mktemp
    - B307  # eval
    - B308  # mark_safe
    - B313  # XML解析器
    - B314  # XML解析器
    - B315  # XML解析器
    - B316  # XML解析器
    - B317  # XML解析器
    - B318  # XML解析器
    - B319  # XML解析器
    - B320  # XML解析器
    - B505  # 弱密码学密钥
    - B601  # shell注入
    - B602  # subprocess shell注入
    - B608  # SQL注入
    - B610  # Django SQL注入
    - B611  # Django SQL注入
    - B701  # jinja2自动转义
    - B702  # Tornado自动转义
    - B703  # Django自动转义

# 置信度级别
confidence:
  # 低置信度
  low:
    - B110
    - B112
    - B311
    - B323
    
  # 中等置信度
  medium:
    - B101
    - B103
    - B104
    - B108
    - B201
    - B303
    - B304
    - B305
    - B309
    - B310
    - B312
    - B321
    - B322
    - B325
    - B501
    - B502
    - B503
    - B504
    - B506
    - B507
    - B603
    - B604
    - B605
    - B606
    - B607
    - B609
    
  # 高置信度
  high:
    - B102
    - B105
    - B106
    - B107
    - B301
    - B302
    - B306
    - B307
    - B308
    - B313
    - B314
    - B315
    - B316
    - B317
    - B318
    - B319
    - B320
    - B505
    - B601
    - B602
    - B608
    - B610
    - B611
    - B701
    - B702
    - B703

# 报告格式
format: json

# 输出详细信息
verbose: true

# 递归扫描
recursive: true

# 聚合重复的问题
aggregate: vuln

# 排序方式
sort: severity

# 最大行数限制
max_lines: 1000
